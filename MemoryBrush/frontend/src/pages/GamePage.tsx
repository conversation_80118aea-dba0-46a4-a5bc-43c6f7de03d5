import React, { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { message } from 'antd'

// 游戏组件
import LevelSelector from '@components/Game/LevelSelector'
import StageSelector from '@components/Game/StageSelector'
import FreeDrawing from '@components/Game/FreeDrawing'
import ShapeDrawing from '@components/Game/ShapeDrawing'
import TracePractice from '@components/TracePractice'

// 测试模式配置
import {
  isTestModeEnabled,
  getAllUnlockedLevels,
  getAllUnlockedStages
} from '@/config/testMode'

type GameState = 'level-select' | 'stage-select' | 'playing'
type GameMode = 'free-draw' | 'trace' | 'image-trace' | 'shape-straight' | 'shape-curved'

const GamePage: React.FC = () => {
  const navigate = useNavigate()
  const { level, stage } = useParams()

  const [gameState, setGameState] = useState<GameState>('level-select')
  const [currentLevel, setCurrentLevel] = useState<string>('')
  const [currentStage, setCurrentStage] = useState<string>('')
  const [gameMode, setGameMode] = useState<GameMode>('free-draw')
  const [unlockedLevels, setUnlockedLevels] = useState<string[]>(() => getAllUnlockedLevels())
  const [unlockedStages, setUnlockedStages] = useState<string[]>(() => getAllUnlockedStages())

  // 描线练习相关状态
  const [tracePracticeData, setTracePracticeData] = useState<{
    sessionId: string;
    guidePaths: any[];
    guideImage: string;
    artworkName: string;
    canvasSize?: { width: number; height: number };
    levelStage?: string;
  } | null>(null)
  const [isLoadingTrace, setIsLoadingTrace] = useState(false)





  // 开始描线练习
  const startTracePractice = React.useCallback(async (artworkName: string, levelStage?: string) => {
    setIsLoadingTrace(true)
    try {
      // 如果是trace类型，直接从文件系统加载
      if (artworkName === 'trace' && levelStage) {
        try {
          console.log(`尝试加载轨迹文件: /${levelStage}/trace.json`)
          const traceResponse = await fetch(`/${levelStage}/trace.json`)

          if (!traceResponse.ok) {
            throw new Error(`HTTP ${traceResponse.status}: 无法加载 ${levelStage}/trace.json`)
          }

          const contentType = traceResponse.headers.get('content-type')
          if (!contentType || !contentType.includes('application/json')) {
            throw new Error(`文件不是JSON格式: ${contentType}`)
          }

          const traceData = await traceResponse.json()
          console.log(`成功加载轨迹数据:`, traceData)

          // 转换数据格式以匹配TracePractice组件的期望
          const guidePaths = traceData.strokes?.map((stroke: any, index: number) => ({
            id: `path_${index}`,
            points: stroke.points || []
          })) || []

          console.log(`转换后的引导路径数量: ${guidePaths.length}`)

          setTracePracticeData({
            sessionId: `trace_${Date.now()}`,
            guidePaths: guidePaths,
            guideImage: traceData.backgroundImage || '',
            artworkName: 'trace',
            canvasSize: traceData.canvasSize || { width: 800, height: 600 },
            levelStage: levelStage
          })

          // 成功加载，直接返回，不调用后端API
          setIsLoadingTrace(false)
          console.log(`轨迹练习数据设置成功`)
          return
        } catch (fileError: any) {
          console.warn(`从文件加载失败: ${fileError?.message || fileError}`)
          console.warn(`文件路径: /${levelStage}/trace.json`)

          // 检查是否是因为文件不存在
          if (fileError?.message?.includes('404') || fileError?.message?.includes('HTTP 404')) {
            message.warning(`${levelStage} 目录下没有 trace.json 文件，该关卡暂不支持图像描线功能`)
            setIsLoadingTrace(false)
            return
          }

          console.warn(`尝试使用后端API作为备选方案`)
          // 如果是其他错误，继续使用后端API
        }
      }

      // 其他情况或文件加载失败时使用原来的API调用
      // 但是如果是trace类型，说明前面的文件加载已经处理过了，不应该到这里
      if (artworkName === 'trace') {
        throw new Error('trace类型轨迹文件加载失败，且没有后端支持')
      }

      const response = await fetch('/api/v1/games/trace/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          artwork_name: artworkName,
          difficulty: 'medium' // 使用默认难度，由后端配置控制
        }),
      })

      if (!response.ok) {
        throw new Error('启动描线练习失败')
      }

      const data = await response.json()
      if (data.success) {
        setTracePracticeData({
          sessionId: data.session_id,
          guidePaths: data.guide_paths,
          guideImage: data.guide_image,
          artworkName: data.artwork_name,
          canvasSize: data.canvas_size,
          levelStage: levelStage
        })
      } else {
        throw new Error(data.error || '启动描线练习失败')
      }
    } catch (error) {
      console.error('启动描线练习失败:', error)
      message.error('启动描线练习失败，请重试')
    } finally {
      setIsLoadingTrace(false)
    }
  }, [])

  // 启动描线练习的统一函数
  const initializeTracePractice = React.useCallback((stageId: string) => {
    // 直接使用stageId作为资源路径，因为现在stage id就是L1-1, L1-2等格式
    startTracePractice('trace', stageId)
  }, [startTracePractice])

  // 根据URL参数初始化游戏状态
  React.useEffect(() => {
    if (level && stage) {
      // 构建完整的stage ID
      // 如果stage已经是完整格式（如L1-2），直接使用
      // 如果stage是数字（如2），则与level组合成L1-2格式
      let fullStageId = stage;
      if (!stage.includes('-') && level) {
        fullStageId = `${level}-${stage}`;
      }

      // 有级别和阶段，直接进入游戏
      setCurrentLevel(level)
      setCurrentStage(fullStageId)
      setGameState('playing')

      // 根据stage设置游戏模式
      const levelData = levels.find(l => l.id === level)
      const stageData = levelData?.stages.find(s => s.id === fullStageId)

      if (stageData) {
        setGameMode(stageData.type as GameMode)

        // 如果是描线练习，启动练习
        if (stageData.type === 'image-trace') {
          initializeTracePractice(fullStageId)
        }
      }
    } else if (level) {
      // 只有级别，进入阶段选择
      setCurrentLevel(level)
      setGameState('stage-select')
    } else {
      // 没有参数，显示级别选择
      setGameState('level-select')
    }
  }, [level, stage, initializeTracePractice])

  // 级别数据定义
  const levels = [
    {
      id: 'level-1',
      title: '线条启蒙',
      description: '从基础的线条开始，培养手部协调能力',
      icon: '📈',
      difficulty: 1,
      stages: [
        {
          id: 'L1-1',
          title: '自由画线',
          description: '随意画线，创作属于您的艺术作品',
          type: 'free-draw' as const
        },
        {
          id: 'L1-2',
          title: '匀速直线',
          description: '跟随引导线条，提高绘画精准度',
          type: 'image-trace' as const
        },
        {
          id: 'L1-3',
          title: '匀速线条组合',
          description: '从真实图片中抽取线条进行描画练习',
          type: 'image-trace' as const
        },
        {
          id: 'L1-4',
          title: '曲线消除',
          description: '绘制三角形、正方形、矩形等直线图形',
          type: 'image-trace' as const
        },
        {
          id: 'L1-5',
          title: 'TBD',
          description: '绘制圆形、椭圆等曲线图形',
          type: 'shape-curved' as const
        }
      ]
    },
    {
      id: 'level-2',
      title: '立体空间',
      description: '从二维图形到三维立体，提升空间想象力',
      icon: '📦',
      difficulty: 2,
      stages: [
        {
          id: 'L2-1',
          title: '立体图形',
          description: '绘制锥形、立方体、圆柱体等三维图形',
          type: 'coming-soon' as const,
          comingSoon: true
        },
        {
          id: 'L2-2',
          title: '色彩填充',
          description: '为几何图形填色，学习色系和色谱搭配',
          type: 'coming-soon' as const,
          comingSoon: true
        },
        {
          id: 'L2-3',
          title: '质感画笔',
          description: '复杂曲线描边，选择不同质感的画笔填色',
          type: 'coming-soon' as const,
          comingSoon: true
        }
      ]
    },
    {
      id: 'level-3',
      title: '画面构图',
      description: '通过引导线条，完成完整的艺术画面',
      icon: '🖼️',
      difficulty: 3,
      stages: [
        {
          id: 'abstract-art',
          title: '抽象艺术',
          description: '用抽象线条和色块创作现代艺术作品',
          type: 'coming-soon' as const,
          comingSoon: true
        },
        {
          id: 'geometric-still',
          title: '几何静物',
          description: '绘制几何形状组成的静物画',
          type: 'coming-soon' as const,
          comingSoon: true
        },
        {
          id: 'life-objects',
          title: '生活物品',
          description: '描绘日常生活中的物品和场景',
          type: 'coming-soon' as const,
          comingSoon: true
        }
      ]
    },
    {
      id: 'level-4',
      title: '智能创作',
      description: '上传照片，AI辅助创作个性化艺术作品',
      icon: '📷',
      difficulty: 4,
      stages: [
        {
          id: 'photo-trace',
          title: '照片描边',
          description: '上传照片，提取轮廓进行描边练习',
          type: 'coming-soon' as const,
          comingSoon: true
        },
        {
          id: 'style-render',
          title: '风格渲染',
          description: '选择不同艺术风格，AI辅助渲染作品',
          type: 'coming-soon' as const,
          comingSoon: true
        },
        {
          id: 'ai-creation',
          title: 'AI协作',
          description: '与AI协作，创作独特的个人艺术作品',
          type: 'coming-soon' as const,
          comingSoon: true
        }
      ]
    }
  ]

  const handleSelectLevel = (levelId: string) => {
    setCurrentLevel(levelId)
    setGameState('stage-select')
    navigate(`/game/${levelId}`)
  }

  const handleSelectStage = React.useCallback((stageId: string) => {
    setCurrentStage(stageId)
    setGameState('playing')

    // 更新URL，这会触发useEffect重新执行，所以不需要在这里重复调用startTracePractice
    navigate(`/game/${currentLevel}/${stageId}`)
  }, [currentLevel, navigate])

  const handleBackToLevelSelect = () => {
    setGameState('level-select')
    setCurrentLevel('')
    setCurrentStage('')
    navigate('/game')
  }

  const handleBackToStageSelect = () => {
    setGameState('stage-select')
    setCurrentStage('')
    navigate(`/game/${currentLevel}`)
  }

  const handleResetProgress = () => {
    setUnlockedLevels(['level-1'])
    setUnlockedStages(['free-lines', 'L1-1'])
    localStorage.removeItem('memorybrush-unlocked-levels')
    localStorage.removeItem('memorybrush-unlocked-stages')
    message.success('游戏进度已重置')
  }





  // 处理描线路径完成
  const handleTracePathComplete = async (pathId: string, userPath: any[]) => {
    if (!tracePracticeData) return

    try {
      const response = await fetch('/api/v1/games/trace/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session_id: tracePracticeData.sessionId,
          user_paths: userPath,
          completed_path_id: pathId
        }),
      })

      if (!response.ok) {
        throw new Error('提交描线进度失败')
      }

      const data = await response.json()
      if (data.success) {
        console.log('路径完成:', pathId)
      }
    } catch (error) {
      console.error('提交描线进度失败:', error)
    }
  }

  // 处理描线练习全部完成
  const handleTraceAllComplete = () => {
    message.success('恭喜！描线练习完成！')

    // 使用通用的阶段解锁逻辑，按顺序解锁下一个阶段
    if (currentStage) {
      checkStageUnlock(currentStage)
    }

    // 返回阶段选择
    handleBackToStageSelect()
  }

  // 检查是否应该解锁新级别（只有完成当前级别所有练习后才解锁）
  const checkLevelUnlock = (completedLevel: string, updatedUnlockedStages?: string[]) => {
    // 测试模式下跳过解锁检查
    if (isTestModeEnabled()) {
      return
    }

    const currentLevelData = levels.find(l => l.id === completedLevel)
    if (!currentLevelData) return

    // 使用传入的更新状态或当前状态
    const currentUnlockedStages = updatedUnlockedStages || unlockedStages

    // 检查当前级别的所有非"即将推出"的阶段是否都已解锁
    const availableStages = currentLevelData.stages.filter(stage => !(stage as any).comingSoon)
    const allStagesUnlocked = availableStages.every(stage => currentUnlockedStages.includes(stage.id))

    if (allStagesUnlocked) {
      const levelOrder = ['level-1', 'level-2', 'level-3', 'level-4']
      const currentIndex = levelOrder.indexOf(completedLevel)

      if (currentIndex >= 0 && currentIndex < levelOrder.length - 1) {
        const nextLevel = levelOrder[currentIndex + 1]
        if (!unlockedLevels.includes(nextLevel)) {
          const newUnlockedLevels = [...unlockedLevels, nextLevel]
          setUnlockedLevels(newUnlockedLevels)
          // 保存到localStorage
          localStorage.setItem('memorybrush-unlocked-levels', JSON.stringify(newUnlockedLevels))
          message.success(`🎉 恭喜解锁新级别：${getNextLevelName(nextLevel)}！`)
        }
      }
    }
  }

  // 检查是否应该解锁新阶段
  const checkStageUnlock = (completedStage: string) => {
    // 测试模式下跳过解锁检查
    if (isTestModeEnabled()) {
      return
    }

    const currentLevelData = levels.find(l => l.id === currentLevel)
    if (!currentLevelData) return

    const stageOrder = currentLevelData.stages.map(s => s.id)
    const currentIndex = stageOrder.indexOf(completedStage)

    if (currentIndex >= 0 && currentIndex < stageOrder.length - 1) {
      const nextStage = stageOrder[currentIndex + 1]
      if (!unlockedStages.includes(nextStage)) {
        const newUnlockedStages = [...unlockedStages, nextStage]
        setUnlockedStages(newUnlockedStages)
        // 保存到localStorage
        localStorage.setItem('memorybrush-unlocked-stages', JSON.stringify(newUnlockedStages))

        const nextStageData = currentLevelData.stages.find(s => s.id === nextStage)
        if (nextStageData) {
          message.success(`🎉 恭喜解锁新练习：${nextStageData.title}！`)
        }

        // 检查是否应该解锁新级别（使用更新后的阶段状态）
        if (currentLevel) {
          checkLevelUnlock(currentLevel, newUnlockedStages)
        }
      }
    } else {
      // 如果是最后一个阶段，直接检查级别解锁
      if (currentLevel) {
        checkLevelUnlock(currentLevel)
      }
    }
  }

  const getNextLevelName = (levelId: string) => {
    const levelNames = {
      'level-1': '线条启蒙',
      'level-2': '立体空间',
      'level-3': '画面构图',
      'level-4': '智能创作'
    }
    return levelNames[levelId as keyof typeof levelNames] || '新级别'
  }

  const handleGameComplete = () => {
    message.success('恭喜完成练习！')

    // 检查阶段解锁（会自动检查级别解锁）
    if (currentStage) {
      checkStageUnlock(currentStage)
    }

    setTimeout(() => {
      handleBackToStageSelect()
    }, 2000)
  }

  const handleGameCompleteWithScore = (score: number) => {
    message.success(`恭喜完成！得分：${score}分`)

    // 检查阶段解锁（会自动检查级别解锁）
    if (currentStage) {
      checkStageUnlock(currentStage)
    }

    setTimeout(() => {
      handleBackToStageSelect()
    }, 2000)
  }

  // 渲染当前游戏组件
  const renderGameComponent = () => {
    switch (gameMode) {
      case 'free-draw':
        return (
          <FreeDrawing
            onBack={handleBackToStageSelect}
            onComplete={handleGameComplete}
          />
        )
      case 'trace':
        if (isLoadingTrace) {
          return (
            <div className="flex items-center justify-center min-h-screen">
              <div className="text-center">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
                <p className="mt-4 text-lg">正在准备描线练习...</p>
              </div>
            </div>
          )
        }

        if (tracePracticeData) {
          return (
            <div className="container mx-auto px-4 py-8">
              <div className="mb-4">
                <button
                  onClick={handleBackToStageSelect}
                  className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                >
                  ← 返回阶段选择
                </button>
              </div>
              <TracePractice
                guidePaths={tracePracticeData.guidePaths}
                guideImage={tracePracticeData.guideImage}
                originalCanvasSize={tracePracticeData.canvasSize}
                levelStage={tracePracticeData.levelStage}
                onPathComplete={handleTracePathComplete}
                onAllComplete={handleTraceAllComplete}
              />
            </div>
          )
        }

        return (
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
              <p className="text-lg">描线练习数据加载失败</p>
              <button
                onClick={handleBackToStageSelect}
                className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                返回阶段选择
              </button>
            </div>
          </div>
        )
      case 'image-trace':
        if (isLoadingTrace) {
          return (
            <div className="flex items-center justify-center min-h-screen">
              <div className="text-center">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
                <p className="mt-4 text-lg">正在从图片抽取线条...</p>
              </div>
            </div>
          )
        }

        if (tracePracticeData) {
          return (
            <div className="container mx-auto px-4 py-8">
              <div className="mb-4">
                <button
                  onClick={handleBackToStageSelect}
                  className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                >
                  ← 返回阶段选择
                </button>
              </div>
              <TracePractice
                guidePaths={tracePracticeData.guidePaths}
                guideImage={tracePracticeData.guideImage}
                originalCanvasSize={tracePracticeData.canvasSize}
                levelStage={tracePracticeData.levelStage}
                onPathComplete={handleTracePathComplete}
                onAllComplete={handleTraceAllComplete}
              />
            </div>
          )
        }

        return (
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
              <p className="text-lg">图像线条抽取失败</p>
              <button
                onClick={handleBackToStageSelect}
                className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                返回阶段选择
              </button>
            </div>
          </div>
        )
      case 'shape-straight':
        return (
          <ShapeDrawing
            onBack={handleBackToStageSelect}
            onComplete={handleGameCompleteWithScore}
            shapeType="straight"
          />
        )
      case 'shape-curved':
        return (
          <ShapeDrawing
            onBack={handleBackToStageSelect}
            onComplete={handleGameCompleteWithScore}
            shapeType="curved"
          />
        )
      default:
        return null
    }
  }

  const getCurrentLevel = () => {
    return levels.find(level => level.id === currentLevel)
  }

  return (
    <div className="min-h-screen pt-8" style={{ background: 'transparent' }}>
      {gameState === 'level-select' && (
        <LevelSelector
          onSelectLevel={handleSelectLevel}
          unlockedLevels={unlockedLevels}
          onResetProgress={handleResetProgress}
        />
      )}

      {gameState === 'stage-select' && getCurrentLevel() && (
        <StageSelector
          level={getCurrentLevel()!}
          onSelectStage={handleSelectStage}
          onBack={handleBackToLevelSelect}
          unlockedStages={unlockedStages}
        />
      )}

      {gameState === 'playing' && renderGameComponent()}
    </div>
  )
}

export default GamePage
