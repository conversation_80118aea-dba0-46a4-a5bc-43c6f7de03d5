import React, { useState, useRef, useEffect } from "react";

interface Point {
    x: number;
    y: number;
}

interface GuidePath {
    id: string;
    points: Point[];
}

interface TracePracticeProps {
    guidePaths: GuidePath[];
    guideImage: string;
    originalCanvasSize?: { width: number; height: number }; // 轨迹记录时的画布尺寸
    levelStage?: string; // 关卡阶段，如 'L1-3'，用于确定资源路径
    onPathComplete: (pathId: string, userPath: Point[]) => void;
    onAllComplete: () => void;
}

// 固定配置 - 极其宽松，几乎必过
const traceConfig = {
    accuracyThreshold: 0.70, // 10%准确度就通过，极其宽松
    tolerance: 20, // 200像素容差，极其宽松
    animationSpeed: 0.6, // 动画速度 - 调慢到0.6倍速度，让引导线更慢更清晰，适合老年人
    waitTimeAfterGuide: 1000, // 引导线完成后等待用户描绘的时间（毫秒），5秒后重新引导
    staticTraceDisplayTime: 3000, // 静态轨迹线显示时间（毫秒）- L1-4新增
};

// 画板尺寸配置
const canvasConfig = {
    circleDiameterOriginal: 1136, // 页面背景图中大圆的原始直径（像素）
    marginRatio: 0.05, // 画板与大圆边缘的间距比例（5%，减少边距让画板更大）
    minCanvasSize: 350, // 最小画板尺寸（像素）
    maxCanvasSize: 700, // 最大画板尺寸（像素，增大最大尺寸）
    // 不同屏幕尺寸下画板占视口高度的比例（增大所有比例）
    sizeRatios: {
        small: 0.45,  // 视口高度 <= 600px（从0.35增加到0.45）
        medium: 0.5,  // 视口高度 <= 900px（从0.4增加到0.5）
        large: 0.55   // 视口高度 > 900px（从0.45增加到0.55）
    }
};

const TracePractice: React.FC<TracePracticeProps> = ({ guidePaths, originalCanvasSize, levelStage = 'L1-3', onPathComplete }) => {
    // L1-4新增：判断是否为L1-4关卡
    const isL14Stage = levelStage === 'L1-4';
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const [currentPathIndex, setCurrentPathIndex] = useState(0);
    const [isDrawing, setIsDrawing] = useState(false);
    const [userPath, setUserPath] = useState<Point[]>([]);
    const [completedPaths, setCompletedPaths] = useState<string[]>([]);
    const [completedUserPaths, setCompletedUserPaths] = useState<{[pathId: string]: Point[]}>({});
    const [animationProgress, setAnimationProgress] = useState(0);

    // 画板动画状态
    const [canvasAnimationStage, setCanvasAnimationStage] = useState<'entering' | 'instruction' | 'active' | 'completing' | 'showing' | 'exiting'>('entering');
    const [canvasScale, setCanvasScale] = useState(0.1);
    const [canvasOpacity, setCanvasOpacity] = useState(1);
    const [rewardOpacity, setRewardOpacity] = useState(0);
    const [rewardScale, setRewardScale] = useState(1);

    // 打字效果状态
    const [displayedText, setDisplayedText] = useState('');
    const [showInstruction, setShowInstruction] = useState(false);

    // 结束鼓励语状态
    const [displayedEndText, setDisplayedEndText] = useState('');
    const [showEndMessage, setShowEndMessage] = useState(false);
    const [arrowImage, setArrowImage] = useState<HTMLImageElement | null>(null);
    const [canvasSize, setCanvasSize] = useState({ width: 800, height: 600 });
    const [bgImageSrc, setBgImageSrc] = useState<string>('');
    const [fullImageSrc, setFullImageSrc] = useState<string>('');

    // 重新引导相关状态
    const [reGuideTimeoutId, setReGuideTimeoutId] = useState<number | null>(null);

    // L1-4新增：引导阶段状态（只有L1-4使用）
    const [guideStage, setGuideStage] = useState<'guide-animation' | 'static-trace' | 'waiting-user'>('guide-animation');
    const [staticTraceTimeoutId, setStaticTraceTimeoutId] = useState<number | null>(null);



    // 加载箭头图片
    useEffect(() => {
        const img = new Image();
        img.onload = () => {
            setArrowImage(img);
        };
        img.src = `/${levelStage}/arrow.png`;
    }, [levelStage]);

    // 加载背景图片并计算画布尺寸
    useEffect(() => {
        // 重置背景图片状态
        setBgImageSrc('');
        setFullImageSrc('');

        const tryLoadImage = (src: string): Promise<HTMLImageElement> => {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = () => resolve(img);
                img.onerror = () => reject(new Error(`Failed to load ${src}`));
                img.src = src;
            });
        };

        const loadBackgroundImage = async () => {
            // 加载画布背景图片（bg.png/jpg）
            const bgFormats = ['png', 'jpg', 'jpeg'];
            let loadedImg: HTMLImageElement | null = null;
            let successSrc = '';

            for (const format of bgFormats) {
                try {
                    const src = `/${levelStage}/bg.${format}`;
                    loadedImg = await tryLoadImage(src);
                    successSrc = src;
                    break;
                } catch (error) {
                    // 继续尝试下一个格式
                }
            }

            if (loadedImg && successSrc) {
                setBgImageSrc(successSrc);

                // 计算考虑缩放的画板尺寸
                const calculateCanvasSize = () => {
                    const viewportHeight = window.innerHeight;

                    // 页面背景图使用 background-size: auto 100vh
                    // 背景图高度 = 视口高度，宽度按比例自适应

                    // 根据视口高度和大圆配置计算合适的画板大小
                    // 假设大圆在背景图中占据合理比例
                    let targetCanvasSize;

                    if (viewportHeight <= 600) {
                        targetCanvasSize = viewportHeight * canvasConfig.sizeRatios.small;
                    } else if (viewportHeight <= 900) {
                        targetCanvasSize = viewportHeight * canvasConfig.sizeRatios.medium;
                    } else {
                        targetCanvasSize = viewportHeight * canvasConfig.sizeRatios.large;
                    }

                    // 限制最大和最小尺寸
                    targetCanvasSize = Math.max(canvasConfig.minCanvasSize, targetCanvasSize);
                    targetCanvasSize = Math.min(canvasConfig.maxCanvasSize, targetCanvasSize);

                    return targetCanvasSize;
                };

                const maxCanvasSize = calculateCanvasSize();
                const aspectRatio = loadedImg.width / loadedImg.height;
                let canvasWidth, canvasHeight;

                // 根据宽高比确定最佳尺寸
                if (aspectRatio >= 1) {
                    // 横向或正方形图片：以宽度为准
                    canvasWidth = maxCanvasSize;
                    canvasHeight = maxCanvasSize / aspectRatio;

                    // 如果高度超出限制，以高度为准重新计算
                    if (canvasHeight > maxCanvasSize) {
                        canvasHeight = maxCanvasSize;
                        canvasWidth = maxCanvasSize * aspectRatio;
                    }
                } else {
                    // 纵向图片：以高度为准
                    canvasHeight = maxCanvasSize;
                    canvasWidth = maxCanvasSize * aspectRatio;

                    // 如果宽度超出限制，以宽度为准重新计算
                    if (canvasWidth > maxCanvasSize) {
                        canvasWidth = maxCanvasSize;
                        canvasHeight = maxCanvasSize / aspectRatio;
                    }
                }

                setCanvasSize({
                    width: Math.round(canvasWidth),
                    height: Math.round(canvasHeight)
                });
            } else {
                console.error('Failed to load canvas background image in any format');
                // 使用默认尺寸
                setCanvasSize({ width: 400, height: 300 });
            }

            // 页面背景图片现在由全局动态背景系统处理，不需要在这里加载

            // 加载完成奖励图片（full.jpg/png）
            const fullImageFormats = ['jpg', 'jpeg', 'png'];
            for (const format of fullImageFormats) {
                try {
                    const src = `/${levelStage}/full.${format}`;
                    await tryLoadImage(src);
                    setFullImageSrc(src);
                    break;
                } catch (error) {
                    // 继续尝试下一个格式
                }
            }
        };

        loadBackgroundImage();
    }, [levelStage]);

    // 处理轨迹数据 - 保持原始轨迹不做后处理
    const processNonOverlappingPaths = (paths: GuidePath[]): GuidePath[] => {
        // 直接返回原始路径，保持数组顺序
        return paths;
    };

    // 移除了所有轨迹后处理函数，保持原始轨迹数据

    // 移除了所有轨迹后处理函数，保持原始轨迹数据

    // 处理后的路径
    const [processedPaths, setProcessedPaths] = useState<GuidePath[]>([]);

    useEffect(() => {
        if (guidePaths.length > 0) {
            const processed = processNonOverlappingPaths(guidePaths);
            setProcessedPaths(processed);

            // 找到第一个有效的轨迹（至少2个点）
            let firstValidIndex = 0;
            while (firstValidIndex < processed.length) {
                const currentPath = processed[firstValidIndex];
                if (!currentPath || currentPath.points.length >= 2) {
                    // 找到有效轨迹或到达数组末尾
                    break;
                }
                // 将无效轨迹标记为已完成
                setCompletedPaths((prev) => [...prev, currentPath.id]);
                onPathComplete(currentPath.id, []);
                firstValidIndex++;
            }

            if (firstValidIndex !== currentPathIndex) {
                setCurrentPathIndex(firstValidIndex);
            }
        }
    }, [guidePaths]);

    const currentPath = processedPaths[currentPathIndex];

    // 初始化效果 - 确保有路径时开始动画，只显示第一条
    useEffect(() => {
        if (processedPaths.length > 0 && currentPath) {
            // 清理之前的重新引导定时器
            if (reGuideTimeoutId) {
                clearTimeout(reGuideTimeoutId);
                setReGuideTimeoutId(null);
            }
            // L1-4新增：清理静态轨迹定时器（只有L1-4需要）
            if (isL14Stage && staticTraceTimeoutId) {
                clearTimeout(staticTraceTimeoutId);
                setStaticTraceTimeoutId(null);
            }
            // 重置用户路径和引导阶段
            setUserPath([]);
            setAnimationProgress(0);
            if (isL14Stage) {
                setGuideStage('guide-animation'); // L1-4新增：重置引导阶段
            }
        }
    }, [processedPaths, currentPathIndex, currentPath]);

    // 画板进入动画
    useEffect(() => {
        if (canvasAnimationStage === 'entering') {
            const startTime = Date.now();
            const duration = 3000; // 3秒进入动画

            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // 缓动函数：从慢到快再到慢
                const easeInOutCubic = (t: number) => {
                    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
                };

                const easedProgress = easeInOutCubic(progress);
                const scale = 0.1 + (1 - 0.1) * easedProgress;
                setCanvasScale(scale);

                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    setCanvasAnimationStage('instruction');
                }
            };

            requestAnimationFrame(animate);
        }
    }, [canvasAnimationStage]);

    // 指导文字打字效果
    useEffect(() => {
        if (canvasAnimationStage === 'instruction') {
            const instructionText = 'Let’s create a beautiful painting together. Try to follow the yellow lines closely and gently trace along with the brush. You’ve got this—take your time.';
            // 动态加载对应关卡的引导文本
            const loadAndDisplayInstruction = async () => {
                let finalInstructionText = instructionText; // 使用默认文本作为后备

                try {
                    // 尝试从对应的关卡目录加载 start.txt
                    const response = await fetch(`/${levelStage}/start.txt`);
                    if (response.ok) {
                        const loadedText = await response.text();
                        if (loadedText.trim()) {
                            finalInstructionText = loadedText.trim();
                        }
                    }
                } catch (error) {
                    console.log(`未找到 ${levelStage}/start.txt，使用默认引导文本`);
                }

                // 如果没有任何引导文本，直接开始游戏
                if (!finalInstructionText) {
                    setShowInstruction(false);
                    setCanvasAnimationStage('active');
                    return;
                }

                setShowInstruction(true);
                setDisplayedText('');

                let currentIndex = 0;
                const typingSpeed = 100; // 每个字符100毫秒

                const typeNextCharacter = () => {
                    if (currentIndex < finalInstructionText.length) {
                        setDisplayedText(finalInstructionText.slice(0, currentIndex + 1));
                        currentIndex++;
                        setTimeout(typeNextCharacter, typingSpeed);
                    } else {
                        // 打字完成，停留2秒后开始引导
                        setTimeout(() => {
                            setShowInstruction(false);
                            setCanvasAnimationStage('active');
                        }, 2000);
                    }
                };

                // 开始打字效果
                setTimeout(typeNextCharacter, 500); // 0.5秒延迟后开始打字
            };

            loadAndDisplayInstruction();
        }
    }, [canvasAnimationStage, levelStage]);

    // 结束鼓励语打字效果
    useEffect(() => {
        if (canvasAnimationStage === 'completing') {
            // 动态加载对应关卡的结束鼓励语
            const loadAndDisplayEndMessage = async () => {
                let endText = '';

                try {
                    // 尝试从对应的关卡目录加载 end.txt
                    const response = await fetch(`/${levelStage}/end.txt`);
                    if (response.ok) {
                        const loadedText = await response.text();
                        if (loadedText.trim()) {
                            endText = loadedText.trim();
                        }
                    }
                } catch (error) {
                    console.log(`未找到 ${levelStage}/end.txt，跳过结束鼓励语`);
                }

                // 如果没有找到结束鼓励语，不显示
                if (!endText) {
                    return;
                }

                // 延迟1秒后开始显示结束鼓励语
                setTimeout(() => {
                    setShowEndMessage(true);
                    setDisplayedEndText('');

                    let currentIndex = 0;
                    const typingSpeed = 80; // 稍快一些的打字速度

                    const typeNextCharacter = () => {
                        if (currentIndex < endText.length) {
                            setDisplayedEndText(endText.slice(0, currentIndex + 1));
                            currentIndex++;
                            setTimeout(typeNextCharacter, typingSpeed);
                        } else {
                            // 打字完成，停留3秒后隐藏
                            setTimeout(() => {
                                setShowEndMessage(false);
                                setDisplayedEndText('');
                            }, 3000);
                        }
                    };

                    // 开始打字效果
                    setTimeout(typeNextCharacter, 300);
                }, 2000); // 延迟1秒开始
            };

            loadAndDisplayEndMessage();
        }
    }, [canvasAnimationStage, levelStage]);

    // 完成动画：画板消失，原图显示
    useEffect(() => {
        if (canvasAnimationStage === 'completing') {
            const startTime = Date.now();
            const duration = 2000; // 2秒完成动画

            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // 画板逐渐消失
                setCanvasOpacity(1 - progress);
                // 原图逐渐显示
                setRewardOpacity(progress);

                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    setCanvasAnimationStage('showing');
                }
            };

            requestAnimationFrame(animate);
        }
    }, [canvasAnimationStage]);

    // 展示阶段：原图完整显示并停留
    useEffect(() => {
        if (canvasAnimationStage === 'showing') {
            if (isL14Stage) {
                // L1-4新增：full图片永久停留，不再消失
                // 不设置定时器，让图片一直显示
                return;
            } else {
                // 其他关卡：保持原来的行为，停留3秒后退出
                const timer = setTimeout(() => {
                    setCanvasAnimationStage('exiting');
                }, 3000); // 停留3秒

                return () => clearTimeout(timer);
            }
        }
    }, [canvasAnimationStage, isL14Stage]);

    // 退出动画：原图缩小并慢慢消失，画布快速消失，营造挂画的感觉
    useEffect(() => {
        if (canvasAnimationStage === 'exiting') {
            // L1-4新增：跳过退出动画，因为full图片要永久停留
            if (isL14Stage) {
                return;
            }

            const startTime = Date.now();
            const duration = 3000; // 3秒退出动画

            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // 缓动函数：平滑缩小
                const easeInOutQuad = (t: number) => {
                    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
                };
                const easedProgress = easeInOutQuad(progress);

                // 原图缩小（从100%到15%，保持较小但可见的大小）
                setRewardScale(1 - easedProgress * 0.85); // 缩小到15%

                // 原图在前90%的时间内保持完全可见，最后10%时间才开始消失
                // 这样营造出画作被挂在墙上的感觉
                const rewardFadeStartPoint = 0.9;
                if (progress > rewardFadeStartPoint) {
                    const fadeProgress = (progress - rewardFadeStartPoint) / (1 - rewardFadeStartPoint);
                    setRewardOpacity(1 - fadeProgress);
                } else {
                    setRewardOpacity(1);
                }

                // 画布在前30%的时间内快速消失，让原图成为主角
                const canvasFadeStartPoint = 0.3;
                if (progress > canvasFadeStartPoint) {
                    setCanvasOpacity(0); // 画布完全消失
                } else {
                    const canvasFadeProgress = progress / canvasFadeStartPoint;
                    setCanvasOpacity(1 - canvasFadeProgress);
                }

                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    // 动画完成，原图已经"挂在墙上"
                    // 可以在这里添加其他逻辑，比如重置到初始状态等
                }
            };

            requestAnimationFrame(animate);
        }
    }, [canvasAnimationStage, isL14Stage]);

    // 防止页面滚动和处理触摸事件
    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const preventDefault = (e: Event) => {
            if ((e.target as Element)?.closest("canvas")) {
                e.preventDefault();
            }
        };

        // 处理触摸开始事件
        const handleTouchStart = (e: TouchEvent) => {
            // 实时获取最新状态，避免闭包问题
            const currentGuideStage = document.querySelector('[data-guide-stage]')?.getAttribute('data-guide-stage') as string || guideStage;

            e.preventDefault();
            e.stopPropagation();

            // L1-4新增：在引导阶段和静态轨迹阶段不允许用户描绘
            if (isL14Stage && (currentGuideStage === 'guide-animation' || currentGuideStage === 'static-trace')) {
                return;
            }

            // 用户开始描绘，只清除未来的重新引导定时器，不影响当前引导动画
            if (reGuideTimeoutId) {
                clearTimeout(reGuideTimeoutId);
                setReGuideTimeoutId(null);
            }

            setIsDrawing(true);
            const touch = e.touches[0];
            const rect = canvas.getBoundingClientRect();
            const scaleX = canvas.width / rect.width;
            const scaleY = canvas.height / rect.height;
            const pos = {
                x: (touch.clientX - rect.left) * scaleX,
                y: (touch.clientY - rect.top) * scaleY,
            };
            setUserPath([pos]);
        };

        // 处理触摸移动事件
        const handleTouchMove = (e: TouchEvent) => {
            e.preventDefault();
            e.stopPropagation();

            // L1-4新增：在引导阶段和静态轨迹阶段不允许用户描绘
            const currentGuideStage = document.querySelector('[data-guide-stage]')?.getAttribute('data-guide-stage') as string || guideStage;
            if (isL14Stage && (currentGuideStage === 'guide-animation' || currentGuideStage === 'static-trace')) {
                return;
            }

            if (!isDrawing) return;

            const touch = e.touches[0];
            const rect = canvas.getBoundingClientRect();
            const scaleX = canvas.width / rect.width;
            const scaleY = canvas.height / rect.height;
            const pos = {
                x: (touch.clientX - rect.left) * scaleX,
                y: (touch.clientY - rect.top) * scaleY,
            };
            const newPath = [...userPath, pos];
            setUserPath(newPath);
        };

        // 处理触摸结束事件
        const handleTouchEnd = (e: TouchEvent) => {
            e.preventDefault();
            e.stopPropagation();
            handleMouseUp();
        };

        // 添加非被动事件监听器
        canvas.addEventListener("touchstart", handleTouchStart, { passive: false });
        canvas.addEventListener("touchmove", handleTouchMove, { passive: false });
        canvas.addEventListener("touchend", handleTouchEnd, { passive: false });

        // 全局防止滚动
        document.addEventListener("touchmove", preventDefault, { passive: false });
        document.addEventListener("wheel", preventDefault, { passive: false });

        return () => {
            canvas.removeEventListener("touchstart", handleTouchStart);
            canvas.removeEventListener("touchmove", handleTouchMove);
            canvas.removeEventListener("touchend", handleTouchEnd);
            document.removeEventListener("touchmove", preventDefault);
            document.removeEventListener("wheel", preventDefault);
        };
    }, [isDrawing, userPath, reGuideTimeoutId, guideStage, isL14Stage]); // L1-4新增：添加guideStage和isL14Stage依赖

    // 清理定时器的effect
    useEffect(() => {
        return () => {
            if (reGuideTimeoutId) {
                clearTimeout(reGuideTimeoutId);
            }
            // L1-4新增：清理静态轨迹定时器（只有L1-4需要）
            if (isL14Stage && staticTraceTimeoutId) {
                clearTimeout(staticTraceTimeoutId);
            }
        };
    }, [reGuideTimeoutId, staticTraceTimeoutId, isL14Stage]);

    // 引导动画效果 - L1-4使用新的三阶段流程，其他关卡使用原来的流程
    useEffect(() => {
        if (!currentPath || canvasAnimationStage !== 'active') return;

        let animationId: number;
        let isRunning = true;

        if (isL14Stage) {
            // L1-4新增：三阶段流程
            const runGuideAnimation = () => {
                if (!isRunning || guideStage !== 'guide-animation') return;

                // 根据路径复杂度调整动画时长
                const pointCount = currentPath.points.length;
                const baseDuration = 3000;
                const extraDuration = Math.max(0, (pointCount - 10) * 150);
                const duration = (baseDuration + extraDuration) / traceConfig.animationSpeed;

                const startTime = Date.now();

                const animate = () => {
                    if (!isRunning || guideStage !== 'guide-animation') return;

                    const now = Date.now();
                    const elapsed = now - startTime;
                    let rawProgress = Math.min(elapsed / duration, 1);

                    // 缓动效果
                    const easeInOutQuad = (t: number): number => {
                        return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
                    };

                    const progress = easeInOutQuad(rawProgress);
                    setAnimationProgress(progress);

                    if (rawProgress < 1) {
                        animationId = requestAnimationFrame(animate);
                    } else {
                        // 引导动画完成，进入静态轨迹阶段
                        if (isRunning && currentPath && !completedPaths.includes(currentPath.id)) {
                            setGuideStage('static-trace');
                            setAnimationProgress(1); // 保持完整显示
                            // 定时器在独立的useEffect中处理
                        }
                    }
                };

                animationId = requestAnimationFrame(animate);
            };

            // 只在引导动画阶段开始动画
            if (guideStage === 'guide-animation') {
                runGuideAnimation();
            }
        } else {
            // 其他关卡：保持原来的循环引导流程
            const runAnimation = () => {
                if (!isRunning) return;

                // 根据路径复杂度调整动画时长
                const pointCount = currentPath.points.length;
                const baseDuration = 3000;
                const extraDuration = Math.max(0, (pointCount - 10) * 150);
                const duration = (baseDuration + extraDuration) / traceConfig.animationSpeed;

                const startTime = Date.now();

                const animate = () => {
                    if (!isRunning) return;

                    const now = Date.now();
                    const elapsed = now - startTime;
                    let rawProgress = Math.min(elapsed / duration, 1);

                    // 缓动效果
                    const easeInOutQuad = (t: number): number => {
                        return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
                    };

                    const progress = easeInOutQuad(rawProgress);
                    setAnimationProgress(progress);

                    if (rawProgress < 1) {
                        animationId = requestAnimationFrame(animate);
                    } else {
                        // 动画完成，检查是否需要继续
                        if (isRunning && currentPath && !completedPaths.includes(currentPath.id)) {
                            // 短暂延迟后重新开始
                            setTimeout(() => {
                                if (isRunning) {
                                    setAnimationProgress(0);
                                    runAnimation();
                                }
                            }, 500);
                        }
                    }
                };

                animationId = requestAnimationFrame(animate);
            };

            // 开始动画
            runAnimation();
        }

        // 清理函数
        return () => {
            isRunning = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
        };
    }, [currentPathIndex, currentPath, canvasAnimationStage, guideStage, isL14Stage]);

    // L1-4新增：独立处理静态轨迹阶段的转换
    useEffect(() => {
        if (!isL14Stage || !currentPath || canvasAnimationStage !== 'active') return;

        if (guideStage === 'static-trace') {
            const timeoutId = setTimeout(() => {
                if (!completedPaths.includes(currentPath.id)) {
                    setGuideStage('waiting-user');
                    setAnimationProgress(0); // 确保隐藏所有引导线
                }
            }, traceConfig.staticTraceDisplayTime);

            setStaticTraceTimeoutId(timeoutId);

            return () => {
                clearTimeout(timeoutId);
            };
        }
    }, [guideStage, currentPath, isL14Stage, canvasAnimationStage, completedPaths]);

    // 确保Canvas尺寸正确设置
    useEffect(() => {
        const canvas = canvasRef.current;
        if (canvas) {
            // 设置Canvas的实际尺寸
            canvas.width = canvasSize.width;
            canvas.height = canvasSize.height;

            // 设置Canvas的显示尺寸
            canvas.style.width = `${canvasSize.width}px`;
            canvas.style.height = `${canvasSize.height}px`;
        }
    }, [canvasSize]);

    // 绘制引导线条和动画
    useEffect(() => {
        const canvas = canvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext("2d");
        if (!ctx) return;

        // 清空画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 绘制引导线
        drawCurrentGuide();

        function drawCurrentGuide() {
            if (!currentPath || !ctx) {
                return;
            }

            // 绘制已完成的路径（用户实际描绘的黑色路径）
            completedPaths.forEach((pathId) => {
                const userDrawnPath = completedUserPaths[pathId];
                if (userDrawnPath && userDrawnPath.length > 0) {
                    drawUserPath(ctx, userDrawnPath);
                }
            });

            // 根据关卡类型绘制引导内容
            if (currentPath) {
                if (isL14Stage) {
                    // L1-4新增：根据引导阶段绘制不同内容
                    if (guideStage === 'guide-animation') {
                        // 阶段1：显示黄色引导动画
                        drawAnimatedGuidePath(ctx, currentPath);
                    } else if (guideStage === 'static-trace') {
                        // 阶段2：显示完整的静态轨迹线
                        drawStaticTracePath(ctx, currentPath);
                    }
                    // 阶段3：waiting-user 不显示任何引导线
                } else {
                    // 其他关卡：只显示黄色引导动画
                    drawAnimatedGuidePath(ctx, currentPath);
                }
            }

            // 绘制用户当前绘制的路径
            if (userPath.length > 0) {
                drawUserPath(ctx, userPath);
            }
        }
    }, [currentPath, userPath, completedPaths, completedUserPaths, animationProgress, canvasSize, isL14Stage ? guideStage : null]); // L1-4新增：只有L1-4才依赖guideStage

    // 绘制箭头图片的辅助函数
    const drawArrow = (
        ctx: CanvasRenderingContext2D,
        fromX: number,
        fromY: number,
        toX: number,
        toY: number,
        color: string,
        size: number = 15
    ) => {
        const dx = toX - fromX;
        const dy = toY - fromY;

        if (!arrowImage) {
            // 如果图片还没加载完成，绘制传统箭头作为备用
            const angle = Math.atan2(dy, dx);
            ctx.fillStyle = color;
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;

            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - size * Math.cos(angle - Math.PI / 5), toY - size * Math.sin(angle - Math.PI / 5));
            ctx.lineTo(toX - size * Math.cos(angle + Math.PI / 5), toY - size * Math.sin(angle + Math.PI / 5));
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
            return;
        }

        // 计算箭头方向角度
        const angle = Math.atan2(dy, dx);

        // 图片大小（可以根据size参数调整）
        const imageSize = size * 2; // 图片比原来的箭头大一些

        // 保存当前画布状态
        ctx.save();

        // 移动到箭头位置
        ctx.translate(toX, toY);

        // 旋转画布以匹配箭头方向
        ctx.rotate(angle);

        // 绘制图片（以图片中心对齐）
        ctx.drawImage(
            arrowImage,
            -imageSize / 2, // x偏移（图片中心对齐）
            -imageSize / 2, // y偏移（图片中心对齐）
            imageSize, // 宽度
            imageSize // 高度
        );

        // 恢复画布状态
        ctx.restore();
    };

    const drawAnimatedGuidePath = (ctx: CanvasRenderingContext2D, path: GuidePath) => {
        if (path.points.length < 2) return;

        // 将轨迹坐标从记录时的画布尺寸缩放到当前画布尺寸
        const recordedWidth = originalCanvasSize?.width || 800;
        const recordedHeight = originalCanvasSize?.height || 600;
        const scaleX = canvasSize.width / recordedWidth;
        const scaleY = canvasSize.height / recordedHeight;

        const scaledPoints = path.points.map((point) => ({
            x: point.x * scaleX,
            y: point.y * scaleY,
        }));

        // 设置黄色实线样式
        ctx.strokeStyle = "#FFD700"; // 金黄色
        ctx.lineWidth = 8;
        ctx.lineCap = "round";
        ctx.lineJoin = "round";

        // 设置为实线
        ctx.setLineDash([]);

        // 逐渐绘制黄色线条
        const totalPoints = scaledPoints.length;
        const animatedPointCount = Math.floor(totalPoints * animationProgress);

        ctx.beginPath();
        if (animatedPointCount > 0) {
            ctx.moveTo(scaledPoints[0].x, scaledPoints[0].y);

            // 绘制已完成的线段
            for (let i = 1; i < animatedPointCount; i++) {
                ctx.lineTo(scaledPoints[i].x, scaledPoints[i].y);
            }

            // 绘制部分完成的最后一段
            if (animatedPointCount < totalPoints) {
                const lastPoint = scaledPoints[animatedPointCount - 1];
                const nextPoint = scaledPoints[animatedPointCount];
                const segmentProgress = totalPoints * animationProgress - animatedPointCount;

                const partialX = lastPoint.x + (nextPoint.x - lastPoint.x) * segmentProgress;
                const partialY = lastPoint.y + (nextPoint.y - lastPoint.y) * segmentProgress;

                ctx.lineTo(partialX, partialY);
            }
        }

        ctx.stroke();

        // 重置虚线样式，避免影响其他绘制
        ctx.setLineDash([]);

        // 绘制箭头在黄线末端 - 简单可见版本
        if (animationProgress > 0.1 && animatedPointCount > 0) {
            let arrowX, arrowY, fromX, fromY;

            if (animatedPointCount < totalPoints) {
                // 箭头在部分完成的线段末端
                const lastPoint = scaledPoints[animatedPointCount - 1];
                const nextPoint = scaledPoints[animatedPointCount];
                const segmentProgress = totalPoints * animationProgress - animatedPointCount;

                arrowX = lastPoint.x + (nextPoint.x - lastPoint.x) * segmentProgress;
                arrowY = lastPoint.y + (nextPoint.y - lastPoint.y) * segmentProgress;
                fromX = lastPoint.x;
                fromY = lastPoint.y;
            } else {
                // 箭头在线条末端
                arrowX = scaledPoints[totalPoints - 1].x;
                arrowY = scaledPoints[totalPoints - 1].y;
                fromX = scaledPoints[Math.max(0, totalPoints - 2)].x;
                fromY = scaledPoints[Math.max(0, totalPoints - 2)].y;
            }

            drawArrow(ctx, fromX, fromY, arrowX, arrowY, "#FFD700", 20);
        }
    };

    // L1-4新增：绘制静态轨迹线
    const drawStaticTracePath = (ctx: CanvasRenderingContext2D, path: GuidePath) => {
        if (path.points.length < 2) return;

        // 将轨迹坐标从记录时的画布尺寸缩放到当前画布尺寸
        const recordedWidth = originalCanvasSize?.width || 800;
        const recordedHeight = originalCanvasSize?.height || 600;
        const scaleX = canvasSize.width / recordedWidth;
        const scaleY = canvasSize.height / recordedHeight;

        const scaledPoints = path.points.map((point) => ({
            x: point.x * scaleX,
            y: point.y * scaleY,
        }));

        // 设置静态轨迹线样式 - 灰色虚线
        ctx.strokeStyle = "#888888"; // 灰色
        ctx.lineWidth = 4;
        ctx.lineCap = "round";
        ctx.lineJoin = "round";

        // 设置虚线样式
        ctx.setLineDash([10, 5]); // 10像素实线，5像素空白

        // 绘制完整的静态轨迹线
        ctx.beginPath();
        ctx.moveTo(scaledPoints[0].x, scaledPoints[0].y);

        for (let i = 1; i < scaledPoints.length; i++) {
            ctx.lineTo(scaledPoints[i].x, scaledPoints[i].y);
        }

        ctx.stroke();

        // 重置虚线样式
        ctx.setLineDash([]);
    };





    const drawUserPath = (ctx: CanvasRenderingContext2D, points: Point[]) => {
        if (points.length < 2) return;

        // 设置黑色用户线条样式
        ctx.strokeStyle = "#000000"; // 黑色
        ctx.lineWidth = 4;
        ctx.lineCap = "round";
        ctx.lineJoin = "round";

        // 绘制平滑的用户线条
        drawSmoothCurve(ctx, points);
    };

    // 绘制平滑曲线的通用函数
    const drawSmoothCurve = (ctx: CanvasRenderingContext2D, points: Point[]) => {
        if (points.length < 2) return;

        ctx.beginPath();
        ctx.moveTo(points[0].x, points[0].y);

        if (points.length === 2) {
            // 只有两个点，直接连线
            ctx.lineTo(points[1].x, points[1].y);
        } else {
            // 使用二次贝塞尔曲线创建平滑效果
            for (let i = 1; i < points.length - 1; i++) {
                const currentPoint = points[i];
                const nextPoint = points[i + 1];

                // 控制点是当前点和下一点的中点
                const controlX = (currentPoint.x + nextPoint.x) / 2;
                const controlY = (currentPoint.y + nextPoint.y) / 2;

                ctx.quadraticCurveTo(currentPoint.x, currentPoint.y, controlX, controlY);
            }

            // 连接到最后一个点
            const lastPoint = points[points.length - 1];
            ctx.lineTo(lastPoint.x, lastPoint.y);
        }

        ctx.stroke();
    };



    const getMousePos = (e: React.MouseEvent<HTMLCanvasElement>) => {
        const canvas = canvasRef.current;
        if (!canvas) return { x: 0, y: 0 };

        const rect = canvas.getBoundingClientRect();

        // 获取canvas的实际显示尺寸
        const scaleX = canvas.width / rect.width;
        const scaleY = canvas.height / rect.height;

        // 计算相对于canvas的坐标，考虑缩放
        const x = (e.clientX - rect.left) * scaleX;
        const y = (e.clientY - rect.top) * scaleY;

        return { x, y };
    };

    const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
        e.preventDefault(); // 阻止默认行为
        e.stopPropagation(); // 阻止事件冒泡

        // L1-4新增：在引导阶段和静态轨迹阶段不允许用户描绘
        if (isL14Stage && (guideStage === 'guide-animation' || guideStage === 'static-trace')) {
            return;
        }

        // 用户开始描绘，只清除未来的重新引导定时器，不影响当前引导动画
        if (reGuideTimeoutId) {
            clearTimeout(reGuideTimeoutId);
            setReGuideTimeoutId(null);
        }

        setIsDrawing(true);
        const pos = getMousePos(e);
        setUserPath([pos]);
    };

    const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
        e.preventDefault(); // 阻止默认行为
        e.stopPropagation(); // 阻止事件冒泡

        // L1-4新增：在引导阶段和静态轨迹阶段不允许用户描绘
        if (isL14Stage && (guideStage === 'guide-animation' || guideStage === 'static-trace')) {
            return;
        }

        if (!isDrawing) return;

        const pos = getMousePos(e);
        const newPath = [...userPath, pos];
        setUserPath(newPath);
    };

    const handleMouseUp = (e?: React.MouseEvent<HTMLCanvasElement>) => {
        if (e) {
            e.preventDefault(); // 阻止默认行为
            e.stopPropagation(); // 阻止事件冒泡
        }

        if (!isDrawing || !currentPath) return;

        setIsDrawing(false);

        // 检查路径完成度
        const accuracy = calculatePathAccuracy(userPath, currentPath.points);



        if (accuracy > traceConfig.accuracyThreshold) {
            // 根据配置的准确度要求
            // 当前路径完成，停止当前引导并保存用户实际描绘的路径

            // 清除引导动画的定时器，停止循环
            if (reGuideTimeoutId) {
                clearTimeout(reGuideTimeoutId);
                setReGuideTimeoutId(null);
            }

            onPathComplete(currentPath.id, userPath);
            setCompletedPaths((prev) => [...prev, currentPath.id]);
            setCompletedUserPaths((prev) => ({...prev, [currentPath.id]: userPath}));

            // 移动到下一条路径（一条一条出现）
            if (currentPathIndex < processedPaths.length - 1) {
                // 寻找下一个有效的轨迹（至少2个点）
                let nextIndex = currentPathIndex + 1;
                while (nextIndex < processedPaths.length) {
                    const nextPath = processedPaths[nextIndex];
                    if (!nextPath || nextPath.points.length >= 2) {
                        // 找到有效轨迹或到达数组末尾
                        break;
                    }
                    // 将无效轨迹标记为已完成
                    setCompletedPaths((prev) => [...prev, nextPath.id]);
                    onPathComplete(nextPath.id, []);
                    nextIndex++;
                }

                if (nextIndex < processedPaths.length) {
                    // 找到有效轨迹，切换到下一条路径
                    setCurrentPathIndex(nextIndex);
                    setUserPath([]);
                    setAnimationProgress(0);
                } else {
                    // 没有更多有效轨迹，开始完成动画
                    setTimeout(() => {
                        setCanvasAnimationStage('completing');
                    }, 500);
                }
            } else {
                // 所有路径完成，开始完成动画
                setTimeout(() => {
                    setCanvasAnimationStage('completing');
                }, 500); // 0.5秒延迟，让用户看到最后一条线完成
            }
        } else {
            // 准确度不够，清除用户绘制的线条
            setUserPath([]);

            if (isL14Stage) {
                // L1-4新增：重新开始整个引导流程
                setGuideStage('guide-animation'); // 重新开始引导动画
                setAnimationProgress(0);

                // 清理静态轨迹定时器
                if (staticTraceTimeoutId) {
                    clearTimeout(staticTraceTimeoutId);
                    setStaticTraceTimeoutId(null);
                }
            }
            // 其他关卡：引导动画会自动继续
        }
    };

    // 生成黄色引导线的密集路径点
    const generateGuideLinePath = (guidePoints: Point[]): Point[] => {
        if (guidePoints.length < 2) return guidePoints;

        // 将引导路径坐标缩放到当前画布尺寸
        const recordedWidth = originalCanvasSize?.width || 800;
        const recordedHeight = originalCanvasSize?.height || 600;
        const scaleX = canvasSize.width / recordedWidth;
        const scaleY = canvasSize.height / recordedHeight;

        const scaledPoints = guidePoints.map((point) => ({
            x: point.x * scaleX,
            y: point.y * scaleY,
        }));

        // 在关键点之间插值生成密集的路径点
        const densePoints: Point[] = [];
        const pointSpacing = 5; // 每5像素一个点

        for (let i = 0; i < scaledPoints.length - 1; i++) {
            const start = scaledPoints[i];
            const end = scaledPoints[i + 1];

            const distance = Math.sqrt(
                Math.pow(end.x - start.x, 2) + Math.pow(end.y - start.y, 2)
            );

            const steps = Math.ceil(distance / pointSpacing);

            for (let step = 0; step <= steps; step++) {
                const t = step / steps;
                densePoints.push({
                    x: start.x + (end.x - start.x) * t,
                    y: start.y + (end.y - start.y) * t
                });
            }
        }

        return densePoints;
    };

    const calculatePathAccuracy = (userPoints: Point[], guidePoints: Point[]): number => {
        if (userPoints.length === 0 || guidePoints.length === 0) return 0;
        if (userPoints.length < 2) return 0;

        // 生成黄色引导线的密集路径点
        const guideLinePath = generateGuideLinePath(guidePoints);

        // 计算用户路径与黄色引导线的相似度
        let totalDistance = 0;
        let validPoints = 0;

        // 对于用户路径的每个点，找到引导线上最近的点
        for (const userPoint of userPoints) {
            let minDistance = Infinity;

            for (const guidePoint of guideLinePath) {
                const distance = Math.sqrt(
                    Math.pow(userPoint.x - guidePoint.x, 2) +
                    Math.pow(userPoint.y - guidePoint.y, 2)
                );
                minDistance = Math.min(minDistance, distance);
            }

            // 只计算在容差范围内的点
            if (minDistance <= traceConfig.tolerance) {
                totalDistance += minDistance;
                validPoints++;
            }
        }

        if (validPoints === 0) return 0;

        // 计算平均距离
        const averageDistance = totalDistance / validPoints;

        // 计算覆盖率（用户描绘了多少比例的路径）
        const coverageRatio = validPoints / userPoints.length;

        // 计算距离得分（距离越小得分越高）
        const distanceScore = Math.max(0, 1 - (averageDistance / traceConfig.tolerance));

        // 综合得分：距离得分 * 覆盖率
        const finalScore = distanceScore * coverageRatio;



        return finalScore;
    };

    return (
        <div
            className="trace-practice min-h-screen"
            data-guide-stage={guideStage}
            style={{
                background: "transparent", // 使用透明背景，让全局背景显示
            }}
        >
            <div className="flex items-center justify-center min-h-screen">
                {/* 画板容器 - 居中在大圆中 */}
                <div className="relative" style={{
                    transform: "translateY(-120px) translateX(10px)", // 调整垂直位置，让画板在大圆中居中
                }}>
                    {/* 指导文字 - 居中显示，限制宽度 */}
                    {showInstruction && (
                        <div className="fixed inset-0 flex items-center justify-center z-50">
                            <div className="bg-black bg-opacity-75 text-white px-8 py-6 rounded-lg shadow-lg max-w-[800px] mx-4">
                                <div className="text-2xl font-medium text-center leading-relaxed">
                                    {displayedText}
                                    <span className="animate-pulse">|</span>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* 结束鼓励语 - 居中显示，限制宽度 */}
                    {showEndMessage && (
                        <div className="fixed inset-0 flex items-center justify-center z-50">
                            <div className="bg-green-600 bg-opacity-90 text-white px-8 py-6 rounded-lg shadow-lg max-w-[800px] mx-4 border-2 border-green-400">
                                <div className="text-2xl font-medium text-center leading-relaxed">
                                    🎉 {displayedEndText}
                                    <span className="animate-pulse">|</span>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* 背景图片容器 */}
                    <div className="relative flex items-center justify-center">
                        {/* 背景图片 - 带缩放和透明度动画 */}
                        {bgImageSrc && (
                            <div
                                style={{
                                    width: `${canvasSize.width}px`,
                                    height: `${canvasSize.height}px`,
                                    transform: `scale(${canvasScale})`,
                                    opacity: canvasOpacity,
                                    transition: 'none', // 使用自定义动画，不用CSS transition
                                }}
                            >
                                <img
                                    src={bgImageSrc}
                                    alt="背景图片"
                                    className="object-contain w-full h-full"
                                />
                            </div>
                        )}



                        {/* 画布覆盖在背景图片上 */}
                        <div
                            className="absolute inset-0 flex items-center justify-center"
                            style={{
                                transform: `scale(${canvasScale})`,
                                opacity: canvasOpacity,
                                transition: 'none', // 使用自定义动画
                            }}
                        >
                            <canvas
                                ref={canvasRef}
                                width={canvasSize.width}
                                height={canvasSize.height}
                                className="bg-transparent"
                                style={{
                                    touchAction: "none",
                                    userSelect: "none",
                                    width: `${canvasSize.width}px`,
                                    height: `${canvasSize.height}px`,
                                    position: "relative",
                                    margin: 0,
                                    padding: 0,
                                    border: "none",
                                    outline: "none",
                                    display: "block",
                                }}
                                onMouseDown={canvasAnimationStage === 'active' ? handleMouseDown : undefined}
                                onMouseMove={canvasAnimationStage === 'active' ? handleMouseMove : undefined}
                                onMouseUp={canvasAnimationStage === 'active' ? handleMouseUp : undefined}
                                onMouseLeave={canvasAnimationStage === 'active' ? () => setIsDrawing(false) : undefined}
                            />
                        </div>

                        {/* 完整图片覆盖层 - 自定义动画 */}
                        {(canvasAnimationStage === 'completing' || canvasAnimationStage === 'showing' || canvasAnimationStage === 'exiting') && fullImageSrc && (
                            <div
                                className="absolute inset-0 flex flex-col items-center justify-center"
                                style={{
                                    opacity: rewardOpacity,
                                    transform: `scale(${rewardScale})`,
                                    transition: 'none', // 使用自定义动画
                                }}
                            >
                                <img
                                    src={fullImageSrc}
                                    alt="完整图片"
                                    className="object-contain"
                                    style={{
                                        width: `${canvasSize.width}px`,
                                        height: `${canvasSize.height}px`,
                                    }}
                                    onError={(e) => {
                                        console.error("奖励图片加载失败");
                                        e.currentTarget.style.display = "none";
                                    }}
                                />
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TracePractice;
