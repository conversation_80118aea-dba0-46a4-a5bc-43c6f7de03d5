import React from 'react'
import { <PERSON>, Typo<PERSON>, But<PERSON>, Row, Col, Popconfirm, Switch, Space, Divider } from 'antd'
import { motion } from 'framer-motion'
import {
  LineChartOutlined,
  // BorderOutlined,
  PlayCircleOutlined,
  StarOutlined,
  BoxPlotOutlined,
  PictureOutlined,
  CameraOutlined,
  LockOutlined,
  ReloadOutlined,
  ExperimentOutlined
} from '@ant-design/icons'

const { Title, Text } = Typography

interface Level {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  difficulty: number
  stages: Stage[]
}

interface Stage {
  id: string
  title: string
  description: string
  type: 'free-draw' | 'trace' | 'shape-straight' | 'shape-curved' | 'coming-soon'
  comingSoon?: boolean
}

interface LevelSelectorProps {
  onSelectLevel: (levelId: string) => void
  unlockedLevels?: string[]
  onResetProgress?: () => void
  testMode?: boolean
  onToggleTestMode?: () => void
}

const LevelSelector: React.FC<LevelSelectorProps> = ({
  onSelectLevel,
  unlockedLevels = ['level-1'],
  onResetProgress,
  testMode = false,
  onToggleTestMode
}) => {
  const levels: Level[] = [
    {
      id: 'level-1',
      title: '线条启蒙',
      description: '从基础的线条开始，培养手部协调能力',
      icon: <LineChartOutlined className="text-4xl" />,
      difficulty: 1,
      stages: [
        {
          id: 'free-lines',
          title: '自由画线',
          description: '随意画线，创作属于您的艺术作品',
          type: 'free-draw'
        },
        {
          id: 'trace-lines',
          title: '描线练习',
          description: '跟随引导线条，提高绘画精准度',
          type: 'trace'
        },
        {
          id: 'straight-shapes',
          title: '直线图形',
          description: '绘制三角形、正方形、矩形等直线图形',
          type: 'shape-straight'
        },
        {
          id: 'curved-shapes',
          title: '曲线图形',
          description: '绘制圆形、椭圆等曲线图形',
          type: 'shape-curved'
        }
      ]
    },
    {
      id: 'level-2',
      title: '立体空间',
      description: '从二维图形到三维立体，提升空间想象力',
      icon: <BoxPlotOutlined className="text-4xl" />,
      difficulty: 2,
      stages: [
        {
          id: '3d-shapes',
          title: '立体图形',
          description: '绘制锥形、立方体、圆柱体等三维图形',
          type: 'coming-soon',
          comingSoon: true
        },
        {
          id: 'color-fill',
          title: '色彩填充',
          description: '为几何图形填色，学习色系和色谱搭配',
          type: 'coming-soon',
          comingSoon: true
        },
        {
          id: 'texture-brush',
          title: '质感画笔',
          description: '复杂曲线描边，选择不同质感的画笔填色',
          type: 'coming-soon',
          comingSoon: true
        }
      ]
    },
    {
      id: 'level-3',
      title: '画面构图',
      description: '通过引导线条，完成完整的艺术画面',
      icon: <PictureOutlined className="text-4xl" />,
      difficulty: 3,
      stages: [
        {
          id: 'abstract-art',
          title: '抽象艺术',
          description: '用抽象线条和色块创作现代艺术作品',
          type: 'coming-soon',
          comingSoon: true
        },
        {
          id: 'geometric-still',
          title: '几何静物',
          description: '绘制几何形状组成的静物画',
          type: 'coming-soon',
          comingSoon: true
        },
        {
          id: 'life-objects',
          title: '生活物品',
          description: '描绘日常生活中的物品和场景',
          type: 'coming-soon',
          comingSoon: true
        }
      ]
    },
    {
      id: 'level-4',
      title: '智能创作',
      description: '上传照片，AI辅助创作个性化艺术作品',
      icon: <CameraOutlined className="text-4xl" />,
      difficulty: 4,
      stages: [
        {
          id: 'photo-trace',
          title: '照片描边',
          description: '上传照片，提取轮廓进行描边练习',
          type: 'coming-soon',
          comingSoon: true
        },
        {
          id: 'style-render',
          title: '风格渲染',
          description: '选择不同艺术风格，AI辅助渲染作品',
          type: 'coming-soon',
          comingSoon: true
        },
        {
          id: 'ai-creation',
          title: 'AI协作',
          description: '与AI协作，创作独特的个人艺术作品',
          type: 'coming-soon',
          comingSoon: true
        }
      ]
    }
  ]

  const getDifficultyColor = (difficulty: number) => {
    switch (difficulty) {
      case 1: return 'text-green-600 bg-green-100'
      case 2: return 'text-blue-600 bg-blue-100'
      case 3: return 'text-purple-600 bg-purple-100'
      case 4: return 'text-orange-600 bg-orange-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getDifficultyText = (difficulty: number) => {
    switch (difficulty) {
      case 1: return '入门'
      case 2: return '初级'
      case 3: return '中级'
      case 4: return '高级'
      default: return '未知'
    }
  }

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center mb-8"
      >
        <Title level={1} className="text-purple-600 mb-4">
          选择游戏级别
        </Title>
        <Text className="text-xl text-gray-600">
          从简单的线条开始，逐步提升您的绘画技能
        </Text>
      </motion.div>

      <Row gutter={[24, 24]}>
        {levels.map((level, levelIndex) => {
          const isLevelUnlocked = unlockedLevels.includes(level.id)

          return (
            <Col xs={24} lg={12} key={level.id}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: levelIndex * 0.1 }}
              >
                <Card
                  className={`h-full shadow-lg transition-all duration-300 ${
                    isLevelUnlocked
                      ? 'hover:shadow-xl cursor-pointer'
                      : 'opacity-60 cursor-not-allowed'
                  }`}
                  bodyStyle={{ padding: '24px' }}
                >
                  <div className="text-center mb-6">
                    <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-white relative ${
                      isLevelUnlocked
                        ? 'bg-gradient-to-br from-purple-400 to-purple-600'
                        : 'bg-gradient-to-br from-gray-400 to-gray-500'
                    }`}>
                      {!isLevelUnlocked && (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <LockOutlined className="text-2xl" />
                        </div>
                      )}
                      {isLevelUnlocked && level.icon}
                    </div>

                    <Title level={3} className={`mb-2 ${!isLevelUnlocked ? 'text-gray-500' : ''}`}>
                      {level.title}
                    </Title>

                    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium mb-3 ${
                      isLevelUnlocked ? getDifficultyColor(level.difficulty) : 'bg-gray-100 text-gray-500'
                    }`}>
                      <StarOutlined className="mr-1" />
                      {getDifficultyText(level.difficulty)}
                    </div>

                    <Text className={`block ${!isLevelUnlocked ? 'text-gray-500' : 'text-gray-600'}`}>
                      {isLevelUnlocked ? level.description : '完成前面的级别来解锁'}
                    </Text>
                  </div>

                  {/* 进入级别按钮 */}
                  <div className="mt-6">
                    <motion.div
                      whileHover={isLevelUnlocked ? { scale: 1.02 } : {}}
                      whileTap={isLevelUnlocked ? { scale: 0.98 } : {}}
                    >
                      <Button
                        type={isLevelUnlocked ? "primary" : "default"}
                        size="large"
                        icon={isLevelUnlocked ? <PlayCircleOutlined /> : <LockOutlined />}
                        onClick={() => isLevelUnlocked && onSelectLevel(level.id)}
                        disabled={!isLevelUnlocked}
                        className="w-full h-12 text-lg"
                      >
                        {isLevelUnlocked ? '进入级别' : '级别锁定'}
                      </Button>
                    </motion.div>
                  </div>
                </Card>
              </motion.div>
            </Col>
          )
        })}
      </Row>

      {/* 开发者选项 */}
      {(onResetProgress || onToggleTestMode) && (
        <div className="text-center mt-8">
          <Card className="max-w-md mx-auto bg-gray-50">
            <div className="text-center">
              <Text className="text-gray-600 mb-4 block">
                <ExperimentOutlined className="mr-2" />
                开发者选项
              </Text>

              <Space direction="vertical" size="middle" className="w-full">
                {/* 测试模式开关 */}
                {onToggleTestMode && (
                  <div className="flex items-center justify-between">
                    <Space>
                      <ExperimentOutlined className="text-blue-500" />
                      <Text>测试模式</Text>
                    </Space>
                    <Switch
                      checked={testMode}
                      onChange={onToggleTestMode}
                      checkedChildren="开启"
                      unCheckedChildren="关闭"
                    />
                  </div>
                )}

                {testMode && (
                  <Text type="secondary" className="text-xs block">
                    🧪 测试模式已开启，所有关卡已解锁
                  </Text>
                )}

                {onToggleTestMode && onResetProgress && <Divider className="my-2" />}

                {/* 重置进度按钮 */}
                {onResetProgress && (
                  <Popconfirm
                    title="重置游戏进度"
                    description="确定要重置所有游戏进度吗？这将清除所有解锁的级别。"
                    onConfirm={onResetProgress}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button
                      icon={<ReloadOutlined />}
                      size="small"
                      className="text-gray-500 hover:text-gray-700"
                      block
                    >
                      重置进度
                    </Button>
                  </Popconfirm>
                )}
              </Space>
            </div>
          </Card>
        </div>
      )}
    </div>
  )
}

export default LevelSelector
