import React from "react";
import { <PERSON>, Typo<PERSON>, <PERSON><PERSON>, <PERSON>, Col } from "antd";
import { motion } from "framer-motion";
import { ArrowLeftOutlined, PlayCircleOutlined, LockOutlined, StarOutlined } from "@ant-design/icons";

// 测试模式配置
import { isTestModeEnabled, isStageUnlocked as isStageUnlockedByConfig } from '@/config/testMode';

// 统一的关卡配置
import { Level } from '@/config/levels';

const { Title, Text } = Typography;

interface StageSelectorProps {
    level: Level;
    onSelectStage: (stageId: string) => void;
    onBack: () => void;
    unlockedStages?: string[];
}

const StageSelector: React.FC<StageSelectorProps> = ({ level, onSelectStage, onBack, unlockedStages = [] }) => {
    // Level 1 特殊处理 - 使用背景图交互
    if (level.id === 'level-1') {
        return <Level1StageSelector level={level} onSelectStage={onSelectStage} onBack={onBack} unlockedStages={unlockedStages} />;
    }
    const getDifficultyColor = (difficulty: number) => {
        switch (difficulty) {
            case 1:
                return "text-green-600 bg-green-100";
            case 2:
                return "text-blue-600 bg-blue-100";
            case 3:
                return "text-purple-600 bg-purple-100";
            case 4:
                return "text-orange-600 bg-orange-100";
            default:
                return "text-gray-600 bg-gray-100";
        }
    };

    const getDifficultyText = (difficulty: number) => {
        switch (difficulty) {
            case 1:
                return "入门";
            case 2:
                return "初级";
            case 3:
                return "中级";
            case 4:
                return "高级";
            default:
                return "未知";
        }
    };

    // 检查阶段是否解锁
    const isStageUnlocked = (stageId: string, stageIndex: number) => {
        // 测试模式下检查配置
        if (isTestModeEnabled()) {
            return isStageUnlockedByConfig(stageId);
        }

        // 第一个阶段默认解锁
        if (stageIndex === 0) return true;

        // 检查是否在解锁列表中
        return unlockedStages.includes(stageId);
    };

    return (
        <div className="max-w-6xl mx-auto px-4 py-8">
            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }}>
                {/* 级别标题和返回按钮 */}
                <Card className="mb-6 shadow-sm">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg flex items-center justify-center text-white text-2xl">
                                {typeof level.icon === "string" ? level.icon : level.icon}
                            </div>
                            <div>
                                <div className="flex items-center gap-3 mb-2">
                                    <Title level={2} className="mb-0 text-purple-600">
                                        {level.title}
                                    </Title>
                                    <div
                                        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(level.difficulty)}`}
                                    >
                                        <StarOutlined className="mr-1" />
                                        {getDifficultyText(level.difficulty)}
                                    </div>
                                </div>
                                <Text className="text-lg text-gray-600">{level.description}</Text>
                            </div>
                        </div>
                        <Button icon={<ArrowLeftOutlined />} onClick={onBack} size="large" className="h-12 px-6">
                            返回级别选择
                        </Button>
                    </div>
                </Card>

                {/* 练习卡片 */}
                <Row gutter={[24, 24]}>
                    {level.stages.map((stage, stageIndex) => {
                        const isUnlocked = isStageUnlocked(stage.id, stageIndex);
                        const canPlay = !stage.comingSoon && isUnlocked;

                        return (
                            <Col xs={24} sm={12} lg={8} key={stage.id}>
                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: stageIndex * 0.1 }}
                                    whileHover={canPlay ? { scale: 1.02 } : {}}
                                    whileTap={canPlay ? { scale: 0.98 } : {}}
                                >
                                    <Card
                                        className={`h-full shadow-lg transition-all duration-300 ${
                                            canPlay ? "hover:shadow-xl cursor-pointer" : "opacity-60 cursor-not-allowed"
                                        }`}
                                        bodyStyle={{ padding: "24px" }}
                                        onClick={() => canPlay && onSelectStage(stage.id)}
                                    >
                                        <div className="text-center">
                                            <div
                                                className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-white ${
                                                    canPlay
                                                        ? "bg-gradient-to-br from-blue-400 to-blue-600"
                                                        : "bg-gradient-to-br from-gray-400 to-gray-500"
                                                }`}
                                            >
                                                {canPlay ? (
                                                    <PlayCircleOutlined className="text-2xl" />
                                                ) : (
                                                    <LockOutlined className="text-2xl" />
                                                )}
                                            </div>

                                            <Title level={4} className={`mb-3 ${!canPlay ? "text-gray-500" : ""}`}>
                                                {stage.title}
                                                {stage.comingSoon && (
                                                    <div className="mt-2">
                                                        <span className="text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded-full">
                                                            即将推出
                                                        </span>
                                                    </div>
                                                )}
                                            </Title>

                                            <Text
                                                className={`block mb-6 ${!canPlay ? "text-gray-500" : "text-gray-600"}`}
                                            >
                                                {stage.description}
                                            </Text>

                                            <Button
                                                type={canPlay ? "primary" : "default"}
                                                size="large"
                                                icon={canPlay ? <PlayCircleOutlined /> : <LockOutlined />}
                                                disabled={!canPlay}
                                                className="w-full h-12"
                                            >
                                                {canPlay
                                                    ? "开始练习"
                                                    : stage.comingSoon
                                                        ? "即将推出"
                                                        : "完成前面练习解锁"
                                                }
                                            </Button>
                                        </div>
                                    </Card>
                                </motion.div>
                            </Col>
                        );
                    })}
                </Row>

                {/* 学习提示 */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    className="mt-8"
                >
                    <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
                        <div className="text-center">
                            <Title level={4} className="text-blue-600 mb-3">
                                💡 学习建议
                            </Title>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-700">
                                <div className="flex items-center justify-center gap-2">
                                    <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                                    <span>按顺序完成练习效果更佳</span>
                                </div>
                                <div className="flex items-center justify-center gap-2">
                                    <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
                                    <span>每次练习时间不宜过长</span>
                                </div>
                                <div className="flex items-center justify-center gap-2">
                                    <span className="w-2 h-2 bg-pink-400 rounded-full"></span>
                                    <span>重复练习有助于提高技能</span>
                                </div>
                            </div>
                        </div>
                    </Card>
                </motion.div>
            </motion.div>
        </div>
    );
};

// Level 1 专用的关卡选择器 - 使用背景图交互
const Level1StageSelector: React.FC<StageSelectorProps> = ({ level, onSelectStage, onBack, unlockedStages = [] }) => {
    // 开发模式 - 显示热区调试信息 (可以通过URL参数控制)
    const isDevelopment = window.location.search.includes('debug=true');
    // 检查阶段是否解锁
    const isStageUnlocked = (stageId: string, stageIndex: number) => {
        // 测试模式下检查配置
        if (isTestModeEnabled()) {
            return isStageUnlockedByConfig(stageId);
        }

        // 第一个阶段默认解锁
        if (stageIndex === 0) return true;

        // 检查是否在解锁列表中
        return unlockedStages.includes(stageId);
    };

    // 关卡热区配置 - 根据背景图中圆圈的位置
    // 这些位置可能需要根据实际背景图调整
    const stageHotspots = [
        { id: 'L1-1', x: '15%', y: '25%', number: 1 },  // 第1个圆圈位置
        { id: 'L1-2', x: '30%', y: '40%', number: 2 },  // 第2个圆圈位置
        { id: 'L1-3', x: '50%', y: '30%', number: 3 },  // 第3个圆圈位置
        { id: 'L1-4', x: '70%', y: '45%', number: 4 },  // 第4个圆圈位置
        { id: 'L1-5', x: '85%', y: '35%', number: 5 },  // 第5个圆圈位置
    ];

    const handleStageClick = (stageId: string) => {
        const stageIndex = level.stages.findIndex(s => s.id === stageId);
        const isUnlocked = isStageUnlocked(stageId, stageIndex);

        if (isUnlocked) {
            onSelectStage(stageId);
        }
    };

    return (
        <div className="min-h-screen relative">
            {/* 背景图容器 */}
            <div
                className="absolute inset-0 bg-no-repeat"
                style={{
                    backgroundImage: 'url(/background-l1.jpg)',
                    backgroundSize: '100% 100vh',
                    backgroundPosition: 'center top'
                }}
            >
                {/* 返回按钮 */}
                <div className="absolute top-8 left-8 z-10">
                    <Button
                        icon={<ArrowLeftOutlined />}
                        onClick={onBack}
                        size="large"
                        className="h-12 px-6 bg-white/90 backdrop-blur-sm hover:bg-white"
                    >
                        返回级别选择
                    </Button>
                </div>



                {/* 可点击的关卡热区 */}
                {stageHotspots.map((hotspot, index) => {
                    const stage = level.stages.find(s => s.id === hotspot.id);
                    const isUnlocked = isStageUnlocked(hotspot.id, index);

                    if (!stage) return null;

                    return (
                        <motion.div
                            key={hotspot.id}
                            className="absolute z-20"
                            style={{
                                left: hotspot.x,
                                top: hotspot.y,
                                transform: 'translate(-50%, -50%)'
                            }}
                            initial={{ opacity: 0, scale: 0 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.6, delay: index * 0.1 }}
                            whileHover={isUnlocked ? { scale: 1.1 } : {}}
                            whileTap={isUnlocked ? { scale: 0.95 } : {}}
                        >
                            <div
                                className={`
                                    w-16 h-16 rounded-full flex items-center justify-center text-white text-xl font-bold
                                    cursor-pointer transition-all duration-300 shadow-lg
                                    ${isUnlocked
                                        ? 'bg-gradient-to-br from-blue-400 to-blue-600 hover:shadow-xl'
                                        : 'bg-gradient-to-br from-gray-400 to-gray-500 cursor-not-allowed opacity-60'
                                    }
                                    ${isDevelopment ? 'ring-2 ring-red-500 ring-opacity-50' : ''}
                                `}
                                onClick={() => handleStageClick(hotspot.id)}
                                title={isUnlocked ? `${stage.title} - 点击开始` : `${stage.title} - 需要完成前面的关卡`}
                            >
                                {isUnlocked ? (
                                    hotspot.number
                                ) : (
                                    <LockOutlined className="text-lg" />
                                )}
                            </div>

                            {/* 开发模式下显示位置信息 */}
                            {isDevelopment && (
                                <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs bg-red-500 text-white px-1 rounded">
                                    {hotspot.x}, {hotspot.y}
                                </div>
                            )}

                            {/* 关卡标题提示 */}
                            <div className="absolute top-full mt-2 left-1/2 transform -translate-x-1/2 whitespace-nowrap">
                                <div className="bg-black/70 text-white px-2 py-1 rounded text-sm">
                                    {stage.title}
                                </div>
                            </div>
                        </motion.div>
                    );
                })}


            </div>
        </div>
    );
};

export default StageSelector;
