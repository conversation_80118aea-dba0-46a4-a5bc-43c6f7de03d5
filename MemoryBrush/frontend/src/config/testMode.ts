// 测试模式配置
export const TEST_MODE_CONFIG = {
  // 是否启用测试模式
  // true: 解锁所有关卡，方便测试
  // false: 使用正常的关卡解锁逻辑
  enabled: true,
  
  // 所有级别ID
  allLevels: ['level-1', 'level-2', 'level-3', 'level-4'],
  
  // 所有关卡ID（包括级别选择器中的关卡和L1-x格式的关卡）
  allStages: [
    // LevelSelector 中的关卡
    'free-lines',
    'trace-lines',
    'straight-shapes',
    'curved-shapes',
    '3d-shapes',
    'color-fill',
    'texture-brush',
    'abstract-art',
    'landscape-art',
    'portrait-art',
    'photo-trace',
    'style-render',
    'ai-creation',

    // GamePage 中的 L1-x 格式关卡
    'L1-1',
    'L1-2',
    'L1-3',
    'L1-4',
    'L1-5',
    'L2-1',
    'L2-2',
    'L2-3',
    'L2-4',
    'L3-1',
    'L3-2',
    'L3-3',
    'L3-4',
    'L4-1',
    'L4-2',
    'L4-3',
    'L4-4'
  ],
  
  // 描线练习关卡ID（L1-x 格式）
  allTraceLevels: [
    'L1-1',
    'L1-2', 
    'L1-3',
    'L1-4',
    'L2-1',
    'L2-2',
    'L2-3',
    'L2-4',
    'L3-1',
    'L3-2',
    'L3-3',
    'L3-4',
    'L4-1',
    'L4-2',
    'L4-3',
    'L4-4'
  ]
}

// 获取测试模式状态
export const isTestModeEnabled = (): boolean => {
  return TEST_MODE_CONFIG.enabled
}

// 获取所有解锁的级别
export const getAllUnlockedLevels = (): string[] => {
  if (isTestModeEnabled()) {
    return TEST_MODE_CONFIG.allLevels
  }
  // 正常模式下从localStorage读取
  const saved = localStorage.getItem('memorybrush-unlocked-levels')
  return saved ? JSON.parse(saved) : ['level-1']
}

// 获取所有解锁的关卡
export const getAllUnlockedStages = (): string[] => {
  if (isTestModeEnabled()) {
    return TEST_MODE_CONFIG.allStages
  }
  // 正常模式下从localStorage读取
  const saved = localStorage.getItem('memorybrush-unlocked-stages')
  return saved ? JSON.parse(saved) : ['free-lines', 'L1-1'] // 默认解锁第一个关卡
}

// 获取所有解锁的描线关卡
export const getAllUnlockedTraceLevels = (): string[] => {
  if (isTestModeEnabled()) {
    return TEST_MODE_CONFIG.allTraceLevels
  }
  // 正常模式下从localStorage读取
  const saved = localStorage.getItem('memorybrush-unlocked-trace-levels')
  return saved ? JSON.parse(saved) : ['L1-1']
}

// 检查级别是否解锁
export const isLevelUnlocked = (levelId: string): boolean => {
  if (isTestModeEnabled()) {
    return TEST_MODE_CONFIG.allLevels.includes(levelId)
  }
  const unlockedLevels = getAllUnlockedLevels()
  return unlockedLevels.includes(levelId)
}

// 检查关卡是否解锁
export const isStageUnlocked = (stageId: string): boolean => {
  if (isTestModeEnabled()) {
    return TEST_MODE_CONFIG.allStages.includes(stageId)
  }
  const unlockedStages = getAllUnlockedStages()
  return unlockedStages.includes(stageId)
}

// 检查描线关卡是否解锁
export const isTraceLevelUnlocked = (traceLevelId: string): boolean => {
  if (isTestModeEnabled()) {
    return TEST_MODE_CONFIG.allTraceLevels.includes(traceLevelId)
  }
  const unlockedTraceLevels = getAllUnlockedTraceLevels()
  return unlockedTraceLevels.includes(traceLevelId)
}
