var Er=Object.defineProperty;var kr=(e,t,n)=>t in e?Er(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var kt=(e,t,n)=>kr(e,typeof t!="symbol"?t+"":t,n);import{r as u,c as Cr,b as Tr,g as Pr,R as Ve}from"./vendor-BTWMFwqw.js";import{T as Le,C as ie,R as ze,a as nt,b as Dt,P as jn,c as Ps,d as ht,e as vn,f as Ft,g as Rs,h as Rr,i as _r,j as Lr,k as ft,B as H,l as wn,m as rt,n as He,s as K,S as fe,o as Sn,p as Ar,q as Nn,r as En,t as mt,u as Fs,I as Ms,v as Ge,w as Ir,x as Bt,y as Us,z as ys,A as kn,D as $r,E as _s,F as Cn,M as Mt,G as Or,H as Dr,J as zs,K as Fr,L as Ct,N as Mr,O as Ur,Q as zr,U as Tn,V as bs,W as Pn,X as Br,Y as qr,Z as Hr,_ as At,$ as Wr}from"./antd-SiMkeERs.js";import{m as re,A as Jr}from"./animation-BAuEqiwG.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))r(a);new MutationObserver(a=>{for(const o of a)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(a){const o={};return a.integrity&&(o.integrity=a.integrity),a.referrerPolicy&&(o.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?o.credentials="include":a.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(a){if(a.ep)return;a.ep=!0;const o=n(a);fetch(a.href,o)}})();var Rn={exports:{}},qt={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gr=u,Vr=Symbol.for("react.element"),Yr=Symbol.for("react.fragment"),Xr=Object.prototype.hasOwnProperty,Kr=Gr.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Qr={key:!0,ref:!0,__self:!0,__source:!0};function _n(e,t,n){var r,a={},o=null,i=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)Xr.call(t,r)&&!Qr.hasOwnProperty(r)&&(a[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)a[r]===void 0&&(a[r]=t[r]);return{$$typeof:Vr,type:e,key:o,ref:i,props:a,_owner:Kr.current}}qt.Fragment=Yr;qt.jsx=_n;qt.jsxs=_n;Rn.exports=qt;var s=Rn.exports,js={},Bs=Cr;js.createRoot=Bs.createRoot,js.hydrateRoot=Bs.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function pt(){return pt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},pt.apply(this,arguments)}var Ye;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Ye||(Ye={}));const qs="popstate";function Zr(e){e===void 0&&(e={});function t(r,a){let{pathname:o,search:i,hash:c}=r.location;return vs("",{pathname:o,search:i,hash:c},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function n(r,a){return typeof a=="string"?a:An(a)}return ta(t,n,null,e)}function Se(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Ln(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function ea(){return Math.random().toString(36).substr(2,8)}function Hs(e,t){return{usr:e.state,key:e.key,idx:t}}function vs(e,t,n,r){return n===void 0&&(n=null),pt({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?at(t):t,{state:n,key:t&&t.key||r||ea()})}function An(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function at(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function ta(e,t,n,r){r===void 0&&(r={});let{window:a=document.defaultView,v5Compat:o=!1}=r,i=a.history,c=Ye.Pop,l=null,h=d();h==null&&(h=0,i.replaceState(pt({},i.state,{idx:h}),""));function d(){return(i.state||{idx:null}).idx}function m(){c=Ye.Pop;let x=d(),P=x==null?null:x-h;h=x,l&&l({action:c,location:j.location,delta:P})}function v(x,P){c=Ye.Push;let T=vs(j.location,x,P);h=d()+1;let E=Hs(T,h),q=j.createHref(T);try{i.pushState(E,"",q)}catch(Q){if(Q instanceof DOMException&&Q.name==="DataCloneError")throw Q;a.location.assign(q)}o&&l&&l({action:c,location:j.location,delta:1})}function L(x,P){c=Ye.Replace;let T=vs(j.location,x,P);h=d();let E=Hs(T,h),q=j.createHref(T);i.replaceState(E,"",q),o&&l&&l({action:c,location:j.location,delta:0})}function b(x){let P=a.location.origin!=="null"?a.location.origin:a.location.href,T=typeof x=="string"?x:An(x);return T=T.replace(/ $/,"%20"),Se(P,"No window.location.(origin|href) available to create URL for href: "+T),new URL(T,P)}let j={get action(){return c},get location(){return e(a,i)},listen(x){if(l)throw new Error("A history only accepts one active listener");return a.addEventListener(qs,m),l=x,()=>{a.removeEventListener(qs,m),l=null}},createHref(x){return t(a,x)},createURL:b,encodeLocation(x){let P=b(x);return{pathname:P.pathname,search:P.search,hash:P.hash}},push:v,replace:L,go(x){return i.go(x)}};return j}var Ws;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Ws||(Ws={}));function sa(e,t,n){return n===void 0&&(n="/"),na(e,t,n)}function na(e,t,n,r){let a=typeof t=="string"?at(t):t,o=On(a.pathname||"/",n);if(o==null)return null;let i=In(e);ra(i);let c=null;for(let l=0;c==null&&l<i.length;++l){let h=xa(o);c=ma(i[l],h)}return c}function In(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let a=(o,i,c)=>{let l={relativePath:c===void 0?o.path||"":c,caseSensitive:o.caseSensitive===!0,childrenIndex:i,route:o};l.relativePath.startsWith("/")&&(Se(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let h=Ke([r,l.relativePath]),d=n.concat(l);o.children&&o.children.length>0&&(Se(o.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+h+'".')),In(o.children,t,d,h)),!(o.path==null&&!o.index)&&t.push({path:h,score:ua(h,o.index),routesMeta:d})};return e.forEach((o,i)=>{var c;if(o.path===""||!((c=o.path)!=null&&c.includes("?")))a(o,i);else for(let l of $n(o.path))a(o,i,l)}),t}function $n(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,a=n.endsWith("?"),o=n.replace(/\?$/,"");if(r.length===0)return a?[o,""]:[o];let i=$n(r.join("/")),c=[];return c.push(...i.map(l=>l===""?o:[o,l].join("/"))),a&&c.push(...i),c.map(l=>e.startsWith("/")&&l===""?"/":l)}function ra(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:ha(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const aa=/^:[\w-]+$/,oa=3,ia=2,la=1,ca=10,da=-2,Js=e=>e==="*";function ua(e,t){let n=e.split("/"),r=n.length;return n.some(Js)&&(r+=da),t&&(r+=ia),n.filter(a=>!Js(a)).reduce((a,o)=>a+(aa.test(o)?oa:o===""?la:ca),r)}function ha(e,t){return e.length===t.length&&e.slice(0,-1).every((r,a)=>r===t[a])?e[e.length-1]-t[t.length-1]:0}function ma(e,t,n){let{routesMeta:r}=e,a={},o="/",i=[];for(let c=0;c<r.length;++c){let l=r[c],h=c===r.length-1,d=o==="/"?t:t.slice(o.length)||"/",m=fa({path:l.relativePath,caseSensitive:l.caseSensitive,end:h},d),v=l.route;if(!m)return null;Object.assign(a,m.params),i.push({params:a,pathname:Ke([o,m.pathname]),pathnameBase:wa(Ke([o,m.pathnameBase])),route:v}),m.pathnameBase!=="/"&&(o=Ke([o,m.pathnameBase]))}return i}function fa(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=pa(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let o=a[0],i=o.replace(/(.)\/+$/,"$1"),c=a.slice(1);return{params:r.reduce((h,d,m)=>{let{paramName:v,isOptional:L}=d;if(v==="*"){let j=c[m]||"";i=o.slice(0,o.length-j.length).replace(/(.)\/+$/,"$1")}const b=c[m];return L&&!b?h[v]=void 0:h[v]=(b||"").replace(/%2F/g,"/"),h},{}),pathname:o,pathnameBase:i,pattern:e}}function pa(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Ln(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,c,l)=>(r.push({paramName:c,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function xa(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Ln(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function On(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function ga(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:a=""}=typeof e=="string"?at(e):e;return{pathname:n?n.startsWith("/")?n:ya(n,t):t,search:Sa(r),hash:Na(a)}}function ya(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?n.length>1&&n.pop():a!=="."&&n.push(a)}),n.length>1?n.join("/"):"/"}function us(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function ba(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function ja(e,t){let n=ba(e);return t?n.map((r,a)=>a===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function va(e,t,n,r){r===void 0&&(r=!1);let a;typeof e=="string"?a=at(e):(a=pt({},e),Se(!a.pathname||!a.pathname.includes("?"),us("?","pathname","search",a)),Se(!a.pathname||!a.pathname.includes("#"),us("#","pathname","hash",a)),Se(!a.search||!a.search.includes("#"),us("#","search","hash",a)));let o=e===""||a.pathname==="",i=o?"/":a.pathname,c;if(i==null)c=n;else{let m=t.length-1;if(!r&&i.startsWith("..")){let v=i.split("/");for(;v[0]==="..";)v.shift(),m-=1;a.pathname=v.join("/")}c=m>=0?t[m]:"/"}let l=ga(a,c),h=i&&i!=="/"&&i.endsWith("/"),d=(o||i===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(h||d)&&(l.pathname+="/"),l}const Ke=e=>e.join("/").replace(/\/\/+/g,"/"),wa=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Sa=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Na=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Ea(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Dn=["post","put","patch","delete"];new Set(Dn);const ka=["get",...Dn];new Set(ka);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function xt(){return xt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},xt.apply(this,arguments)}const Ls=u.createContext(null),Ca=u.createContext(null),Ht=u.createContext(null),Wt=u.createContext(null),et=u.createContext({outlet:null,matches:[],isDataRoute:!1}),Fn=u.createContext(null);function Jt(){return u.useContext(Wt)!=null}function Gt(){return Jt()||Se(!1),u.useContext(Wt).location}function Mn(e){u.useContext(Ht).static||u.useLayoutEffect(e)}function We(){let{isDataRoute:e}=u.useContext(et);return e?za():Ta()}function Ta(){Jt()||Se(!1);let e=u.useContext(Ls),{basename:t,future:n,navigator:r}=u.useContext(Ht),{matches:a}=u.useContext(et),{pathname:o}=Gt(),i=JSON.stringify(ja(a,n.v7_relativeSplatPath)),c=u.useRef(!1);return Mn(()=>{c.current=!0}),u.useCallback(function(h,d){if(d===void 0&&(d={}),!c.current)return;if(typeof h=="number"){r.go(h);return}let m=va(h,JSON.parse(i),o,d.relative==="path");e==null&&t!=="/"&&(m.pathname=m.pathname==="/"?t:Ke([t,m.pathname])),(d.replace?r.replace:r.push)(m,d.state,d)},[t,r,i,o,e])}function Pa(){let{matches:e}=u.useContext(et),t=e[e.length-1];return t?t.params:{}}function Ra(e,t){return _a(e,t)}function _a(e,t,n,r){Jt()||Se(!1);let{navigator:a}=u.useContext(Ht),{matches:o}=u.useContext(et),i=o[o.length-1],c=i?i.params:{};i&&i.pathname;let l=i?i.pathnameBase:"/";i&&i.route;let h=Gt(),d;if(t){var m;let x=typeof t=="string"?at(t):t;l==="/"||(m=x.pathname)!=null&&m.startsWith(l)||Se(!1),d=x}else d=h;let v=d.pathname||"/",L=v;if(l!=="/"){let x=l.replace(/^\//,"").split("/");L="/"+v.replace(/^\//,"").split("/").slice(x.length).join("/")}let b=sa(e,{pathname:L}),j=Oa(b&&b.map(x=>Object.assign({},x,{params:Object.assign({},c,x.params),pathname:Ke([l,a.encodeLocation?a.encodeLocation(x.pathname).pathname:x.pathname]),pathnameBase:x.pathnameBase==="/"?l:Ke([l,a.encodeLocation?a.encodeLocation(x.pathnameBase).pathname:x.pathnameBase])})),o,n,r);return t&&j?u.createElement(Wt.Provider,{value:{location:xt({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:Ye.Pop}},j):j}function La(){let e=Ua(),t=Ea(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return u.createElement(u.Fragment,null,u.createElement("h2",null,"Unexpected Application Error!"),u.createElement("h3",{style:{fontStyle:"italic"}},t),n?u.createElement("pre",{style:a},n):null,null)}const Aa=u.createElement(La,null);class Ia extends u.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?u.createElement(et.Provider,{value:this.props.routeContext},u.createElement(Fn.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function $a(e){let{routeContext:t,match:n,children:r}=e,a=u.useContext(Ls);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),u.createElement(et.Provider,{value:t},r)}function Oa(e,t,n,r){var a;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var o;if(!n)return null;if(n.errors)e=n.matches;else if((o=r)!=null&&o.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let i=e,c=(a=n)==null?void 0:a.errors;if(c!=null){let d=i.findIndex(m=>m.route.id&&(c==null?void 0:c[m.route.id])!==void 0);d>=0||Se(!1),i=i.slice(0,Math.min(i.length,d+1))}let l=!1,h=-1;if(n&&r&&r.v7_partialHydration)for(let d=0;d<i.length;d++){let m=i[d];if((m.route.HydrateFallback||m.route.hydrateFallbackElement)&&(h=d),m.route.id){let{loaderData:v,errors:L}=n,b=m.route.loader&&v[m.route.id]===void 0&&(!L||L[m.route.id]===void 0);if(m.route.lazy||b){l=!0,h>=0?i=i.slice(0,h+1):i=[i[0]];break}}}return i.reduceRight((d,m,v)=>{let L,b=!1,j=null,x=null;n&&(L=c&&m.route.id?c[m.route.id]:void 0,j=m.route.errorElement||Aa,l&&(h<0&&v===0?(Ba("route-fallback"),b=!0,x=null):h===v&&(b=!0,x=m.route.hydrateFallbackElement||null)));let P=t.concat(i.slice(0,v+1)),T=()=>{let E;return L?E=j:b?E=x:m.route.Component?E=u.createElement(m.route.Component,null):m.route.element?E=m.route.element:E=d,u.createElement($a,{match:m,routeContext:{outlet:d,matches:P,isDataRoute:n!=null},children:E})};return n&&(m.route.ErrorBoundary||m.route.errorElement||v===0)?u.createElement(Ia,{location:n.location,revalidation:n.revalidation,component:j,error:L,children:T(),routeContext:{outlet:null,matches:P,isDataRoute:!0}}):T()},null)}var Un=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Un||{}),zn=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(zn||{});function Da(e){let t=u.useContext(Ls);return t||Se(!1),t}function Fa(e){let t=u.useContext(Ca);return t||Se(!1),t}function Ma(e){let t=u.useContext(et);return t||Se(!1),t}function Bn(e){let t=Ma(),n=t.matches[t.matches.length-1];return n.route.id||Se(!1),n.route.id}function Ua(){var e;let t=u.useContext(Fn),n=Fa(),r=Bn();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function za(){let{router:e}=Da(Un.UseNavigateStable),t=Bn(zn.UseNavigateStable),n=u.useRef(!1);return Mn(()=>{n.current=!0}),u.useCallback(function(a,o){o===void 0&&(o={}),n.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,xt({fromRouteId:t},o)))},[e,t])}const Gs={};function Ba(e,t,n){Gs[e]||(Gs[e]=!0)}function qa(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function De(e){Se(!1)}function Ha(e){let{basename:t="/",children:n=null,location:r,navigationType:a=Ye.Pop,navigator:o,static:i=!1,future:c}=e;Jt()&&Se(!1);let l=t.replace(/^\/*/,"/"),h=u.useMemo(()=>({basename:l,navigator:o,static:i,future:xt({v7_relativeSplatPath:!1},c)}),[l,c,o,i]);typeof r=="string"&&(r=at(r));let{pathname:d="/",search:m="",hash:v="",state:L=null,key:b="default"}=r,j=u.useMemo(()=>{let x=On(d,l);return x==null?null:{location:{pathname:x,search:m,hash:v,state:L,key:b},navigationType:a}},[l,d,m,v,L,b,a]);return j==null?null:u.createElement(Ht.Provider,{value:h},u.createElement(Wt.Provider,{children:n,value:j}))}function Wa(e){let{children:t,location:n}=e;return Ra(ws(t),n)}new Promise(()=>{});function ws(e,t){t===void 0&&(t=[]);let n=[];return u.Children.forEach(e,(r,a)=>{if(!u.isValidElement(r))return;let o=[...t,a];if(r.type===u.Fragment){n.push.apply(n,ws(r.props.children,o));return}r.type!==De&&Se(!1),!r.props.index||!r.props.children||Se(!1);let i={id:r.props.id||o.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(i.children=ws(r.props.children,o)),n.push(i)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const Ja="6";try{window.__reactRouterVersion=Ja}catch{}const Ga="startTransition",Vs=Tr[Ga];function Va(e){let{basename:t,children:n,future:r,window:a}=e,o=u.useRef();o.current==null&&(o.current=Zr({window:a,v5Compat:!0}));let i=o.current,[c,l]=u.useState({action:i.action,location:i.location}),{v7_startTransition:h}=r||{},d=u.useCallback(m=>{h&&Vs?Vs(()=>l(m)):l(m)},[l,h]);return u.useLayoutEffect(()=>i.listen(d),[i,d]),u.useEffect(()=>qa(r),[r]),u.createElement(Ha,{basename:t,children:n,location:c.location,navigationType:c.action,navigator:i,future:r})}var Ys;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Ys||(Ys={}));var Xs;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Xs||(Xs={}));var Vt={},qn={exports:{}};(function(e){function t(n){return n&&n.__esModule?n:{default:n}}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})(qn);var Yt=qn.exports,Xt={};Object.defineProperty(Xt,"__esModule",{value:!0});Xt.default=void 0;var Ya={items_per_page:"条/页",jump_to:"跳至",jump_to_confirm:"确定",page:"页",prev_page:"上一页",next_page:"下一页",prev_5:"向前 5 页",next_5:"向后 5 页",prev_3:"向前 3 页",next_3:"向后 3 页",page_size:"页码"};Xt.default=Ya;var Kt={},yt={},Qt={},Hn={exports:{}},Wn={exports:{}},Jn={exports:{}},Gn={exports:{}};(function(e){function t(n){"@babel/helpers - typeof";return e.exports=t=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(r){return typeof r}:function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})(Gn);var Vn=Gn.exports,Yn={exports:{}};(function(e){var t=Vn.default;function n(r,a){if(t(r)!="object"||!r)return r;var o=r[Symbol.toPrimitive];if(o!==void 0){var i=o.call(r,a||"default");if(t(i)!="object")return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return(a==="string"?String:Number)(r)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports})(Yn);var Xa=Yn.exports;(function(e){var t=Vn.default,n=Xa;function r(a){var o=n(a,"string");return t(o)=="symbol"?o:o+""}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports})(Jn);var Ka=Jn.exports;(function(e){var t=Ka;function n(r,a,o){return(a=t(a))in r?Object.defineProperty(r,a,{value:o,enumerable:!0,configurable:!0,writable:!0}):r[a]=o,r}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports})(Wn);var Qa=Wn.exports;(function(e){var t=Qa;function n(a,o){var i=Object.keys(a);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(a);o&&(c=c.filter(function(l){return Object.getOwnPropertyDescriptor(a,l).enumerable})),i.push.apply(i,c)}return i}function r(a){for(var o=1;o<arguments.length;o++){var i=arguments[o]!=null?arguments[o]:{};o%2?n(Object(i),!0).forEach(function(c){t(a,c,i[c])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(i)):n(Object(i)).forEach(function(c){Object.defineProperty(a,c,Object.getOwnPropertyDescriptor(i,c))})}return a}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports})(Hn);var Za=Hn.exports,Zt={};Object.defineProperty(Zt,"__esModule",{value:!0});Zt.commonLocale=void 0;Zt.commonLocale={yearFormat:"YYYY",dayFormat:"D",cellMeridiemFormat:"A",monthBeforeYear:!0};var eo=Yt.default;Object.defineProperty(Qt,"__esModule",{value:!0});Qt.default=void 0;var Ks=eo(Za),to=Zt,so=(0,Ks.default)((0,Ks.default)({},to.commonLocale),{},{locale:"zh_CN",today:"今天",now:"此刻",backToToday:"返回今天",ok:"确定",timeSelect:"选择时间",dateSelect:"选择日期",weekSelect:"选择周",clear:"清除",week:"周",month:"月",year:"年",previousMonth:"上个月 (翻页上键)",nextMonth:"下个月 (翻页下键)",monthSelect:"选择月份",yearSelect:"选择年份",decadeSelect:"选择年代",previousYear:"上一年 (Control键加左方向键)",nextYear:"下一年 (Control键加右方向键)",previousDecade:"上一年代",nextDecade:"下一年代",previousCentury:"上一世纪",nextCentury:"下一世纪",yearFormat:"YYYY年",cellDateFormat:"D",monthBeforeYear:!1});Qt.default=so;var bt={};Object.defineProperty(bt,"__esModule",{value:!0});bt.default=void 0;const no={placeholder:"请选择时间",rangePlaceholder:["开始时间","结束时间"]};bt.default=no;var Xn=Yt.default;Object.defineProperty(yt,"__esModule",{value:!0});yt.default=void 0;var ro=Xn(Qt),ao=Xn(bt);const Kn={lang:Object.assign({placeholder:"请选择日期",yearPlaceholder:"请选择年份",quarterPlaceholder:"请选择季度",monthPlaceholder:"请选择月份",weekPlaceholder:"请选择周",rangePlaceholder:["开始日期","结束日期"],rangeYearPlaceholder:["开始年份","结束年份"],rangeMonthPlaceholder:["开始月份","结束月份"],rangeQuarterPlaceholder:["开始季度","结束季度"],rangeWeekPlaceholder:["开始周","结束周"]},ro.default),timePickerLocale:Object.assign({},ao.default)};Kn.lang.ok="确定";yt.default=Kn;var oo=Yt.default;Object.defineProperty(Kt,"__esModule",{value:!0});Kt.default=void 0;var io=oo(yt);Kt.default=io.default;var es=Yt.default;Object.defineProperty(Vt,"__esModule",{value:!0});Vt.default=void 0;var lo=es(Xt),co=es(Kt),uo=es(yt),ho=es(bt);const Ie="${label}不是一个有效的${type}",mo={locale:"zh-cn",Pagination:lo.default,DatePicker:uo.default,TimePicker:ho.default,Calendar:co.default,global:{placeholder:"请选择",close:"关闭"},Table:{filterTitle:"筛选",filterConfirm:"确定",filterReset:"重置",filterEmptyText:"无筛选项",filterCheckAll:"全选",filterSearchPlaceholder:"在筛选项中搜索",emptyText:"暂无数据",selectAll:"全选当页",selectInvert:"反选当页",selectNone:"清空所有",selectionAll:"全选所有",sortTitle:"排序",expand:"展开行",collapse:"关闭行",triggerDesc:"点击降序",triggerAsc:"点击升序",cancelSort:"取消排序"},Modal:{okText:"确定",cancelText:"取消",justOkText:"知道了"},Tour:{Next:"下一步",Previous:"上一步",Finish:"结束导览"},Popconfirm:{cancelText:"取消",okText:"确定"},Transfer:{titles:["",""],searchPlaceholder:"请输入搜索内容",itemUnit:"项",itemsUnit:"项",remove:"删除",selectCurrent:"全选当页",removeCurrent:"删除当页",selectAll:"全选所有",deselectAll:"取消全选",removeAll:"删除全部",selectInvert:"反选当页"},Upload:{uploading:"文件上传中",removeFile:"删除文件",uploadError:"上传错误",previewFile:"预览文件",downloadFile:"下载文件"},Empty:{description:"暂无数据"},Icon:{icon:"图标"},Text:{edit:"编辑",copy:"复制",copied:"复制成功",expand:"展开",collapse:"收起"},Form:{optional:"（可选）",defaultValidateMessages:{default:"字段验证错误${label}",required:"请输入${label}",enum:"${label}必须是其中一个[${enum}]",whitespace:"${label}不能为空字符",date:{format:"${label}日期格式无效",parse:"${label}不能转换为日期",invalid:"${label}是一个无效日期"},types:{string:Ie,method:Ie,array:Ie,object:Ie,number:Ie,date:Ie,boolean:Ie,integer:Ie,float:Ie,regexp:Ie,email:Ie,url:Ie,hex:Ie},string:{len:"${label}须为${len}个字符",min:"${label}最少${min}个字符",max:"${label}最多${max}个字符",range:"${label}须在${min}-${max}字符之间"},number:{len:"${label}必须等于${len}",min:"${label}最小值为${min}",max:"${label}最大值为${max}",range:"${label}须在${min}-${max}之间"},array:{len:"须为${len}个${label}",min:"最少${min}个${label}",max:"最多${max}个${label}",range:"${label}数量须在${min}-${max}之间"},pattern:{mismatch:"${label}与模式不匹配${pattern}"}}},Image:{preview:"预览"},QRCode:{expired:"二维码过期",refresh:"点击刷新",scanned:"已扫描"},ColorPicker:{presetEmpty:"暂无",transparent:"无色",singleColor:"单色",gradientColor:"渐变色"}};Vt.default=mo;var fo=Vt;const po=Pr(fo),xo=()=>{const e=Gt();u.useEffect(()=>{const t=e.pathname.split("/").filter(o=>o.length>0);let n="";if(t[0]==="game"&&t[1]==="level-1"&&t.length===2){document.body.style.backgroundImage="",document.documentElement.style.backgroundImage="";return}if(t[0]==="game"&&t.length>=3){const o=t[1],i=t[2];if(i.startsWith("L")&&i.includes("-"))n=i;else if(o.startsWith("level-")&&i.startsWith("stage-")){const c=o.replace("level-",""),l=i.replace("stage-","");n=`L${c}-${l}`}else o.startsWith("L")&&!o.includes("-")?n=`${o}-${i}`:o.startsWith("level-")&&i.startsWith("L")&&(n=i)}const r=o=>{document.body.style.backgroundImage=`url('${o}')`,document.documentElement.style.backgroundImage=`url('${o}')`};return n?(async o=>{if(o.startsWith("L1-")){const i=["jpg","jpeg","png"];for(const c of i)try{const l=`/${o}/background.${c}`,h=new Image;await new Promise((d,m)=>{h.onload=d,h.onerror=m,h.src=l}),r(l),console.log("✅ 成功加载L1关卡背景:",l);return}catch{continue}r("/background.jpg"),console.log("⚠️ L1关卡背景不存在，使用统一背景: /background.jpg")}else r("/background.jpg"),console.log("✅ 使用统一背景:","/background.jpg")})(n):(r("/background.jpg"),console.log("✅ 非游戏页面使用统一背景:","/background.jpg")),()=>{}},[e.pathname])},{Title:Tt,Text:Ee}=Le,go=()=>{const e=We(),t={currentLevel:2,completedStages:5,overallProgress:42,consecutiveDays:5};return s.jsx("div",{className:"min-h-screen",style:{background:"transparent"},children:s.jsxs("div",{className:"max-w-6xl mx-auto px-4 pt-8 pb-12",children:[s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"mb-16",children:s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto",children:[s.jsx(re.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(ie,{className:"bg-gradient-to-br from-orange-400 to-pink-500 border-0 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer h-48",onClick:()=>e("/game"),children:s.jsxs("div",{className:"text-center text-white h-full flex flex-col justify-center",children:[s.jsx(ze,{className:"text-6xl mb-4"}),s.jsx(Tt,{level:2,className:"text-white mb-2",children:"开始绘画"}),s.jsx(Ee,{className:"text-white text-xl opacity-90",children:"让创意自由流淌"})]})})}),s.jsx(re.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(ie,{className:"bg-white border-2 border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer h-48",onClick:()=>e("/gallery"),children:s.jsxs("div",{className:"text-center h-full flex flex-col justify-center",children:[s.jsx(nt,{className:"text-6xl text-purple-500 mb-4"}),s.jsx(Tt,{level:2,className:"text-gray-700 mb-2",children:"作品画廊"}),s.jsx(Ee,{className:"text-gray-600 text-xl",children:"欣赏美好作品"})]})})})]})}),s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},className:"mb-16",children:s.jsx(ie,{className:"bg-white shadow-soft border-0 max-w-4xl mx-auto",children:s.jsxs("div",{className:"text-center p-8",children:[s.jsxs(Tt,{level:2,className:"text-gray-700 mb-8",children:[s.jsx(Dt,{className:"mr-3 text-yellow-500"}),"您的学习进度"]}),s.jsxs("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-8 mb-8",children:[s.jsxs("div",{className:"flex justify-between items-center mb-6",children:[s.jsx(Ee,{className:"text-2xl text-gray-700",children:"艺术之旅进度"}),s.jsxs(Ee,{className:"text-4xl font-bold text-purple-600",children:[t.overallProgress,"%"]})]}),s.jsx(jn,{percent:t.overallProgress,strokeColor:{"0%":"#3b82f6","50%":"#8b5cf6","100%":"#ec4899"},strokeWidth:20,className:"mb-4"}),s.jsx(Ee,{className:"text-xl text-gray-600",children:"每一步都是成长，每一画都是进步 ✨"})]}),s.jsxs(Ps,{gutter:24,children:[s.jsx(ht,{span:8,children:s.jsxs("div",{className:"text-center p-6 bg-gradient-to-br from-orange-100 to-orange-200 rounded-xl",children:[s.jsx("div",{className:"text-5xl mb-3",children:"🎨"}),s.jsx(Ee,{className:"text-4xl font-bold text-orange-600 block",children:t.currentLevel}),s.jsx(Ee,{className:"text-xl text-gray-600",children:"当前级别"})]})}),s.jsx(ht,{span:8,children:s.jsxs("div",{className:"text-center p-6 bg-gradient-to-br from-green-100 to-green-200 rounded-xl",children:[s.jsx("div",{className:"text-5xl mb-3",children:"⭐"}),s.jsx(Ee,{className:"text-4xl font-bold text-green-600 block",children:t.completedStages}),s.jsx(Ee,{className:"text-xl text-gray-600",children:"完成阶段"})]})}),s.jsx(ht,{span:8,children:s.jsxs("div",{className:"text-center p-6 bg-gradient-to-br from-pink-100 to-pink-200 rounded-xl",children:[s.jsx("div",{className:"text-5xl mb-3",children:"🔥"}),s.jsx(Ee,{className:"text-4xl font-bold text-pink-600 block",children:t.consecutiveDays}),s.jsx(Ee,{className:"text-xl text-gray-600",children:"连续天数"})]})})]})]})})}),s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:1},className:"mb-16",children:s.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto",children:[s.jsx(re.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsxs(ie,{className:"text-center cursor-pointer bg-gradient-to-br from-blue-50 to-blue-100 border-0 shadow-soft hover:shadow-lg transition-all duration-300 h-32",onClick:()=>e("/profile"),children:[s.jsx(vn,{className:"text-4xl text-blue-500 mb-2"}),s.jsx(Ee,{className:"text-lg font-medium text-gray-700",children:"个人档案"})]})}),s.jsx(re.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsxs(ie,{className:"text-center cursor-pointer bg-gradient-to-br from-yellow-50 to-yellow-100 border-0 shadow-soft hover:shadow-lg transition-all duration-300 h-32",onClick:()=>e("/leaderboard"),children:[s.jsx(Dt,{className:"text-4xl text-yellow-500 mb-2"}),s.jsx(Ee,{className:"text-lg font-medium text-gray-700",children:"排行榜"})]})}),s.jsx(re.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsxs(ie,{className:"text-center cursor-pointer bg-gradient-to-br from-purple-50 to-purple-100 border-0 shadow-soft hover:shadow-lg transition-all duration-300 h-32",onClick:()=>e("/achievements"),children:[s.jsx(Ft,{className:"text-4xl text-purple-500 mb-2"}),s.jsx(Ee,{className:"text-lg font-medium text-gray-700",children:"成就"})]})}),s.jsx(re.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsxs(ie,{className:"text-center cursor-pointer bg-gradient-to-br from-green-50 to-green-100 border-0 shadow-soft hover:shadow-lg transition-all duration-300 h-32",onClick:()=>e("/settings"),children:[s.jsx(Rs,{className:"text-4xl text-green-500 mb-2"}),s.jsx(Ee,{className:"text-lg font-medium text-gray-700",children:"设置"})]})})]})}),s.jsx(re.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:1,delay:1.2},className:"text-center mt-16 py-12",children:s.jsx("div",{className:"max-w-3xl mx-auto",children:s.jsxs("div",{className:"bg-gradient-to-r from-orange-100 via-pink-100 to-purple-100 rounded-2xl p-8 shadow-soft",children:[s.jsx("div",{className:"flex justify-center mb-6",children:s.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-orange-400 via-pink-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg",children:s.jsx(Rr,{className:"text-2xl text-white"})})}),s.jsx(Tt,{level:2,className:"text-3xl mb-4",children:s.jsx("span",{className:"bg-gradient-to-r from-orange-500 via-pink-500 to-purple-600 bg-clip-text text-transparent",children:"让艺术点亮记忆，让创造温暖心灵"})}),s.jsx(Ee,{className:"text-xl text-gray-700 mb-6",children:"每一笔都是希望，每一画都是奇迹"}),s.jsx(Ee,{className:"text-lg text-gray-600",children:"奇迹创造者团队 · 用心陪伴您的艺术之旅 ✨"})]})})})]})})},ts=[{id:"level-1",title:"线条启蒙",description:"从基础的线条开始，培养手部协调能力",icon:"📈",difficulty:1,stages:[{id:"L1-1",title:"自由画线",description:"随意画线，创作属于您的艺术作品",type:"free-draw"},{id:"L1-2",title:"匀速直线",description:"跟随引导线条，提高绘画精准度",type:"image-trace"},{id:"L1-3",title:"匀速线条组合",description:"从真实图片中抽取线条进行描画练习",type:"image-trace"},{id:"L1-4",title:"曲线消除",description:"绘制曲线和复杂图形",type:"image-trace"},{id:"L1-5",title:"直线图形",description:"绘制三角形、正方形、矩形等直线图形",type:"shape-straight"}]},{id:"level-2",title:"立体空间",description:"从二维图形到三维立体，提升空间想象力",icon:"📦",difficulty:2,stages:[{id:"L2-1",title:"立体图形",description:"绘制锥形、立方体、圆柱体等三维图形",type:"coming-soon",comingSoon:!0},{id:"L2-2",title:"色彩填充",description:"为几何图形填色，学习色系和色谱搭配",type:"coming-soon",comingSoon:!0},{id:"L2-3",title:"质感画笔",description:"复杂曲线描边，选择不同质感的画笔填色",type:"coming-soon",comingSoon:!0},{id:"L2-4",title:"阴影效果",description:"学习光影关系，添加阴影效果",type:"coming-soon",comingSoon:!0}]},{id:"level-3",title:"画面构图",description:"通过引导线条，完成完整的艺术画面",icon:"🖼️",difficulty:3,stages:[{id:"L3-1",title:"抽象艺术",description:"用抽象线条和色块创作现代艺术作品",type:"coming-soon",comingSoon:!0},{id:"L3-2",title:"几何静物",description:"绘制几何形状组成的静物画",type:"coming-soon",comingSoon:!0},{id:"L3-3",title:"风景艺术",description:"创作简单的风景画作品",type:"coming-soon",comingSoon:!0},{id:"L3-4",title:"肖像艺术",description:"学习人物肖像的基本绘制",type:"coming-soon",comingSoon:!0}]},{id:"level-4",title:"智能创作",description:"上传照片，AI辅助创作个性化艺术作品",icon:"🤖",difficulty:4,stages:[{id:"L4-1",title:"照片描边",description:"上传照片，提取轮廓进行描边练习",type:"coming-soon",comingSoon:!0},{id:"L4-2",title:"风格渲染",description:"选择不同艺术风格，AI辅助渲染作品",type:"coming-soon",comingSoon:!0},{id:"L4-3",title:"AI协作",description:"与AI协作，创作独特的个人艺术作品",type:"coming-soon",comingSoon:!0},{id:"L4-4",title:"创意模式",description:"自由创作模式，发挥无限想象力",type:"coming-soon",comingSoon:!0}]}],yo=()=>ts.flatMap(e=>e.stages.map(t=>t.id)),bo=()=>ts.map(e=>e.id),{Title:Qs,Text:Zs}=Le,jo=({onSelectLevel:e,unlockedLevels:t=["level-1"],onResetProgress:n})=>{const r=ts.map(i=>({...i,icon:i.id==="level-1"?s.jsx(_r,{className:"text-4xl"}):i.id==="level-2"?s.jsx(Lr,{className:"text-4xl"}):i.id==="level-3"?s.jsx(nt,{className:"text-4xl"}):i.id==="level-4"?s.jsx(Ft,{className:"text-4xl"}):s.jsx(ze,{className:"text-4xl"})})),a=i=>{switch(i){case 1:return"text-green-600 bg-green-100";case 2:return"text-blue-600 bg-blue-100";case 3:return"text-purple-600 bg-purple-100";case 4:return"text-orange-600 bg-orange-100";default:return"text-gray-600 bg-gray-100"}},o=i=>{switch(i){case 1:return"入门";case 2:return"初级";case 3:return"中级";case 4:return"高级";default:return"未知"}};return s.jsxs("div",{className:"max-w-6xl mx-auto px-4 py-8",children:[s.jsxs(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-center mb-8",children:[s.jsx(Qs,{level:1,className:"text-purple-600 mb-4",children:"选择游戏级别"}),s.jsx(Zs,{className:"text-xl text-gray-600",children:"从简单的线条开始，逐步提升您的绘画技能"})]}),s.jsx(Ps,{gutter:[24,24],children:r.map((i,c)=>{const l=t.includes(i.id);return s.jsx(ht,{xs:24,lg:12,children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:c*.1},children:s.jsxs(ie,{className:`h-full shadow-lg transition-all duration-300 ${l?"hover:shadow-xl cursor-pointer":"opacity-60 cursor-not-allowed"}`,bodyStyle:{padding:"24px"},children:[s.jsxs("div",{className:"text-center mb-6",children:[s.jsxs("div",{className:`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-white relative ${l?"bg-gradient-to-br from-purple-400 to-purple-600":"bg-gradient-to-br from-gray-400 to-gray-500"}`,children:[!l&&s.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:s.jsx(ft,{className:"text-2xl"})}),l&&i.icon]}),s.jsx(Qs,{level:3,className:`mb-2 ${l?"":"text-gray-500"}`,children:i.title}),s.jsxs("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium mb-3 ${l?a(i.difficulty):"bg-gray-100 text-gray-500"}`,children:[s.jsx(Ft,{className:"mr-1"}),o(i.difficulty)]}),s.jsx(Zs,{className:`block ${l?"text-gray-600":"text-gray-500"}`,children:l?i.description:"完成前面的级别来解锁"})]}),s.jsx("div",{className:"mt-6",children:s.jsx(re.div,{whileHover:l?{scale:1.02}:{},whileTap:l?{scale:.98}:{},children:s.jsx(H,{type:l?"primary":"default",size:"large",icon:l?s.jsx(ze,{}):s.jsx(ft,{}),onClick:()=>l&&e(i.id),disabled:!l,className:"w-full h-12 text-lg",children:l?"进入级别":"级别锁定"})})})]})})},i.id)})}),n&&s.jsx("div",{className:"text-center mt-8",children:s.jsx(wn,{title:"重置游戏进度",description:"确定要重置所有游戏进度吗？这将清除所有解锁的级别。",onConfirm:n,okText:"确定",cancelText:"取消",children:s.jsx(H,{icon:s.jsx(rt,{}),size:"small",className:"text-gray-500 hover:text-gray-700",children:"重置进度"})})})]})},As={get allLevels(){return bo()},get allStages(){return yo()}},vo=()=>As.allLevels,wo=()=>As.allStages,Qn=e=>As.allStages.includes(e),{Title:hs,Text:en}=Le,So=({level:e,onSelectStage:t,onBack:n,unlockedStages:r=[]})=>{if(e.id==="level-1")return s.jsx(No,{level:e,onSelectStage:t,onBack:n,unlockedStages:r});const a=c=>{switch(c){case 1:return"text-green-600 bg-green-100";case 2:return"text-blue-600 bg-blue-100";case 3:return"text-purple-600 bg-purple-100";case 4:return"text-orange-600 bg-orange-100";default:return"text-gray-600 bg-gray-100"}},o=c=>{switch(c){case 1:return"入门";case 2:return"初级";case 3:return"中级";case 4:return"高级";default:return"未知"}},i=(c,l)=>Qn(c);return s.jsx("div",{className:"max-w-6xl mx-auto px-4 py-8",children:s.jsxs(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[s.jsx(ie,{className:"mb-6 shadow-sm",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg flex items-center justify-center text-white text-2xl",children:(typeof e.icon=="string",e.icon)}),s.jsxs("div",{children:[s.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[s.jsx(hs,{level:2,className:"mb-0 text-purple-600",children:e.title}),s.jsxs("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${a(e.difficulty)}`,children:[s.jsx(Ft,{className:"mr-1"}),o(e.difficulty)]})]}),s.jsx(en,{className:"text-lg text-gray-600",children:e.description})]})]}),s.jsx(H,{icon:s.jsx(He,{}),onClick:n,size:"large",className:"h-12 px-6",children:"返回级别选择"})]})}),s.jsx(Ps,{gutter:[24,24],children:e.stages.map((c,l)=>{const h=i(c.id),d=!c.comingSoon&&h;return s.jsx(ht,{xs:24,sm:12,lg:8,children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:l*.1},whileHover:d?{scale:1.02}:{},whileTap:d?{scale:.98}:{},children:s.jsx(ie,{className:`h-full shadow-lg transition-all duration-300 ${d?"hover:shadow-xl cursor-pointer":"opacity-60 cursor-not-allowed"}`,bodyStyle:{padding:"24px"},onClick:()=>d&&t(c.id),children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-white ${d?"bg-gradient-to-br from-blue-400 to-blue-600":"bg-gradient-to-br from-gray-400 to-gray-500"}`,children:d?s.jsx(ze,{className:"text-2xl"}):s.jsx(ft,{className:"text-2xl"})}),s.jsxs(hs,{level:4,className:`mb-3 ${d?"":"text-gray-500"}`,children:[c.title,c.comingSoon&&s.jsx("div",{className:"mt-2",children:s.jsx("span",{className:"text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded-full",children:"即将推出"})})]}),s.jsx(en,{className:`block mb-6 ${d?"text-gray-600":"text-gray-500"}`,children:c.description}),s.jsx(H,{type:d?"primary":"default",size:"large",icon:d?s.jsx(ze,{}):s.jsx(ft,{}),disabled:!d,className:"w-full h-12",children:d?"开始练习":c.comingSoon?"即将推出":"完成前面练习解锁"})]})})})},c.id)})}),s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"mt-8",children:s.jsx(ie,{className:"bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200",children:s.jsxs("div",{className:"text-center",children:[s.jsx(hs,{level:4,className:"text-blue-600 mb-3",children:"💡 学习建议"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-700",children:[s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-blue-400 rounded-full"}),s.jsx("span",{children:"按顺序完成练习效果更佳"})]}),s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-purple-400 rounded-full"}),s.jsx("span",{children:"每次练习时间不宜过长"})]}),s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-pink-400 rounded-full"}),s.jsx("span",{children:"重复练习有助于提高技能"})]})]})]})})})]})})},No=({level:e,onSelectStage:t,onBack:n,unlockedStages:r=[]})=>{const a=window.location.search.includes("debug=true"),o=(l,h)=>Qn(l),i=[{id:"L1-1",x:"29%",y:"46%",number:1},{id:"L1-2",x:"35%",y:"28%",number:2},{id:"L1-3",x:"48%",y:"19%",number:3},{id:"L1-4",x:"61%",y:"29%",number:4},{id:"L1-5",x:"67%",y:"47%",number:5}],c=l=>{e.stages.findIndex(d=>d.id===l),o(l)&&t(l)};return s.jsx("div",{className:"min-h-screen relative overflow-hidden",children:s.jsx("div",{className:"absolute inset-0 flex items-center justify-center",style:{backgroundColor:"#f0f0f0"},children:s.jsxs("div",{className:"relative",children:[s.jsx("img",{src:"/background-l1.jpg",alt:"Level 1 Background",className:"h-screen w-auto object-contain",style:{maxWidth:"none"}}),s.jsxs("div",{className:"absolute inset-0",children:[s.jsx("div",{className:"absolute top-8 left-8 z-10",children:s.jsx(H,{icon:s.jsx(He,{}),onClick:n,size:"large",className:"h-12 px-6 bg-white/90 backdrop-blur-sm hover:bg-white",children:"返回级别选择"})}),i.map((l,h)=>{const d=e.stages.find(v=>v.id===l.id),m=o(l.id);return d?s.jsxs(re.div,{className:"absolute z-20",style:{left:l.x,top:l.y,transform:"translate(-50%, -50%)"},initial:{opacity:0,scale:0},animate:{opacity:1,scale:1},transition:{duration:.6,delay:h*.1},whileHover:m?{scale:1.1}:{},whileTap:m?{scale:.95}:{},children:[s.jsx("div",{className:`
                                            w-20 h-20 rounded-full flex items-center justify-center text-white text-xl font-bold
                                            cursor-pointer transition-all duration-300 shadow-lg
                                            ${m?"bg-gradient-to-br from-yellow-400 to-yellow-600 hover:shadow-xl":"bg-gradient-to-br from-gray-400 to-gray-500 cursor-not-allowed opacity-60"}
                                            ${a?"ring-2 ring-red-500 ring-opacity-50":""}
                                        `,onClick:()=>c(l.id),title:m?`${d.title} - 点击开始`:`${d.title} - 需要完成前面的关卡`,children:m?l.number:s.jsx(ft,{className:"text-lg"})}),a&&s.jsxs("div",{className:"absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs bg-red-500 text-white px-1 rounded",children:[l.x,", ",l.y]}),s.jsx("div",{className:"absolute top-full mt-2 left-1/2 transform -translate-x-1/2 whitespace-nowrap",children:s.jsx("div",{className:"bg-black/70 text-white px-2 py-1 rounded text-sm",children:d.title})})]},l.id):null})]})]})})})},Zn=({width:e=800,height:t=600,onSave:n,disabled:r=!1,backgroundImage:a,showReference:o=!1,hideCanvas:i=!1,onClearRef:c,isCompleted:l=!1,onPathsChange:h})=>{const d=u.useRef(null),[m,v]=u.useState(!1),[L,b]=u.useState(5),[j,x]=u.useState("#000000"),[P,T]=u.useState([]),[E,q]=u.useState([]),[Q,oe]=u.useState(!1),[te,xe]=u.useState(!1),w=l,O=u.useCallback(y=>{const I=d.current;if(!I)return{x:0,y:0};const W=I.getBoundingClientRect(),B=I.width/W.width,ee=I.height/W.height;let X,le;if("touches"in y){const ge=y.touches[0]||y.changedTouches[0];X=ge.clientX,le=ge.clientY}else X=y.clientX,le=y.clientY;const je=(X-W.left)*B,Ae=(le-W.top)*ee;return{x:je,y:Ae}},[]),N=u.useCallback(y=>{if(r||w)return;y.preventDefault(),v(!0);const I=O(y);q([I])},[r,w,O]),k=u.useCallback(y=>{if(!m||r||w)return;y.preventDefault();const I=O(y);q(W=>[...W,I])},[m,r,w,O]),G=u.useCallback(()=>{if(!(!m||r||w)){if(v(!1),E.length>1){const y={points:E,color:j,size:L};T(I=>{const W=[...I,y];return oe(!0),xe(!1),h==null||h(W),W})}q([])}},[m,r,w,E,j,L]),J=u.useCallback((y,I,W,B)=>{if(!(I.length<2)){y.strokeStyle=W,y.lineWidth=B,y.lineCap="round",y.lineJoin="round",y.beginPath(),y.moveTo(I[0].x,I[0].y);for(let ee=1;ee<I.length;ee++)y.lineTo(I[ee].x,I[ee].y);y.stroke()}},[]),Z=u.useCallback(()=>{const y=d.current;if(!y)return;const I=y.getContext("2d",{alpha:!0});if(I)if(y.style.backgroundColor="rgba(0,0,0,0)",y.style.background="none",I.clearRect(0,0,y.width,y.height),I.globalCompositeOperation="source-over",a&&o){const W=new Image;W.onload=()=>{I.globalAlpha=.3,I.drawImage(W,0,0,y.width,y.height),I.globalAlpha=1,P.forEach(B=>{J(I,B.points,B.color,B.size)}),E.length>1&&J(I,E,j,L)},W.src=a}else P.forEach(W=>{J(I,W.points,W.color,W.size)}),E.length>1&&J(I,E,j,L)},[P,E,j,L,a,o,J]),de=u.useCallback(()=>{const y=d.current;if(y){const I=y.getContext("2d",{alpha:!0});I&&I.clearRect(0,0,y.width,y.height)}T([]),q([]),oe(!1),xe(!1),h==null||h([])},[h]),Y=u.useCallback(()=>{de()},[de]),pe=u.useCallback(()=>{const y=d.current;if(!y)return;const I=y.toDataURL("image/png");n==null||n(I),xe(!0),K.success("画作已保存！")},[n]);return u.useEffect(()=>{const y=d.current;if(y){const I=y.getContext("2d",{alpha:!0});I&&(I.globalCompositeOperation="source-over",y.style.backgroundColor="rgba(0,0,0,0)",y.style.background="none",I.clearRect(0,0,y.width,y.height),y.setAttribute("style","background-color: rgba(0,0,0,0) !important; background: none !important; touch-action: none; border-radius: 8px; display: block;"),y.style.setProperty("background-color","rgba(0,0,0,0)","important"),y.style.setProperty("background","none","important"),console.log("Canvas initialized with alpha channel"))}},[]),u.useEffect(()=>{Z()},[Z]),u.useEffect(()=>{const y=d.current;if(y){y.width=e,y.height=t,y.style.width=`${e}px`,y.style.height=`${t}px`;const I=ee=>{if(r||w)return;ee.preventDefault(),ee.stopPropagation();const X=ee.touches[0],le=y.getBoundingClientRect(),je=y.width/le.width,Ae=y.height/le.height,ge={x:(X.clientX-le.left)*je,y:(X.clientY-le.top)*Ae};v(!0),q([ge])},W=ee=>{if(!m||r||w)return;ee.preventDefault(),ee.stopPropagation();const X=ee.touches[0],le=y.getBoundingClientRect(),je=y.width/le.width,Ae=y.height/le.height,ge={x:(X.clientX-le.left)*je,y:(X.clientY-le.top)*Ae};q(Me=>[...Me,ge])},B=ee=>{if(ee.preventDefault(),ee.stopPropagation(),!(!m||r||w)){if(v(!1),E.length>1){const X={points:E,color:j,size:L};T(le=>{const je=[...le,X];return oe(!0),xe(!1),h==null||h(je),je})}q([])}};return y.addEventListener("touchstart",I,{passive:!1}),y.addEventListener("touchmove",W,{passive:!1}),y.addEventListener("touchend",B,{passive:!1}),Z(),()=>{y.removeEventListener("touchstart",I),y.removeEventListener("touchmove",W),y.removeEventListener("touchend",B)}}},[e,t,Z,r,w,m,E,j,L,h]),u.useEffect(()=>{c&&c(de)},[c,de]),u.useEffect(()=>{if(!i&&P.length===0){const y=d.current;if(y){const I=y.getContext("2d",{alpha:!0});I&&I.clearRect(0,0,y.width,y.height)}}},[i,P]),s.jsxs("div",{className:"flex flex-col items-center gap-4",style:{backgroundColor:"transparent"},children:[s.jsxs("div",{className:"flex flex-wrap items-center gap-4 p-4 bg-white rounded-lg shadow-sm border",children:[s.jsxs(fe,{children:[s.jsx("span",{className:"text-sm font-medium",children:"画笔大小:"}),s.jsx(Sn,{min:1,max:20,value:L,onChange:b,style:{width:100},disabled:w}),s.jsxs("span",{className:"text-sm text-gray-500",children:[L,"px"]})]}),s.jsxs(fe,{children:[s.jsx("span",{className:"text-sm font-medium",children:"颜色:"}),s.jsx(Ar,{value:j,onChange:y=>x(y.toHexString()),showText:!0,disabled:w})]}),s.jsxs(fe,{children:[s.jsx(H,{icon:s.jsx(Nn,{}),onClick:Y,disabled:!Q||te||w,title:"清空",children:"清空"}),s.jsx(H,{icon:s.jsx(En,{}),onClick:pe,type:"primary",disabled:!Q||te||w,title:"保存",children:"保存"})]})]}),!i&&s.jsx("div",{style:{backgroundColor:"rgba(0,0,0,0)",background:"none",position:"relative",borderRadius:"8px",border:"2px solid #e5e7eb",overflow:"hidden"},children:s.jsx("canvas",{ref:d,width:e,height:t,className:w?"cursor-not-allowed opacity-60":"cursor-crosshair",onMouseDown:N,onMouseMove:k,onMouseUp:G,onMouseLeave:G,style:{touchAction:"none",backgroundColor:"rgba(0,0,0,0)",background:"none",borderRadius:"8px",display:"block",opacity:"1",position:"relative",margin:0,padding:0,border:"none",outline:"none"}})})]})};function er(e,t){return function(){return e.apply(t,arguments)}}const{toString:Eo}=Object.prototype,{getPrototypeOf:Is}=Object,{iterator:ss,toStringTag:tr}=Symbol,ns=(e=>t=>{const n=Eo.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),Fe=e=>(e=e.toLowerCase(),t=>ns(t)===e),rs=e=>t=>typeof t===e,{isArray:ot}=Array,gt=rs("undefined");function jt(e){return e!==null&&!gt(e)&&e.constructor!==null&&!gt(e.constructor)&&Re(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const sr=Fe("ArrayBuffer");function ko(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&sr(e.buffer),t}const Co=rs("string"),Re=rs("function"),nr=rs("number"),vt=e=>e!==null&&typeof e=="object",To=e=>e===!0||e===!1,It=e=>{if(ns(e)!=="object")return!1;const t=Is(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(tr in e)&&!(ss in e)},Po=e=>{if(!vt(e)||jt(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},Ro=Fe("Date"),_o=Fe("File"),Lo=Fe("Blob"),Ao=Fe("FileList"),Io=e=>vt(e)&&Re(e.pipe),$o=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Re(e.append)&&((t=ns(e))==="formdata"||t==="object"&&Re(e.toString)&&e.toString()==="[object FormData]"))},Oo=Fe("URLSearchParams"),[Do,Fo,Mo,Uo]=["ReadableStream","Request","Response","Headers"].map(Fe),zo=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function wt(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,a;if(typeof e!="object"&&(e=[e]),ot(e))for(r=0,a=e.length;r<a;r++)t.call(null,e[r],r,e);else{if(jt(e))return;const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let c;for(r=0;r<i;r++)c=o[r],t.call(null,e[c],c,e)}}function rr(e,t){if(jt(e))return null;t=t.toLowerCase();const n=Object.keys(e);let r=n.length,a;for(;r-- >0;)if(a=n[r],t===a.toLowerCase())return a;return null}const Xe=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,ar=e=>!gt(e)&&e!==Xe;function Ss(){const{caseless:e}=ar(this)&&this||{},t={},n=(r,a)=>{const o=e&&rr(t,a)||a;It(t[o])&&It(r)?t[o]=Ss(t[o],r):It(r)?t[o]=Ss({},r):ot(r)?t[o]=r.slice():t[o]=r};for(let r=0,a=arguments.length;r<a;r++)arguments[r]&&wt(arguments[r],n);return t}const Bo=(e,t,n,{allOwnKeys:r}={})=>(wt(t,(a,o)=>{n&&Re(a)?e[o]=er(a,n):e[o]=a},{allOwnKeys:r}),e),qo=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Ho=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Wo=(e,t,n,r)=>{let a,o,i;const c={};if(t=t||{},e==null)return t;do{for(a=Object.getOwnPropertyNames(e),o=a.length;o-- >0;)i=a[o],(!r||r(i,e,t))&&!c[i]&&(t[i]=e[i],c[i]=!0);e=n!==!1&&Is(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Jo=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},Go=e=>{if(!e)return null;if(ot(e))return e;let t=e.length;if(!nr(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Vo=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Is(Uint8Array)),Yo=(e,t)=>{const r=(e&&e[ss]).call(e);let a;for(;(a=r.next())&&!a.done;){const o=a.value;t.call(e,o[0],o[1])}},Xo=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},Ko=Fe("HTMLFormElement"),Qo=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,a){return r.toUpperCase()+a}),tn=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Zo=Fe("RegExp"),or=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};wt(n,(a,o)=>{let i;(i=t(a,o,e))!==!1&&(r[o]=i||a)}),Object.defineProperties(e,r)},ei=e=>{or(e,(t,n)=>{if(Re(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(Re(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},ti=(e,t)=>{const n={},r=a=>{a.forEach(o=>{n[o]=!0})};return ot(e)?r(e):r(String(e).split(t)),n},si=()=>{},ni=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function ri(e){return!!(e&&Re(e.append)&&e[tr]==="FormData"&&e[ss])}const ai=e=>{const t=new Array(10),n=(r,a)=>{if(vt(r)){if(t.indexOf(r)>=0)return;if(jt(r))return r;if(!("toJSON"in r)){t[a]=r;const o=ot(r)?[]:{};return wt(r,(i,c)=>{const l=n(i,a+1);!gt(l)&&(o[c]=l)}),t[a]=void 0,o}}return r};return n(e,0)},oi=Fe("AsyncFunction"),ii=e=>e&&(vt(e)||Re(e))&&Re(e.then)&&Re(e.catch),ir=((e,t)=>e?setImmediate:t?((n,r)=>(Xe.addEventListener("message",({source:a,data:o})=>{a===Xe&&o===n&&r.length&&r.shift()()},!1),a=>{r.push(a),Xe.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",Re(Xe.postMessage)),li=typeof queueMicrotask<"u"?queueMicrotask.bind(Xe):typeof process<"u"&&process.nextTick||ir,ci=e=>e!=null&&Re(e[ss]),p={isArray:ot,isArrayBuffer:sr,isBuffer:jt,isFormData:$o,isArrayBufferView:ko,isString:Co,isNumber:nr,isBoolean:To,isObject:vt,isPlainObject:It,isEmptyObject:Po,isReadableStream:Do,isRequest:Fo,isResponse:Mo,isHeaders:Uo,isUndefined:gt,isDate:Ro,isFile:_o,isBlob:Lo,isRegExp:Zo,isFunction:Re,isStream:Io,isURLSearchParams:Oo,isTypedArray:Vo,isFileList:Ao,forEach:wt,merge:Ss,extend:Bo,trim:zo,stripBOM:qo,inherits:Ho,toFlatObject:Wo,kindOf:ns,kindOfTest:Fe,endsWith:Jo,toArray:Go,forEachEntry:Yo,matchAll:Xo,isHTMLForm:Ko,hasOwnProperty:tn,hasOwnProp:tn,reduceDescriptors:or,freezeMethods:ei,toObjectSet:ti,toCamelCase:Qo,noop:si,toFiniteNumber:ni,findKey:rr,global:Xe,isContextDefined:ar,isSpecCompliantForm:ri,toJSONObject:ai,isAsyncFn:oi,isThenable:ii,setImmediate:ir,asap:li,isIterable:ci};function V(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a,this.status=a.status?a.status:null)}p.inherits(V,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:p.toJSONObject(this.config),code:this.code,status:this.status}}});const lr=V.prototype,cr={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{cr[e]={value:e}});Object.defineProperties(V,cr);Object.defineProperty(lr,"isAxiosError",{value:!0});V.from=(e,t,n,r,a,o)=>{const i=Object.create(lr);return p.toFlatObject(e,i,function(l){return l!==Error.prototype},c=>c!=="isAxiosError"),V.call(i,e.message,t,n,r,a),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const di=null;function Ns(e){return p.isPlainObject(e)||p.isArray(e)}function dr(e){return p.endsWith(e,"[]")?e.slice(0,-2):e}function sn(e,t,n){return e?e.concat(t).map(function(a,o){return a=dr(a),!n&&o?"["+a+"]":a}).join(n?".":""):t}function ui(e){return p.isArray(e)&&!e.some(Ns)}const hi=p.toFlatObject(p,{},null,function(t){return/^is[A-Z]/.test(t)});function as(e,t,n){if(!p.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=p.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(j,x){return!p.isUndefined(x[j])});const r=n.metaTokens,a=n.visitor||d,o=n.dots,i=n.indexes,l=(n.Blob||typeof Blob<"u"&&Blob)&&p.isSpecCompliantForm(t);if(!p.isFunction(a))throw new TypeError("visitor must be a function");function h(b){if(b===null)return"";if(p.isDate(b))return b.toISOString();if(p.isBoolean(b))return b.toString();if(!l&&p.isBlob(b))throw new V("Blob is not supported. Use a Buffer instead.");return p.isArrayBuffer(b)||p.isTypedArray(b)?l&&typeof Blob=="function"?new Blob([b]):Buffer.from(b):b}function d(b,j,x){let P=b;if(b&&!x&&typeof b=="object"){if(p.endsWith(j,"{}"))j=r?j:j.slice(0,-2),b=JSON.stringify(b);else if(p.isArray(b)&&ui(b)||(p.isFileList(b)||p.endsWith(j,"[]"))&&(P=p.toArray(b)))return j=dr(j),P.forEach(function(E,q){!(p.isUndefined(E)||E===null)&&t.append(i===!0?sn([j],q,o):i===null?j:j+"[]",h(E))}),!1}return Ns(b)?!0:(t.append(sn(x,j,o),h(b)),!1)}const m=[],v=Object.assign(hi,{defaultVisitor:d,convertValue:h,isVisitable:Ns});function L(b,j){if(!p.isUndefined(b)){if(m.indexOf(b)!==-1)throw Error("Circular reference detected in "+j.join("."));m.push(b),p.forEach(b,function(P,T){(!(p.isUndefined(P)||P===null)&&a.call(t,P,p.isString(T)?T.trim():T,j,v))===!0&&L(P,j?j.concat(T):[T])}),m.pop()}}if(!p.isObject(e))throw new TypeError("data must be an object");return L(e),t}function nn(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function $s(e,t){this._pairs=[],e&&as(e,this,t)}const ur=$s.prototype;ur.append=function(t,n){this._pairs.push([t,n])};ur.toString=function(t){const n=t?function(r){return t.call(this,r,nn)}:nn;return this._pairs.map(function(a){return n(a[0])+"="+n(a[1])},"").join("&")};function mi(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function hr(e,t,n){if(!t)return e;const r=n&&n.encode||mi;p.isFunction(n)&&(n={serialize:n});const a=n&&n.serialize;let o;if(a?o=a(t,n):o=p.isURLSearchParams(t)?t.toString():new $s(t,n).toString(r),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class rn{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){p.forEach(this.handlers,function(r){r!==null&&t(r)})}}const mr={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},fi=typeof URLSearchParams<"u"?URLSearchParams:$s,pi=typeof FormData<"u"?FormData:null,xi=typeof Blob<"u"?Blob:null,gi={isBrowser:!0,classes:{URLSearchParams:fi,FormData:pi,Blob:xi},protocols:["http","https","file","blob","url","data"]},Os=typeof window<"u"&&typeof document<"u",Es=typeof navigator=="object"&&navigator||void 0,yi=Os&&(!Es||["ReactNative","NativeScript","NS"].indexOf(Es.product)<0),bi=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",ji=Os&&window.location.href||"http://localhost",vi=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Os,hasStandardBrowserEnv:yi,hasStandardBrowserWebWorkerEnv:bi,navigator:Es,origin:ji},Symbol.toStringTag,{value:"Module"})),Ce={...vi,...gi};function wi(e,t){return as(e,new Ce.classes.URLSearchParams,{visitor:function(n,r,a,o){return Ce.isNode&&p.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)},...t})}function Si(e){return p.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Ni(e){const t={},n=Object.keys(e);let r;const a=n.length;let o;for(r=0;r<a;r++)o=n[r],t[o]=e[o];return t}function fr(e){function t(n,r,a,o){let i=n[o++];if(i==="__proto__")return!0;const c=Number.isFinite(+i),l=o>=n.length;return i=!i&&p.isArray(a)?a.length:i,l?(p.hasOwnProp(a,i)?a[i]=[a[i],r]:a[i]=r,!c):((!a[i]||!p.isObject(a[i]))&&(a[i]=[]),t(n,r,a[i],o)&&p.isArray(a[i])&&(a[i]=Ni(a[i])),!c)}if(p.isFormData(e)&&p.isFunction(e.entries)){const n={};return p.forEachEntry(e,(r,a)=>{t(Si(r),a,n,0)}),n}return null}function Ei(e,t,n){if(p.isString(e))try{return(t||JSON.parse)(e),p.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const St={transitional:mr,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",a=r.indexOf("application/json")>-1,o=p.isObject(t);if(o&&p.isHTMLForm(t)&&(t=new FormData(t)),p.isFormData(t))return a?JSON.stringify(fr(t)):t;if(p.isArrayBuffer(t)||p.isBuffer(t)||p.isStream(t)||p.isFile(t)||p.isBlob(t)||p.isReadableStream(t))return t;if(p.isArrayBufferView(t))return t.buffer;if(p.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let c;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return wi(t,this.formSerializer).toString();if((c=p.isFileList(t))||r.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return as(c?{"files[]":t}:t,l&&new l,this.formSerializer)}}return o||a?(n.setContentType("application/json",!1),Ei(t)):t}],transformResponse:[function(t){const n=this.transitional||St.transitional,r=n&&n.forcedJSONParsing,a=this.responseType==="json";if(p.isResponse(t)||p.isReadableStream(t))return t;if(t&&p.isString(t)&&(r&&!this.responseType||a)){const i=!(n&&n.silentJSONParsing)&&a;try{return JSON.parse(t)}catch(c){if(i)throw c.name==="SyntaxError"?V.from(c,V.ERR_BAD_RESPONSE,this,null,this.response):c}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ce.classes.FormData,Blob:Ce.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};p.forEach(["delete","get","head","post","put","patch"],e=>{St.headers[e]={}});const ki=p.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Ci=e=>{const t={};let n,r,a;return e&&e.split(`
`).forEach(function(i){a=i.indexOf(":"),n=i.substring(0,a).trim().toLowerCase(),r=i.substring(a+1).trim(),!(!n||t[n]&&ki[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},an=Symbol("internals");function dt(e){return e&&String(e).trim().toLowerCase()}function $t(e){return e===!1||e==null?e:p.isArray(e)?e.map($t):String(e)}function Ti(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const Pi=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function ms(e,t,n,r,a){if(p.isFunction(r))return r.call(this,t,n);if(a&&(t=n),!!p.isString(t)){if(p.isString(r))return t.indexOf(r)!==-1;if(p.isRegExp(r))return r.test(t)}}function Ri(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function _i(e,t){const n=p.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(a,o,i){return this[r].call(this,t,a,o,i)},configurable:!0})})}let _e=class{constructor(t){t&&this.set(t)}set(t,n,r){const a=this;function o(c,l,h){const d=dt(l);if(!d)throw new Error("header name must be a non-empty string");const m=p.findKey(a,d);(!m||a[m]===void 0||h===!0||h===void 0&&a[m]!==!1)&&(a[m||l]=$t(c))}const i=(c,l)=>p.forEach(c,(h,d)=>o(h,d,l));if(p.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(p.isString(t)&&(t=t.trim())&&!Pi(t))i(Ci(t),n);else if(p.isObject(t)&&p.isIterable(t)){let c={},l,h;for(const d of t){if(!p.isArray(d))throw TypeError("Object iterator must return a key-value pair");c[h=d[0]]=(l=c[h])?p.isArray(l)?[...l,d[1]]:[l,d[1]]:d[1]}i(c,n)}else t!=null&&o(n,t,r);return this}get(t,n){if(t=dt(t),t){const r=p.findKey(this,t);if(r){const a=this[r];if(!n)return a;if(n===!0)return Ti(a);if(p.isFunction(n))return n.call(this,a,r);if(p.isRegExp(n))return n.exec(a);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=dt(t),t){const r=p.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||ms(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let a=!1;function o(i){if(i=dt(i),i){const c=p.findKey(r,i);c&&(!n||ms(r,r[c],c,n))&&(delete r[c],a=!0)}}return p.isArray(t)?t.forEach(o):o(t),a}clear(t){const n=Object.keys(this);let r=n.length,a=!1;for(;r--;){const o=n[r];(!t||ms(this,this[o],o,t,!0))&&(delete this[o],a=!0)}return a}normalize(t){const n=this,r={};return p.forEach(this,(a,o)=>{const i=p.findKey(r,o);if(i){n[i]=$t(a),delete n[o];return}const c=t?Ri(o):String(o).trim();c!==o&&delete n[o],n[c]=$t(a),r[c]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return p.forEach(this,(r,a)=>{r!=null&&r!==!1&&(n[a]=t&&p.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(a=>r.set(a)),r}static accessor(t){const r=(this[an]=this[an]={accessors:{}}).accessors,a=this.prototype;function o(i){const c=dt(i);r[c]||(_i(a,i),r[c]=!0)}return p.isArray(t)?t.forEach(o):o(t),this}};_e.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);p.reduceDescriptors(_e.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});p.freezeMethods(_e);function fs(e,t){const n=this||St,r=t||n,a=_e.from(r.headers);let o=r.data;return p.forEach(e,function(c){o=c.call(n,o,a.normalize(),t?t.status:void 0)}),a.normalize(),o}function pr(e){return!!(e&&e.__CANCEL__)}function it(e,t,n){V.call(this,e??"canceled",V.ERR_CANCELED,t,n),this.name="CanceledError"}p.inherits(it,V,{__CANCEL__:!0});function xr(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new V("Request failed with status code "+n.status,[V.ERR_BAD_REQUEST,V.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Li(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Ai(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a=0,o=0,i;return t=t!==void 0?t:1e3,function(l){const h=Date.now(),d=r[o];i||(i=h),n[a]=l,r[a]=h;let m=o,v=0;for(;m!==a;)v+=n[m++],m=m%e;if(a=(a+1)%e,a===o&&(o=(o+1)%e),h-i<t)return;const L=d&&h-d;return L?Math.round(v*1e3/L):void 0}}function Ii(e,t){let n=0,r=1e3/t,a,o;const i=(h,d=Date.now())=>{n=d,a=null,o&&(clearTimeout(o),o=null),e(...h)};return[(...h)=>{const d=Date.now(),m=d-n;m>=r?i(h,d):(a=h,o||(o=setTimeout(()=>{o=null,i(a)},r-m)))},()=>a&&i(a)]}const Ut=(e,t,n=3)=>{let r=0;const a=Ai(50,250);return Ii(o=>{const i=o.loaded,c=o.lengthComputable?o.total:void 0,l=i-r,h=a(l),d=i<=c;r=i;const m={loaded:i,total:c,progress:c?i/c:void 0,bytes:l,rate:h||void 0,estimated:h&&c&&d?(c-i)/h:void 0,event:o,lengthComputable:c!=null,[t?"download":"upload"]:!0};e(m)},n)},on=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},ln=e=>(...t)=>p.asap(()=>e(...t)),$i=Ce.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Ce.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Ce.origin),Ce.navigator&&/(msie|trident)/i.test(Ce.navigator.userAgent)):()=>!0,Oi=Ce.hasStandardBrowserEnv?{write(e,t,n,r,a,o){const i=[e+"="+encodeURIComponent(t)];p.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),p.isString(r)&&i.push("path="+r),p.isString(a)&&i.push("domain="+a),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Di(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Fi(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function gr(e,t,n){let r=!Di(t);return e&&(r||n==!1)?Fi(e,t):t}const cn=e=>e instanceof _e?{...e}:e;function Ze(e,t){t=t||{};const n={};function r(h,d,m,v){return p.isPlainObject(h)&&p.isPlainObject(d)?p.merge.call({caseless:v},h,d):p.isPlainObject(d)?p.merge({},d):p.isArray(d)?d.slice():d}function a(h,d,m,v){if(p.isUndefined(d)){if(!p.isUndefined(h))return r(void 0,h,m,v)}else return r(h,d,m,v)}function o(h,d){if(!p.isUndefined(d))return r(void 0,d)}function i(h,d){if(p.isUndefined(d)){if(!p.isUndefined(h))return r(void 0,h)}else return r(void 0,d)}function c(h,d,m){if(m in t)return r(h,d);if(m in e)return r(void 0,h)}const l={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:c,headers:(h,d,m)=>a(cn(h),cn(d),m,!0)};return p.forEach(Object.keys({...e,...t}),function(d){const m=l[d]||a,v=m(e[d],t[d],d);p.isUndefined(v)&&m!==c||(n[d]=v)}),n}const yr=e=>{const t=Ze({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:a,xsrfCookieName:o,headers:i,auth:c}=t;t.headers=i=_e.from(i),t.url=hr(gr(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&i.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let l;if(p.isFormData(n)){if(Ce.hasStandardBrowserEnv||Ce.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((l=i.getContentType())!==!1){const[h,...d]=l?l.split(";").map(m=>m.trim()).filter(Boolean):[];i.setContentType([h||"multipart/form-data",...d].join("; "))}}if(Ce.hasStandardBrowserEnv&&(r&&p.isFunction(r)&&(r=r(t)),r||r!==!1&&$i(t.url))){const h=a&&o&&Oi.read(o);h&&i.set(a,h)}return t},Mi=typeof XMLHttpRequest<"u",Ui=Mi&&function(e){return new Promise(function(n,r){const a=yr(e);let o=a.data;const i=_e.from(a.headers).normalize();let{responseType:c,onUploadProgress:l,onDownloadProgress:h}=a,d,m,v,L,b;function j(){L&&L(),b&&b(),a.cancelToken&&a.cancelToken.unsubscribe(d),a.signal&&a.signal.removeEventListener("abort",d)}let x=new XMLHttpRequest;x.open(a.method.toUpperCase(),a.url,!0),x.timeout=a.timeout;function P(){if(!x)return;const E=_e.from("getAllResponseHeaders"in x&&x.getAllResponseHeaders()),Q={data:!c||c==="text"||c==="json"?x.responseText:x.response,status:x.status,statusText:x.statusText,headers:E,config:e,request:x};xr(function(te){n(te),j()},function(te){r(te),j()},Q),x=null}"onloadend"in x?x.onloadend=P:x.onreadystatechange=function(){!x||x.readyState!==4||x.status===0&&!(x.responseURL&&x.responseURL.indexOf("file:")===0)||setTimeout(P)},x.onabort=function(){x&&(r(new V("Request aborted",V.ECONNABORTED,e,x)),x=null)},x.onerror=function(){r(new V("Network Error",V.ERR_NETWORK,e,x)),x=null},x.ontimeout=function(){let q=a.timeout?"timeout of "+a.timeout+"ms exceeded":"timeout exceeded";const Q=a.transitional||mr;a.timeoutErrorMessage&&(q=a.timeoutErrorMessage),r(new V(q,Q.clarifyTimeoutError?V.ETIMEDOUT:V.ECONNABORTED,e,x)),x=null},o===void 0&&i.setContentType(null),"setRequestHeader"in x&&p.forEach(i.toJSON(),function(q,Q){x.setRequestHeader(Q,q)}),p.isUndefined(a.withCredentials)||(x.withCredentials=!!a.withCredentials),c&&c!=="json"&&(x.responseType=a.responseType),h&&([v,b]=Ut(h,!0),x.addEventListener("progress",v)),l&&x.upload&&([m,L]=Ut(l),x.upload.addEventListener("progress",m),x.upload.addEventListener("loadend",L)),(a.cancelToken||a.signal)&&(d=E=>{x&&(r(!E||E.type?new it(null,e,x):E),x.abort(),x=null)},a.cancelToken&&a.cancelToken.subscribe(d),a.signal&&(a.signal.aborted?d():a.signal.addEventListener("abort",d)));const T=Li(a.url);if(T&&Ce.protocols.indexOf(T)===-1){r(new V("Unsupported protocol "+T+":",V.ERR_BAD_REQUEST,e));return}x.send(o||null)})},zi=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,a;const o=function(h){if(!a){a=!0,c();const d=h instanceof Error?h:this.reason;r.abort(d instanceof V?d:new it(d instanceof Error?d.message:d))}};let i=t&&setTimeout(()=>{i=null,o(new V(`timeout ${t} of ms exceeded`,V.ETIMEDOUT))},t);const c=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(h=>{h.unsubscribe?h.unsubscribe(o):h.removeEventListener("abort",o)}),e=null)};e.forEach(h=>h.addEventListener("abort",o));const{signal:l}=r;return l.unsubscribe=()=>p.asap(c),l}},Bi=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,a;for(;r<n;)a=r+t,yield e.slice(r,a),r=a},qi=async function*(e,t){for await(const n of Hi(e))yield*Bi(n,t)},Hi=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},dn=(e,t,n,r)=>{const a=qi(e,t);let o=0,i,c=l=>{i||(i=!0,r&&r(l))};return new ReadableStream({async pull(l){try{const{done:h,value:d}=await a.next();if(h){c(),l.close();return}let m=d.byteLength;if(n){let v=o+=m;n(v)}l.enqueue(new Uint8Array(d))}catch(h){throw c(h),h}},cancel(l){return c(l),a.return()}},{highWaterMark:2})},os=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",br=os&&typeof ReadableStream=="function",Wi=os&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),jr=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Ji=br&&jr(()=>{let e=!1;const t=new Request(Ce.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),un=64*1024,ks=br&&jr(()=>p.isReadableStream(new Response("").body)),zt={stream:ks&&(e=>e.body)};os&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!zt[t]&&(zt[t]=p.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new V(`Response type '${t}' is not supported`,V.ERR_NOT_SUPPORT,r)})})})(new Response);const Gi=async e=>{if(e==null)return 0;if(p.isBlob(e))return e.size;if(p.isSpecCompliantForm(e))return(await new Request(Ce.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(p.isArrayBufferView(e)||p.isArrayBuffer(e))return e.byteLength;if(p.isURLSearchParams(e)&&(e=e+""),p.isString(e))return(await Wi(e)).byteLength},Vi=async(e,t)=>{const n=p.toFiniteNumber(e.getContentLength());return n??Gi(t)},Yi=os&&(async e=>{let{url:t,method:n,data:r,signal:a,cancelToken:o,timeout:i,onDownloadProgress:c,onUploadProgress:l,responseType:h,headers:d,withCredentials:m="same-origin",fetchOptions:v}=yr(e);h=h?(h+"").toLowerCase():"text";let L=zi([a,o&&o.toAbortSignal()],i),b;const j=L&&L.unsubscribe&&(()=>{L.unsubscribe()});let x;try{if(l&&Ji&&n!=="get"&&n!=="head"&&(x=await Vi(d,r))!==0){let Q=new Request(t,{method:"POST",body:r,duplex:"half"}),oe;if(p.isFormData(r)&&(oe=Q.headers.get("content-type"))&&d.setContentType(oe),Q.body){const[te,xe]=on(x,Ut(ln(l)));r=dn(Q.body,un,te,xe)}}p.isString(m)||(m=m?"include":"omit");const P="credentials"in Request.prototype;b=new Request(t,{...v,signal:L,method:n.toUpperCase(),headers:d.normalize().toJSON(),body:r,duplex:"half",credentials:P?m:void 0});let T=await fetch(b,v);const E=ks&&(h==="stream"||h==="response");if(ks&&(c||E&&j)){const Q={};["status","statusText","headers"].forEach(w=>{Q[w]=T[w]});const oe=p.toFiniteNumber(T.headers.get("content-length")),[te,xe]=c&&on(oe,Ut(ln(c),!0))||[];T=new Response(dn(T.body,un,te,()=>{xe&&xe(),j&&j()}),Q)}h=h||"text";let q=await zt[p.findKey(zt,h)||"text"](T,e);return!E&&j&&j(),await new Promise((Q,oe)=>{xr(Q,oe,{data:q,headers:_e.from(T.headers),status:T.status,statusText:T.statusText,config:e,request:b})})}catch(P){throw j&&j(),P&&P.name==="TypeError"&&/Load failed|fetch/i.test(P.message)?Object.assign(new V("Network Error",V.ERR_NETWORK,e,b),{cause:P.cause||P}):V.from(P,P&&P.code,e,b)}}),Cs={http:di,xhr:Ui,fetch:Yi};p.forEach(Cs,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const hn=e=>`- ${e}`,Xi=e=>p.isFunction(e)||e===null||e===!1,vr={getAdapter:e=>{e=p.isArray(e)?e:[e];const{length:t}=e;let n,r;const a={};for(let o=0;o<t;o++){n=e[o];let i;if(r=n,!Xi(n)&&(r=Cs[(i=String(n)).toLowerCase()],r===void 0))throw new V(`Unknown adapter '${i}'`);if(r)break;a[i||"#"+o]=r}if(!r){const o=Object.entries(a).map(([c,l])=>`adapter ${c} `+(l===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(hn).join(`
`):" "+hn(o[0]):"as no adapter specified";throw new V("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:Cs};function ps(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new it(null,e)}function mn(e){return ps(e),e.headers=_e.from(e.headers),e.data=fs.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),vr.getAdapter(e.adapter||St.adapter)(e).then(function(r){return ps(e),r.data=fs.call(e,e.transformResponse,r),r.headers=_e.from(r.headers),r},function(r){return pr(r)||(ps(e),r&&r.response&&(r.response.data=fs.call(e,e.transformResponse,r.response),r.response.headers=_e.from(r.response.headers))),Promise.reject(r)})}const wr="1.11.0",is={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{is[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const fn={};is.transitional=function(t,n,r){function a(o,i){return"[Axios v"+wr+"] Transitional option '"+o+"'"+i+(r?". "+r:"")}return(o,i,c)=>{if(t===!1)throw new V(a(i," has been removed"+(n?" in "+n:"")),V.ERR_DEPRECATED);return n&&!fn[i]&&(fn[i]=!0,console.warn(a(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,c):!0}};is.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function Ki(e,t,n){if(typeof e!="object")throw new V("options must be an object",V.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const o=r[a],i=t[o];if(i){const c=e[o],l=c===void 0||i(c,o,e);if(l!==!0)throw new V("option "+o+" must be "+l,V.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new V("Unknown option "+o,V.ERR_BAD_OPTION)}}const Ot={assertOptions:Ki,validators:is},Ue=Ot.validators;let Qe=class{constructor(t){this.defaults=t||{},this.interceptors={request:new rn,response:new rn}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let a={};Error.captureStackTrace?Error.captureStackTrace(a):a=new Error;const o=a.stack?a.stack.replace(/^.+\n/,""):"";try{r.stack?o&&!String(r.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+o):r.stack=o}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Ze(this.defaults,n);const{transitional:r,paramsSerializer:a,headers:o}=n;r!==void 0&&Ot.assertOptions(r,{silentJSONParsing:Ue.transitional(Ue.boolean),forcedJSONParsing:Ue.transitional(Ue.boolean),clarifyTimeoutError:Ue.transitional(Ue.boolean)},!1),a!=null&&(p.isFunction(a)?n.paramsSerializer={serialize:a}:Ot.assertOptions(a,{encode:Ue.function,serialize:Ue.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Ot.assertOptions(n,{baseUrl:Ue.spelling("baseURL"),withXsrfToken:Ue.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&p.merge(o.common,o[n.method]);o&&p.forEach(["delete","get","head","post","put","patch","common"],b=>{delete o[b]}),n.headers=_e.concat(i,o);const c=[];let l=!0;this.interceptors.request.forEach(function(j){typeof j.runWhen=="function"&&j.runWhen(n)===!1||(l=l&&j.synchronous,c.unshift(j.fulfilled,j.rejected))});const h=[];this.interceptors.response.forEach(function(j){h.push(j.fulfilled,j.rejected)});let d,m=0,v;if(!l){const b=[mn.bind(this),void 0];for(b.unshift(...c),b.push(...h),v=b.length,d=Promise.resolve(n);m<v;)d=d.then(b[m++],b[m++]);return d}v=c.length;let L=n;for(m=0;m<v;){const b=c[m++],j=c[m++];try{L=b(L)}catch(x){j.call(this,x);break}}try{d=mn.call(this,L)}catch(b){return Promise.reject(b)}for(m=0,v=h.length;m<v;)d=d.then(h[m++],h[m++]);return d}getUri(t){t=Ze(this.defaults,t);const n=gr(t.baseURL,t.url,t.allowAbsoluteUrls);return hr(n,t.params,t.paramsSerializer)}};p.forEach(["delete","get","head","options"],function(t){Qe.prototype[t]=function(n,r){return this.request(Ze(r||{},{method:t,url:n,data:(r||{}).data}))}});p.forEach(["post","put","patch"],function(t){function n(r){return function(o,i,c){return this.request(Ze(c||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}Qe.prototype[t]=n(),Qe.prototype[t+"Form"]=n(!0)});let Qi=class Sr{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(a=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](a);r._listeners=null}),this.promise.then=a=>{let o;const i=new Promise(c=>{r.subscribe(c),o=c}).then(a);return i.cancel=function(){r.unsubscribe(o)},i},t(function(o,i,c){r.reason||(r.reason=new it(o,i,c),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Sr(function(a){t=a}),cancel:t}}};function Zi(e){return function(n){return e.apply(null,n)}}function el(e){return p.isObject(e)&&e.isAxiosError===!0}const Ts={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Ts).forEach(([e,t])=>{Ts[t]=e});function Nr(e){const t=new Qe(e),n=er(Qe.prototype.request,t);return p.extend(n,Qe.prototype,t,{allOwnKeys:!0}),p.extend(n,t,null,{allOwnKeys:!0}),n.create=function(a){return Nr(Ze(e,a))},n}const be=Nr(St);be.Axios=Qe;be.CanceledError=it;be.CancelToken=Qi;be.isCancel=pr;be.VERSION=wr;be.toFormData=as;be.AxiosError=V;be.Cancel=be.CanceledError;be.all=function(t){return Promise.all(t)};be.spread=Zi;be.isAxiosError=el;be.mergeConfig=Ze;be.AxiosHeaders=_e;be.formToJSON=e=>fr(p.isHTMLForm(e)?new FormData(e):e);be.getAdapter=vr.getAdapter;be.HttpStatusCode=Ts;be.default=be;const{Axios:Ul,AxiosError:zl,CanceledError:Bl,isCancel:ql,CancelToken:Hl,VERSION:Wl,all:Jl,Cancel:Gl,isAxiosError:Vl,spread:Yl,toFormData:Xl,AxiosHeaders:Kl,HttpStatusCode:Ql,formToJSON:Zl,getAdapter:ec,mergeConfig:tc}=be,tl="http://localhost:8000/api/v1",Pe=be.create({baseURL:tl,timeout:3e4,headers:{"Content-Type":"application/json"}});Pe.interceptors.request.use(e=>{var t;return console.log("API Request:",(t=e.method)==null?void 0:t.toUpperCase(),e.url),e},e=>(console.error("API Request Error:",e),Promise.reject(e)));Pe.interceptors.response.use(e=>(console.log("API Response:",e.status,e.config.url),e),e=>{var t,n,r,a,o;return console.error("API Response Error:",{status:(t=e.response)==null?void 0:t.status,statusText:(n=e.response)==null?void 0:n.statusText,data:(r=e.response)==null?void 0:r.data,url:(a=e.config)==null?void 0:a.url,method:(o=e.config)==null?void 0:o.method,message:e.message}),Promise.reject(e)});class sl{async analyzeLines(t){try{console.log("发送线条分析请求:",{canvas_data_length:t.canvas_data.length,paths_count:t.paths.length,paths_sample:t.paths.slice(0,2)});const n=await Pe.post("/games/analyze-lines",t);return console.log("线条分析响应:",n.data),n.data}catch(n){throw console.error("线条分析失败:",n),console.error("请求数据:",t),new Error("线条分析失败，请稍后重试")}}async generateAIImage(t){try{console.log("发送AI生图请求");const n=await Pe.post("/games/generate-ai-image",t);return console.log("AI生图响应:",n.data),n.data}catch(n){return console.error("AI生图失败:",n),{status:"failed",error:"AI生图失败，请稍后重试"}}}async getGameLevels(){try{return(await Pe.get("/games/levels")).data}catch(t){throw console.error("获取游戏级别失败:",t),new Error("获取游戏级别失败")}}async getGameLevel(t){try{return(await Pe.get(`/games/levels/${t}`)).data}catch(n){throw console.error("获取级别详情失败:",n),new Error("获取级别详情失败")}}async getFamousArtworks(){try{return(await Pe.get("/games/artworks")).data}catch(t){throw console.error("获取名画列表失败:",t),new Error("获取名画列表失败")}}async getArtworkDetails(t){try{return(await Pe.get(`/games/artworks/${t}`)).data.data}catch(n){throw console.error("获取名画详情失败:",n),new Error("获取名画详情失败")}}async startGameSession(t,n=1,r){try{return(await Pe.post("/games/sessions/start",{level:t,stage:n,user_id:r})).data}catch(a){throw console.error("开始游戏会话失败:",a),new Error("开始游戏会话失败")}}async submitDrawing(t,n){try{return(await Pe.post(`/games/sessions/${t}/submit`,n)).data}catch(r){throw console.error("提交作品失败:",r),new Error("提交作品失败")}}}const pn=new sl,{Title:Pt,Text:ke}=Le,nl=({onBack:e,onComplete:t})=>{const[n,r]=u.useState(!1),[a,o]=u.useState(""),[i,c]=u.useState(null),[l,h]=u.useState(null),[d,m]=u.useState(!1),[v,L]=u.useState([]),[b,j]=u.useState(0),x=async E=>{var q;o(E),m(!0);try{const Q=await pn.analyzeLines({canvas_data:E,paths:v});if(h(Q),r(!0),m(!1),K.success("风格分析完成！AI正在为您创作艺术作品..."),((q=Q.ai_generated_image)==null?void 0:q.status)==="generating")try{const oe=await pn.generateAIImage({canvas_data:E,paths:v});h(te=>te&&{...te,ai_generated_image:oe}),oe.status==="success"?K.success("AI艺术作品生成完成！"):K.warning("AI生图暂时不可用，但风格分析已完成")}catch(oe){console.error("AI生图失败:",oe),h(te=>te&&{...te,ai_generated_image:{status:"failed",error:"AI生图服务暂时不可用"}}),K.warning("AI生图暂时不可用，但风格分析已完成")}}catch(Q){console.error("线条分析失败:",Q),K.error("分析失败，但作品已保存"),r(!0),m(!1)}},P=()=>{a?t():K.warning("请先保存您的作品")},T=()=>{r(!1),o(""),h(null),L([]),j(E=>E+1),i&&i()};return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-orange-50 via-pink-50 to-purple-50",children:s.jsx("div",{className:"max-w-6xl mx-auto px-4 py-8",children:s.jsxs(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[!n&&s.jsx(ie,{className:"mb-6 shadow-sm",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx(Pt,{level:2,className:"mb-2 text-purple-600",children:"自由绘画"}),s.jsx(ke,{className:"text-lg text-gray-600",children:"发挥您的想象力，创作属于您的艺术作品。没有对错，只有表达。让您的创意自由流淌吧！"})]}),s.jsx(H,{icon:s.jsx(He,{}),onClick:e,size:"large",className:"h-12 px-6",children:"返回"})]})}),n&&s.jsx(re.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5},className:"mb-6",children:s.jsx(ie,{className:"bg-gradient-to-r from-green-50 to-blue-50 border-green-200",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(mt,{className:"text-2xl text-white"})}),s.jsx(Pt,{level:3,className:"text-green-600 mb-3",children:"🎉 创作完成！"}),s.jsx(ke,{className:"text-lg text-gray-700 block mb-6",children:"太棒了！您已经完成了一幅美丽的作品。每一笔都是您创意的体现！"}),d&&s.jsxs("div",{className:"mb-6",children:[s.jsx(Fs,{size:"large"}),s.jsx(ke,{className:"block mt-2 text-gray-600",children:"正在分析您的线条风格..."})]}),l&&s.jsxs("div",{className:"mb-6 p-4 bg-white rounded-lg border",children:[s.jsxs(Pt,{level:4,className:"text-blue-600 mb-3",children:[s.jsx(nt,{className:"mr-2"}),"您的作品风格分析"]}),s.jsx("div",{className:"w-full",children:s.jsxs("div",{className:"grid grid-cols-4 gap-4",children:[s.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded",children:[s.jsx(ke,{className:"block text-sm text-gray-600 mb-1",children:"复杂度"}),s.jsxs(ke,{className:"font-medium text-lg",children:[(l.line_features.complexity*100).toFixed(1),"%"]})]}),s.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded",children:[s.jsx(ke,{className:"block text-sm text-gray-600 mb-1",children:"节奏感"}),s.jsxs(ke,{className:"font-medium text-lg",children:[(l.line_features.rhythm*100).toFixed(1),"%"]})]}),s.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded",children:[s.jsx(ke,{className:"block text-sm text-gray-600 mb-1",children:"平滑度"}),s.jsxs(ke,{className:"font-medium text-lg",children:[(l.line_features.smoothness*100).toFixed(1),"%"]})]}),s.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded",children:[s.jsx(ke,{className:"block text-sm text-gray-600 mb-1",children:"曲线特征"}),s.jsx(ke,{className:"font-medium text-lg",children:l.line_features.dominant_curves?"是":"否"})]})]})}),l.ai_generated_image&&s.jsxs("div",{className:"mt-6 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200",children:[s.jsx(Pt,{level:4,className:"text-purple-600 mb-3 text-center",children:"🎨 AI为您创作的艺术作品"}),l.ai_generated_image.status==="success"&&l.ai_generated_image.image_url&&s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsxs("div",{className:"text-center",children:[s.jsx(ke,{className:"block text-lg font-medium text-gray-700 mb-3",children:"📝 您的原创作品"}),s.jsx(Ms,{src:a,alt:"用户原创作品",className:"rounded-lg shadow-lg max-w-full h-auto border-2 border-gray-200",style:{maxHeight:"300px"},preview:{mask:s.jsx("div",{className:"text-white",children:"🔍 查看原图"})}})]}),s.jsxs("div",{className:"text-center",children:[s.jsx(ke,{className:"block text-lg font-medium text-purple-600 mb-3",children:"🤖 AI 创作版本"}),s.jsx(Ms,{src:l.ai_generated_image.image_url,alt:"AI生成的艺术作品",className:"rounded-lg shadow-lg max-w-full h-auto border-2 border-purple-200",style:{maxHeight:"300px"},preview:{mask:s.jsx("div",{className:"text-white",children:"🔍 查看AI作品"})}})]})]}),l.ai_generated_image.status==="failed"&&s.jsxs("div",{className:"text-center text-gray-500",children:[s.jsx(ke,{children:"😔 AI创作暂时不可用"}),l.ai_generated_image.error&&s.jsx(ke,{className:"text-xs block mt-1",children:l.ai_generated_image.error})]}),l.ai_generated_image.status==="generating"&&s.jsx("div",{className:"text-center py-8",children:s.jsxs("div",{className:"relative",children:[s.jsx(Fs,{size:"large"}),s.jsxs("div",{className:"mt-4",children:[s.jsx(ke,{className:"block text-lg font-medium text-purple-600",children:"🎨 AI正在为您创作艺术作品"}),s.jsx(ke,{className:"block mt-2 text-gray-600",children:"请稍候，这可能需要10-30秒..."}),s.jsxs("div",{className:"mt-3 flex justify-center items-center space-x-2",children:[s.jsx("div",{className:"w-2 h-2 bg-purple-400 rounded-full animate-bounce"}),s.jsx("div",{className:"w-2 h-2 bg-pink-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),s.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]})]})})]})]}),s.jsxs(fe,{size:"large",children:[s.jsx(H,{type:"primary",size:"large",icon:s.jsx(mt,{}),onClick:P,className:"h-12 px-8 bg-green-500 hover:bg-green-600",children:"完成练习"}),s.jsx(H,{size:"large",icon:s.jsx(rt,{}),onClick:T,className:"h-12 px-8",children:"重新创作"})]})]})})}),!n&&s.jsx("div",{className:"transparent-canvas-card",children:s.jsx(Zn,{width:768,height:1024,onSave:x,hideCanvas:!1,onClearRef:c,isCompleted:!1,onPathsChange:L},b)})]})})})},{Title:Rt,Text:_t}=Le,rl=[{id:"triangle",name:"三角形",description:"绘制一个三角形",instruction:"画三条直线，让它们相互连接形成三角形",difficulty:1,type:"straight"},{id:"square",name:"正方形",description:"绘制一个正方形",instruction:"画四条相等的直线，形成四个直角",difficulty:1,type:"straight"},{id:"rectangle",name:"长方形",description:"绘制一个长方形",instruction:"画四条直线，对边相等且平行",difficulty:1,type:"straight"},{id:"pentagon",name:"五边形",description:"绘制一个五边形",instruction:"画五条直线，形成一个封闭的五边形",difficulty:2,type:"straight"},{id:"hexagon",name:"六边形",description:"绘制一个六边形",instruction:"画六条直线，形成一个封闭的六边形",difficulty:2,type:"straight"},{id:"circle",name:"圆形",description:"绘制一个圆形",instruction:"画一条连续的曲线，让起点和终点相接",difficulty:1,type:"curved"},{id:"oval",name:"椭圆形",description:"绘制一个椭圆形",instruction:"画一个拉长的圆形，像鸡蛋的形状",difficulty:2,type:"curved"},{id:"heart",name:"心形",description:"绘制一个心形",instruction:"画两个圆弧在顶部，底部汇聚成一个点",difficulty:3,type:"curved"},{id:"star",name:"星形",description:"绘制一个五角星",instruction:"画五个尖角，每个尖角之间用直线或曲线连接",difficulty:3,type:"curved"}],xn=({onBack:e,onComplete:t,shapeType:n})=>{const[r,a]=u.useState(0),[o,i]=u.useState(!1),[c,l]=u.useState(0),[h,d]=u.useState(!1),m=rl.filter(q=>q.type===n),v=m[r],L=q=>{const oe=(4-v.difficulty)*10,te=Math.floor(Math.random()*20),xe=Math.min(100,60+oe+te);l(xe),i(!0),K.success(`太棒了！您的${v.name}得分：${xe}分`)},b=()=>{t(c)},j=()=>{r<m.length-1?(a(q=>q+1),i(!1),l(0),d(!1)):b()},x=()=>{i(!1),l(0)},P=()=>{d(!h)},T=q=>{switch(q){case 1:return"green";case 2:return"blue";case 3:return"purple";default:return"gray"}},E=q=>{switch(q){case 1:return"简单";case 2:return"中等";case 3:return"困难";default:return"未知"}};return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50",children:s.jsx("div",{className:"max-w-6xl mx-auto px-4 py-8",children:s.jsxs(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[s.jsx(ie,{className:"mb-6 shadow-sm",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsxs(Rt,{level:2,className:"mb-2 text-purple-600",children:[n==="straight"?"直线图形":"曲线图形","绘制"]}),s.jsx(_t,{className:"text-lg text-gray-600",children:n==="straight"?"学习绘制各种直线构成的几何图形":"练习绘制优美的曲线图形"})]}),s.jsx(H,{icon:s.jsx(He,{}),onClick:e,size:"large",className:"h-12 px-6",children:"返回"})]})}),s.jsx(ie,{className:"mb-6 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[s.jsxs(Rt,{level:4,className:"text-blue-600 mb-0",children:[v.name," (",r+1,"/",m.length,")"]}),s.jsx(Ge,{color:T(v.difficulty),children:E(v.difficulty)})]}),s.jsx(_t,{className:"text-gray-700 block mb-2",children:v.description}),h&&s.jsx(re.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},transition:{duration:.3},children:s.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-2",children:s.jsxs(_t,{className:"text-yellow-800",children:["💡 提示：",v.instruction]})})})]}),s.jsxs(H,{icon:s.jsx(Ir,{}),onClick:P,type:h?"primary":"default",children:[h?"隐藏":"显示","提示"]})]})}),s.jsx(ie,{className:"mb-6 bg-gradient-to-r from-green-50 to-blue-50 border-green-200",children:s.jsxs("div",{className:"text-center",children:[s.jsx(Rt,{level:4,className:"text-green-600 mb-3",children:"🎯 绘画要点"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-700",children:[s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-green-400 rounded-full"}),s.jsx("span",{children:n==="straight"?"保持线条笔直":"保持曲线流畅"})]}),s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-blue-400 rounded-full"}),s.jsx("span",{children:"注意图形的对称性"})]}),s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-purple-400 rounded-full"}),s.jsx("span",{children:"确保线条闭合"})]})]})]})}),s.jsx("div",{className:"transparent-canvas-card",children:s.jsx(Zn,{width:800,height:600,onSave:L})}),o&&s.jsx(re.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5},className:"mt-6",children:s.jsx(ie,{className:"bg-gradient-to-r from-green-50 to-blue-50 border-green-200",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(mt,{className:"text-2xl text-white"})}),s.jsxs(Rt,{level:3,className:"text-green-600 mb-3",children:["🎉 ",v.name,"绘制完成！"]}),s.jsxs("div",{className:"mb-4",children:[s.jsx(_t,{className:"text-lg text-gray-700 block mb-2",children:"绘制评分"}),s.jsx(jn,{percent:c,strokeColor:{"0%":"#108ee9","100%":"#87d068"},className:"max-w-md mx-auto"})]}),s.jsxs(fe,{size:"large",children:[r<m.length-1?s.jsx(H,{type:"primary",size:"large",icon:s.jsx(mt,{}),onClick:j,className:"h-12 px-8 bg-blue-500 hover:bg-blue-600",children:"下一个图形"}):s.jsx(H,{type:"primary",size:"large",icon:s.jsx(mt,{}),onClick:b,className:"h-12 px-8 bg-green-500 hover:bg-green-600",children:"完成练习"}),s.jsx(H,{size:"large",icon:s.jsx(rt,{}),onClick:x,className:"h-12 px-8",children:"重新绘制"})]})]})})})]})})})},st={accuracyThreshold:.7,tolerance:20,animationSpeed:.6,staticTraceDisplayTime:3e3},ut={minCanvasSize:350,maxCanvasSize:700,sizeRatios:{small:.45,medium:.5,large:.55}},gn=({guidePaths:e,originalCanvasSize:t,levelStage:n="L1-3",onPathComplete:r})=>{const a=n==="L1-4",o=u.useRef(null),[i,c]=u.useState(0),[l,h]=u.useState(!1),[d,m]=u.useState([]),[v,L]=u.useState([]),[b,j]=u.useState({}),[x,P]=u.useState(0),[T,E]=u.useState("entering"),[q,Q]=u.useState(.1),[oe,te]=u.useState(1),[xe,w]=u.useState(0),[O,N]=u.useState(1),[k,G]=u.useState(""),[J,Z]=u.useState(!1),[de,Y]=u.useState(""),[pe,y]=u.useState(!1),[I,W]=u.useState(null),[B,ee]=u.useState({width:800,height:600}),[X,le]=u.useState(""),[je,Ae]=u.useState(""),[ge,Me]=u.useState(null),[ve,tt]=u.useState("guide-animation"),[Be,lt]=u.useState(null);u.useEffect(()=>{const f=new Image;f.onload=()=>{W(f)},f.src=`/${n}/arrow.png`},[n]),u.useEffect(()=>{le(""),Ae("");const f=C=>new Promise((_,R)=>{const U=new Image;U.onload=()=>_(U),U.onerror=()=>R(new Error(`Failed to load ${C}`)),U.src=C});(async()=>{const C=["png","jpg","jpeg"];let _=null,R="";for(const $ of C)try{const A=`/${n}/bg.${$}`;_=await f(A),R=A;break}catch{}if(_&&R){le(R);const A=(()=>{const ae=window.innerHeight;let me;return ae<=600?me=ae*ut.sizeRatios.small:ae<=900?me=ae*ut.sizeRatios.medium:me=ae*ut.sizeRatios.large,me=Math.max(ut.minCanvasSize,me),me=Math.min(ut.maxCanvasSize,me),me})(),F=_.width/_.height;let z,ne;F>=1?(z=A,ne=A/F,ne>A&&(ne=A,z=A*F)):(ne=A,z=A*F,z>A&&(z=A,ne=A/F)),ee({width:Math.round(z),height:Math.round(ne)})}else console.error("Failed to load canvas background image in any format"),ee({width:400,height:300});const U=["jpg","jpeg","png"];for(const $ of U)try{const A=`/${n}/full.${$}`;await f(A),Ae(A);break}catch{}})()},[n]);const Nt=f=>f,[qe,ls]=u.useState([]);u.useEffect(()=>{if(e.length>0){const f=Nt(e);ls(f);let S=0;for(;S<f.length;){const C=f[S];if(!C||C.points.length>=2)break;L(_=>[..._,C.id]),r(C.id,[]),S++}S!==i&&c(S)}},[e]);const ue=qe[i];u.useEffect(()=>{qe.length>0&&ue&&(ge&&(clearTimeout(ge),Me(null)),a&&Be&&(clearTimeout(Be),lt(null)),m([]),P(0),a&&tt("guide-animation"))},[qe,i,ue]),u.useEffect(()=>{if(T==="entering"){const f=Date.now(),S=3e3,C=()=>{const _=Date.now()-f,R=Math.min(_/S,1),$=(F=>F<.5?4*F*F*F:1-Math.pow(-2*F+2,3)/2)(R),A=.1+(1-.1)*$;Q(A),R<1?requestAnimationFrame(C):E("instruction")};requestAnimationFrame(C)}},[T]),u.useEffect(()=>{if(T==="instruction"){const f="Let’s create a beautiful painting together. Try to follow the yellow lines closely and gently trace along with the brush. You’ve got this—take your time.";(async()=>{let C=f;try{const $=await fetch(`/${n}/start.txt`);if($.ok){const A=await $.text();A.trim()&&(C=A.trim())}}catch{console.log(`未找到 ${n}/start.txt，使用默认引导文本`)}if(!C){Z(!1),E("active");return}Z(!0),G("");let _=0;const R=100,U=()=>{_<C.length?(G(C.slice(0,_+1)),_++,setTimeout(U,R)):setTimeout(()=>{Z(!1),E("active")},2e3)};setTimeout(U,500)})()}},[T,n]),u.useEffect(()=>{T==="completing"&&(async()=>{let S="";try{const C=await fetch(`/${n}/end.txt`);if(C.ok){const _=await C.text();_.trim()&&(S=_.trim())}}catch{console.log(`未找到 ${n}/end.txt，跳过结束鼓励语`)}S&&setTimeout(()=>{y(!0),Y("");let C=0;const _=80,R=()=>{C<S.length?(Y(S.slice(0,C+1)),C++,setTimeout(R,_)):setTimeout(()=>{y(!1),Y("")},3e3)};setTimeout(R,300)},1e3)})()},[T,n]),u.useEffect(()=>{if(T==="completing"){const f=Date.now(),S=2e3,C=()=>{const _=Date.now()-f,R=Math.min(_/S,1);te(1-R),w(R),R<1?requestAnimationFrame(C):E("showing")};requestAnimationFrame(C)}},[T]),u.useEffect(()=>{if(T==="showing"){if(a)return;{const f=setTimeout(()=>{E("exiting")},3e3);return()=>clearTimeout(f)}}},[T,a]),u.useEffect(()=>{if(T==="exiting"){if(a)return;const f=Date.now(),S=3e3,C=()=>{const _=Date.now()-f,R=Math.min(_/S,1),$=(z=>z<.5?2*z*z:-1+(4-2*z)*z)(R);N(1-$*.85);const A=.9;if(R>A){const z=(R-A)/(1-A);w(1-z)}else w(1);const F=.3;if(R>F)te(0);else{const z=R/F;te(1-z)}R<1&&requestAnimationFrame(C)};requestAnimationFrame(C)}},[T,a]),u.useEffect(()=>{const f=o.current;if(!f)return;const S=U=>{var $;($=U.target)!=null&&$.closest("canvas")&&U.preventDefault()},C=U=>{var me;const $=((me=document.querySelector("[data-guide-stage]"))==null?void 0:me.getAttribute("data-guide-stage"))||ve;if(U.preventDefault(),U.stopPropagation(),a&&($==="guide-animation"||$==="static-trace"))return;ge&&(clearTimeout(ge),Me(null)),h(!0);const A=U.touches[0],F=f.getBoundingClientRect(),z=f.width/F.width,ne=f.height/F.height,ae={x:(A.clientX-F.left)*z,y:(A.clientY-F.top)*ne};m([ae])},_=U=>{var we;U.preventDefault(),U.stopPropagation();const $=((we=document.querySelector("[data-guide-stage]"))==null?void 0:we.getAttribute("data-guide-stage"))||ve;if(a&&($==="guide-animation"||$==="static-trace")||!l)return;const A=U.touches[0],F=f.getBoundingClientRect(),z=f.width/F.width,ne=f.height/F.height,ae={x:(A.clientX-F.left)*z,y:(A.clientY-F.top)*ne},me=[...d,ae];m(me)},R=U=>{U.preventDefault(),U.stopPropagation(),he()};return f.addEventListener("touchstart",C,{passive:!1}),f.addEventListener("touchmove",_,{passive:!1}),f.addEventListener("touchend",R,{passive:!1}),document.addEventListener("touchmove",S,{passive:!1}),document.addEventListener("wheel",S,{passive:!1}),()=>{f.removeEventListener("touchstart",C),f.removeEventListener("touchmove",_),f.removeEventListener("touchend",R),document.removeEventListener("touchmove",S),document.removeEventListener("wheel",S)}},[l,d,ge,ve,a]),u.useEffect(()=>()=>{ge&&clearTimeout(ge),a&&Be&&clearTimeout(Be)},[ge,Be,a]),u.useEffect(()=>{if(!ue||T!=="active")return;let f,S=!0;if(a)ve==="guide-animation"&&(()=>{if(!S||ve!=="guide-animation")return;const _=ue.points.length,R=3e3,U=Math.max(0,(_-10)*150),$=(R+U)/st.animationSpeed,A=Date.now(),F=()=>{if(!S||ve!=="guide-animation")return;const ne=Date.now()-A;let ae=Math.min(ne/$,1);const we=(Ne=>Ne<.5?2*Ne*Ne:-1+(4-2*Ne)*Ne)(ae);P(we),ae<1?f=requestAnimationFrame(F):S&&ue&&!v.includes(ue.id)&&(tt("static-trace"),P(1))};f=requestAnimationFrame(F)})();else{const C=()=>{if(!S)return;const _=ue.points.length,R=3e3,U=Math.max(0,(_-10)*150),$=(R+U)/st.animationSpeed,A=Date.now(),F=()=>{if(!S)return;const ne=Date.now()-A;let ae=Math.min(ne/$,1);const we=(Ne=>Ne<.5?2*Ne*Ne:-1+(4-2*Ne)*Ne)(ae);P(we),ae<1?f=requestAnimationFrame(F):S&&ue&&!v.includes(ue.id)&&setTimeout(()=>{S&&(P(0),C())},500)};f=requestAnimationFrame(F)};C()}return()=>{S=!1,f&&cancelAnimationFrame(f)}},[i,ue,T,ve,a]),u.useEffect(()=>{if(!(!a||!ue||T!=="active")&&ve==="static-trace"){const f=setTimeout(()=>{v.includes(ue.id)||(tt("waiting-user"),P(0))},st.staticTraceDisplayTime);return lt(f),()=>{clearTimeout(f)}}},[ve,ue,a,T,v]),u.useEffect(()=>{const f=o.current;f&&(f.width=B.width,f.height=B.height,f.style.width=`${B.width}px`,f.style.height=`${B.height}px`)},[B]),u.useEffect(()=>{const f=o.current;if(!f)return;const S=f.getContext("2d");if(!S)return;S.clearRect(0,0,f.width,f.height),C();function C(){!ue||!S||(v.forEach(_=>{const R=b[_];R&&R.length>0&&Et(S,R)}),ue&&(a?ve==="guide-animation"?ct(S,ue):ve==="static-trace"&&ds(S,ue):ct(S,ue)),d.length>0&&Et(S,d))}},[ue,d,v,b,x,B,a?ve:null]);const cs=(f,S,C,_,R,U,$=15)=>{const A=_-S,F=R-C;if(!I){const ae=Math.atan2(F,A);f.fillStyle=U,f.strokeStyle=U,f.lineWidth=2,f.beginPath(),f.moveTo(_,R),f.lineTo(_-$*Math.cos(ae-Math.PI/5),R-$*Math.sin(ae-Math.PI/5)),f.lineTo(_-$*Math.cos(ae+Math.PI/5),R-$*Math.sin(ae+Math.PI/5)),f.closePath(),f.fill(),f.stroke();return}const z=Math.atan2(F,A),ne=$*2;f.save(),f.translate(_,R),f.rotate(z),f.drawImage(I,-ne/2,-ne/2,ne,ne),f.restore()},ct=(f,S)=>{if(S.points.length<2)return;const C=(t==null?void 0:t.width)||800,_=(t==null?void 0:t.height)||600,R=B.width/C,U=B.height/_,$=S.points.map(z=>({x:z.x*R,y:z.y*U}));f.strokeStyle="#FFD700",f.lineWidth=8,f.lineCap="round",f.lineJoin="round",f.setLineDash([]);const A=$.length,F=Math.floor(A*x);if(f.beginPath(),F>0){f.moveTo($[0].x,$[0].y);for(let z=1;z<F;z++)f.lineTo($[z].x,$[z].y);if(F<A){const z=$[F-1],ne=$[F],ae=A*x-F,me=z.x+(ne.x-z.x)*ae,we=z.y+(ne.y-z.y)*ae;f.lineTo(me,we)}}if(f.stroke(),f.setLineDash([]),x>.1&&F>0){let z,ne,ae,me;if(F<A){const we=$[F-1],Ne=$[F],Ds=A*x-F;z=we.x+(Ne.x-we.x)*Ds,ne=we.y+(Ne.y-we.y)*Ds,ae=we.x,me=we.y}else z=$[A-1].x,ne=$[A-1].y,ae=$[Math.max(0,A-2)].x,me=$[Math.max(0,A-2)].y;cs(f,ae,me,z,ne,"#FFD700",20)}},ds=(f,S)=>{if(S.points.length<2)return;const C=(t==null?void 0:t.width)||800,_=(t==null?void 0:t.height)||600,R=B.width/C,U=B.height/_,$=S.points.map(A=>({x:A.x*R,y:A.y*U}));f.strokeStyle="#888888",f.lineWidth=4,f.lineCap="round",f.lineJoin="round",f.setLineDash([10,5]),f.beginPath(),f.moveTo($[0].x,$[0].y);for(let A=1;A<$.length;A++)f.lineTo($[A].x,$[A].y);f.stroke(),f.setLineDash([])},Et=(f,S)=>{S.length<2||(f.strokeStyle="#000000",f.lineWidth=4,f.lineCap="round",f.lineJoin="round",g(f,S))},g=(f,S)=>{if(!(S.length<2)){if(f.beginPath(),f.moveTo(S[0].x,S[0].y),S.length===2)f.lineTo(S[1].x,S[1].y);else{for(let _=1;_<S.length-1;_++){const R=S[_],U=S[_+1],$=(R.x+U.x)/2,A=(R.y+U.y)/2;f.quadraticCurveTo(R.x,R.y,$,A)}const C=S[S.length-1];f.lineTo(C.x,C.y)}f.stroke()}},M=f=>{const S=o.current;if(!S)return{x:0,y:0};const C=S.getBoundingClientRect(),_=S.width/C.width,R=S.height/C.height,U=(f.clientX-C.left)*_,$=(f.clientY-C.top)*R;return{x:U,y:$}},D=f=>{if(f.preventDefault(),f.stopPropagation(),a&&(ve==="guide-animation"||ve==="static-trace"))return;ge&&(clearTimeout(ge),Me(null)),h(!0);const S=M(f);m([S])},se=f=>{if(f.preventDefault(),f.stopPropagation(),a&&(ve==="guide-animation"||ve==="static-trace")||!l)return;const S=M(f),C=[...d,S];m(C)},he=f=>{if(f&&(f.preventDefault(),f.stopPropagation()),!l||!ue)return;if(h(!1),ye(d,ue.points)>st.accuracyThreshold)if(ge&&(clearTimeout(ge),Me(null)),r(ue.id,d),L(C=>[...C,ue.id]),j(C=>({...C,[ue.id]:d})),i<qe.length-1){let C=i+1;for(;C<qe.length;){const _=qe[C];if(!_||_.points.length>=2)break;L(R=>[...R,_.id]),r(_.id,[]),C++}C<qe.length?(c(C),m([]),P(0)):setTimeout(()=>{E("completing")},500)}else setTimeout(()=>{E("completing")},500);else m([]),a&&(tt("guide-animation"),P(0),Be&&(clearTimeout(Be),lt(null)))},ce=f=>{if(f.length<2)return f;const S=(t==null?void 0:t.width)||800,C=(t==null?void 0:t.height)||600,_=B.width/S,R=B.height/C,U=f.map(F=>({x:F.x*_,y:F.y*R})),$=[],A=5;for(let F=0;F<U.length-1;F++){const z=U[F],ne=U[F+1],ae=Math.sqrt(Math.pow(ne.x-z.x,2)+Math.pow(ne.y-z.y,2)),me=Math.ceil(ae/A);for(let we=0;we<=me;we++){const Ne=we/me;$.push({x:z.x+(ne.x-z.x)*Ne,y:z.y+(ne.y-z.y)*Ne})}}return $},ye=(f,S)=>{if(f.length===0||S.length===0||f.length<2)return 0;const C=ce(S);let _=0,R=0;for(const z of f){let ne=1/0;for(const ae of C){const me=Math.sqrt(Math.pow(z.x-ae.x,2)+Math.pow(z.y-ae.y,2));ne=Math.min(ne,me)}ne<=st.tolerance&&(_+=ne,R++)}if(R===0)return 0;const U=_/R,$=R/f.length;return Math.max(0,1-U/st.tolerance)*$};return s.jsx("div",{className:"trace-practice min-h-screen","data-guide-stage":ve,style:{background:"transparent"},children:s.jsx("div",{className:"flex items-center justify-center min-h-screen",children:s.jsxs("div",{className:"relative",style:{transform:"translateY(-120px) translateX(10px)"},children:[J&&s.jsx("div",{className:"fixed inset-0 flex items-center justify-center z-50",children:s.jsx("div",{className:"bg-black bg-opacity-75 text-white px-8 py-6 rounded-lg shadow-lg max-w-[800px] mx-4",children:s.jsxs("div",{className:"text-2xl font-medium text-center leading-relaxed",children:[k,s.jsx("span",{className:"animate-pulse",children:"|"})]})})}),pe&&s.jsx("div",{className:"fixed inset-0 flex items-center justify-center z-50",children:s.jsx("div",{className:"bg-green-600 bg-opacity-90 text-white px-8 py-6 rounded-lg shadow-lg max-w-[800px] mx-4 border-2 border-green-400",children:s.jsxs("div",{className:"text-2xl font-medium text-center leading-relaxed",children:["🎉 ",de,s.jsx("span",{className:"animate-pulse",children:"|"})]})})}),s.jsxs("div",{className:"relative flex items-center justify-center",children:[X&&s.jsx("div",{style:{width:`${B.width}px`,height:`${B.height}px`,transform:`scale(${q})`,opacity:oe,transition:"none"},children:s.jsx("img",{src:X,alt:"背景图片",className:"object-contain w-full h-full"})}),s.jsx("div",{className:"absolute inset-0 flex items-center justify-center",style:{transform:`scale(${q})`,opacity:oe,transition:"none"},children:s.jsx("canvas",{ref:o,width:B.width,height:B.height,className:"bg-transparent",style:{touchAction:"none",userSelect:"none",width:`${B.width}px`,height:`${B.height}px`,position:"relative",margin:0,padding:0,border:"none",outline:"none",display:"block"},onMouseDown:T==="active"?D:void 0,onMouseMove:T==="active"?se:void 0,onMouseUp:T==="active"?he:void 0,onMouseLeave:T==="active"?()=>h(!1):void 0})}),(T==="completing"||T==="showing"||T==="exiting")&&je&&s.jsx("div",{className:"absolute inset-0 flex flex-col items-center justify-center",style:{opacity:xe,transform:`scale(${O})`,transition:"none"},children:s.jsx("img",{src:je,alt:"完整图片",className:"object-contain",style:{width:`${B.width}px`,height:`${B.height}px`},onError:f=>{console.error("奖励图片加载失败"),f.currentTarget.style.display="none"}})})]})]})})})},xs=()=>{const e=We(),{level:t,stage:n}=Pa(),[r,a]=u.useState("level-select"),[o,i]=u.useState(""),[c,l]=u.useState(""),[h,d]=u.useState("free-draw"),[m,v]=u.useState(()=>vo()),[L,b]=u.useState(()=>wo()),[j,x]=u.useState(null),[P,T]=u.useState(!1),E=Ve.useCallback(async(Y,pe)=>{var y,I,W;T(!0);try{if(Y==="trace"&&pe)try{console.log(`尝试加载轨迹文件: /${pe}/trace.json`);const X=await fetch(`/${pe}/trace.json`);if(!X.ok)throw new Error(`HTTP ${X.status}: 无法加载 ${pe}/trace.json`);const le=X.headers.get("content-type");if(!le||!le.includes("application/json"))throw new Error(`文件不是JSON格式: ${le}`);const je=await X.json();console.log("成功加载轨迹数据:",je);const Ae=((y=je.strokes)==null?void 0:y.map((ge,Me)=>({id:`path_${Me}`,points:ge.points||[]})))||[];console.log(`转换后的引导路径数量: ${Ae.length}`),x({sessionId:`trace_${Date.now()}`,guidePaths:Ae,guideImage:je.backgroundImage||"",artworkName:"trace",canvasSize:je.canvasSize||{width:800,height:600},levelStage:pe}),T(!1),console.log("轨迹练习数据设置成功");return}catch(X){if(console.warn(`从文件加载失败: ${(X==null?void 0:X.message)||X}`),console.warn(`文件路径: /${pe}/trace.json`),(I=X==null?void 0:X.message)!=null&&I.includes("404")||(W=X==null?void 0:X.message)!=null&&W.includes("HTTP 404")){K.warning(`${pe} 目录下没有 trace.json 文件，该关卡暂不支持图像描线功能`),T(!1);return}console.warn("尝试使用后端API作为备选方案")}if(Y==="trace")throw new Error("trace类型轨迹文件加载失败，且没有后端支持");const B=await fetch("/api/v1/games/trace/start",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({artwork_name:Y,difficulty:"medium"})});if(!B.ok)throw new Error("启动描线练习失败");const ee=await B.json();if(ee.success)x({sessionId:ee.session_id,guidePaths:ee.guide_paths,guideImage:ee.guide_image,artworkName:ee.artwork_name,canvasSize:ee.canvas_size,levelStage:pe});else throw new Error(ee.error||"启动描线练习失败")}catch(B){console.error("启动描线练习失败:",B),K.error("启动描线练习失败，请重试")}finally{T(!1)}},[]),q=Ve.useCallback(Y=>{E("trace",Y)},[E]);Ve.useEffect(()=>{if(t&&n){let Y=n;!n.includes("-")&&t&&(Y=`${t}-${n}`),i(t),l(Y),a("playing");const pe=Q.find(I=>I.id===t),y=pe==null?void 0:pe.stages.find(I=>I.id===Y);y&&(d(y.type),y.type==="image-trace"&&q(Y))}else t?(i(t),a("stage-select")):a("level-select")},[t,n,q]);const Q=ts,oe=Y=>{i(Y),a("stage-select"),e(`/game/${Y}`)},te=Ve.useCallback(Y=>{l(Y),a("playing"),e(`/game/${o}/${Y}`)},[o,e]),xe=()=>{a("level-select"),i(""),l(""),e("/game")},w=()=>{a("stage-select"),l(""),e(`/game/${o}`)},O=()=>{v(["level-1"]),b(["L1-1"]),localStorage.removeItem("memorybrush-unlocked-levels"),localStorage.removeItem("memorybrush-unlocked-stages"),K.success("游戏进度已重置")},N=async(Y,pe)=>{if(j)try{const y=await fetch("/api/v1/games/trace/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({session_id:j.sessionId,user_paths:pe,completed_path_id:Y})});if(!y.ok)throw new Error("提交描线进度失败");(await y.json()).success&&console.log("路径完成:",Y)}catch(y){console.error("提交描线进度失败:",y)}},k=()=>{K.success("恭喜！描线练习完成！"),w()},G=()=>{K.success("恭喜完成练习！"),setTimeout(()=>{w()},2e3)},J=Y=>{K.success(`恭喜完成！得分：${Y}分`),setTimeout(()=>{w()},2e3)},Z=()=>{switch(h){case"free-draw":return s.jsx(nl,{onBack:w,onComplete:G});case"trace":return P?s.jsx("div",{className:"flex items-center justify-center min-h-screen",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"}),s.jsx("p",{className:"mt-4 text-lg",children:"正在准备描线练习..."})]})}):j?s.jsxs("div",{className:"container mx-auto px-4 py-8",children:[s.jsx("div",{className:"mb-4",children:s.jsx("button",{onClick:w,className:"px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600",children:"← 返回阶段选择"})}),s.jsx(gn,{guidePaths:j.guidePaths,guideImage:j.guideImage,originalCanvasSize:j.canvasSize,levelStage:j.levelStage,onPathComplete:N,onAllComplete:k})]}):s.jsx("div",{className:"flex items-center justify-center min-h-screen",children:s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-lg",children:"描线练习数据加载失败"}),s.jsx("button",{onClick:w,className:"mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"返回阶段选择"})]})});case"image-trace":return P?s.jsx("div",{className:"flex items-center justify-center min-h-screen",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"}),s.jsx("p",{className:"mt-4 text-lg",children:"正在从图片抽取线条..."})]})}):j?s.jsxs("div",{className:"container mx-auto px-4 py-8",children:[s.jsx("div",{className:"mb-4",children:s.jsx("button",{onClick:w,className:"px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600",children:"← 返回阶段选择"})}),s.jsx(gn,{guidePaths:j.guidePaths,guideImage:j.guideImage,originalCanvasSize:j.canvasSize,levelStage:j.levelStage,onPathComplete:N,onAllComplete:k})]}):s.jsx("div",{className:"flex items-center justify-center min-h-screen",children:s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-lg",children:"图像线条抽取失败"}),s.jsx("button",{onClick:w,className:"mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"返回阶段选择"})]})});case"shape-straight":return s.jsx(xn,{onBack:w,onComplete:J,shapeType:"straight"});case"shape-curved":return s.jsx(xn,{onBack:w,onComplete:J,shapeType:"curved"});default:return null}},de=()=>Q.find(Y=>Y.id===o);return s.jsxs("div",{className:"min-h-screen pt-8",style:{background:"transparent"},children:[r==="level-select"&&s.jsx(jo,{onSelectLevel:oe,unlockedLevels:m,onResetProgress:O}),r==="stage-select"&&de()&&s.jsx(So,{level:de(),onSelectStage:te,onBack:xe,unlockedStages:L}),r==="playing"&&Z()]})},{Title:al,Paragraph:ol}=Le,il=()=>{const e=We();return s.jsx("div",{className:"min-h-screen",style:{background:"transparent"},children:s.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ie,{className:"text-center p-12 shadow-soft",children:[s.jsxs("div",{className:"mb-8",children:[s.jsx("div",{className:"w-24 h-24 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx(vn,{className:"text-4xl text-white"})}),s.jsx(al,{level:1,className:"text-blue-600 mb-4",children:"个人资料"}),s.jsxs(ol,{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:["个人资料功能正在开发中，敬请期待！",s.jsx("br",{}),"我们正在为您打造个性化的用户体验。"]})]}),s.jsx(fe,{size:"large",children:s.jsx(H,{size:"large",icon:s.jsx(He,{}),onClick:()=>e("/"),className:"h-12 px-8 text-lg",children:"返回首页"})})]})})})})},{Title:ll,Paragraph:cl}=Le,dl=()=>{const e=We();return s.jsx("div",{className:"min-h-screen",style:{background:"transparent"},children:s.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ie,{className:"text-center p-12 shadow-soft",children:[s.jsxs("div",{className:"mb-8",children:[s.jsx("div",{className:"w-24 h-24 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx(Rs,{className:"text-4xl text-white"})}),s.jsx(ll,{level:1,className:"text-green-600 mb-4",children:"设置"}),s.jsxs(cl,{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:["设置功能正在开发中，敬请期待！",s.jsx("br",{}),"我们正在为您准备贴心的个性化设置选项。"]})]}),s.jsx(fe,{size:"large",children:s.jsx(H,{size:"large",icon:s.jsx(He,{}),onClick:()=>e("/"),className:"h-12 px-8 text-lg",children:"返回首页"})})]})})})})},{Title:ul,Paragraph:hl}=Le,ml=()=>{const e=We();return s.jsx("div",{className:"min-h-screen",style:{background:"transparent"},children:s.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ie,{className:"text-center p-12 shadow-soft",children:[s.jsxs("div",{className:"mb-8",children:[s.jsx("div",{className:"w-24 h-24 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx(nt,{className:"text-4xl text-white"})}),s.jsx(ul,{level:1,className:"text-purple-600 mb-4",children:"作品画廊"}),s.jsxs(hl,{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:["作品画廊功能正在开发中，敬请期待！",s.jsx("br",{}),"我们正在为您打造精美的作品展示空间。"]})]}),s.jsx(fe,{size:"large",children:s.jsx(H,{size:"large",icon:s.jsx(He,{}),onClick:()=>e("/"),className:"h-12 px-8 text-lg",children:"返回首页"})})]})})})})},{Title:fl,Paragraph:pl}=Le,xl=()=>{const e=We();return s.jsx("div",{className:"min-h-screen",style:{background:"transparent"},children:s.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ie,{className:"text-center p-12 shadow-soft",children:[s.jsxs("div",{className:"mb-8",children:[s.jsx("div",{className:"w-24 h-24 bg-gradient-to-br from-yellow-400 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx(Dt,{className:"text-4xl text-white"})}),s.jsx(fl,{level:1,className:"text-orange-600 mb-4",children:"排行榜"}),s.jsxs(pl,{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:["排行榜功能正在开发中，敬请期待！",s.jsx("br",{}),"我们正在为您打造激励性的成就展示系统。"]})]}),s.jsx(fe,{size:"large",children:s.jsx(H,{size:"large",icon:s.jsx(He,{}),onClick:()=>e("/"),className:"h-12 px-8 text-lg",children:"返回首页"})})]})})})})};class gl{constructor(){kt(this,"baseUrl","/traces")}async createSession(t){try{return(await Pe.post(`${this.baseUrl}/sessions`,t)).data}catch(n){throw console.error("创建轨迹会话失败:",n),n}}async getSessions(t=50,n=0){try{return(await Pe.get(`${this.baseUrl}/sessions`,{params:{limit:t,offset:n}})).data}catch(r){throw console.error("获取轨迹会话列表失败:",r),r}}async getSession(t){try{return(await Pe.get(`${this.baseUrl}/sessions/${t}`)).data}catch(n){throw console.error("获取轨迹会话失败:",n),n}}async deleteSession(t){try{return(await Pe.delete(`${this.baseUrl}/sessions/${t}`)).data}catch(n){throw console.error("删除轨迹会话失败:",n),n}}async generateGuidePaths(t,n={}){try{const r={session_id:t,simplification_level:n.simplification_level||"medium",min_stroke_length:n.min_stroke_length||20,merge_distance:n.merge_distance||50};return(await Pe.post(`${this.baseUrl}/sessions/${t}/generate-guide`,r)).data}catch(r){throw console.error("生成引导线失败:",r),r}}exportSessionAsJson(t){const n=JSON.stringify(t,null,2),r=new Blob([n],{type:"application/json"}),a=URL.createObjectURL(r),o=document.createElement("a");o.href=a,o.download=`trace_${t.name}_${Date.now()}.json`,o.click(),URL.revokeObjectURL(a)}async importSessionFromJson(t){return new Promise((n,r)=>{const a=new FileReader;a.onload=o=>{var i;try{const c=(i=o.target)==null?void 0:i.result,l=JSON.parse(c);if(!this.validateTraceSession(l))throw new Error("无效的轨迹数据格式");n(l)}catch(c){r(new Error("解析JSON文件失败: "+c.message))}},a.onerror=()=>{r(new Error("读取文件失败"))},a.readAsText(t)})}validateTraceSession(t){if(!t||typeof t!="object")return!1;const n=["id","name","strokes","canvasSize","createdAt","duration"];for(const r of n)if(!(r in t))return!1;return!(!Array.isArray(t.strokes)||!t.canvasSize||typeof t.canvasSize!="object"||!("width"in t.canvasSize)||!("height"in t.canvasSize))}saveToLocalStorage(t){try{const n=this.getFromLocalStorage(),r=n.findIndex(a=>a.id===t.id);r>=0?n[r]=t:n.push(t),localStorage.setItem("traceSessions",JSON.stringify(n))}catch(n){throw console.error("保存到本地存储失败:",n),n}}getFromLocalStorage(){try{const t=localStorage.getItem("traceSessions");return t?JSON.parse(t):[]}catch(t){return console.error("从本地存储读取失败:",t),[]}}deleteFromLocalStorage(t){try{const r=this.getFromLocalStorage().filter(a=>a.id!==t);localStorage.setItem("traceSessions",JSON.stringify(r))}catch(n){throw console.error("从本地存储删除失败:",n),n}}clearLocalStorage(){try{localStorage.removeItem("traceSessions")}catch(t){throw console.error("清空本地存储失败:",t),t}}calculateSessionStats(t){const n=t.strokes.length;let r=0,a=0;return t.strokes.forEach(o=>{r+=o.points.length;for(let i=1;i<o.points.length;i++){const c=o.points[i].x-o.points[i-1].x,l=o.points[i].y-o.points[i-1].y;a+=Math.sqrt(c*c+l*l)}}),{totalStrokes:n,totalPoints:r,totalLength:a,averageStrokeLength:n>0?a/n:0,duration:t.duration}}}const Te=new gl,{Title:yl,Text:$e}=Le,{Option:Lt}=Bt,bl=()=>{const e=u.useRef(null),[t,n]=u.useState(!1),[r,a]=u.useState(!1),[o,i]=u.useState(!1),[c,l]=u.useState(null),[h,d]=u.useState([]),[m,v]=u.useState(""),[L,b]=u.useState(""),[j,x]=u.useState("#000000"),[P,T]=u.useState(3),[E,q]=u.useState({width:800,height:600}),[Q,oe]=u.useState(null),[te,xe]=u.useState(null),[w,O]=u.useState(0),[N,k]=u.useState(null),[G,J]=u.useState(!1),[Z,de]=u.useState(!1),[Y,pe]=u.useState(null),[y,I]=u.useState(null),W=u.useRef(null),B=u.useCallback(g=>{h.forEach(M=>{if(!(M.points.length<2)){g.strokeStyle="#000000",g.lineWidth=3,g.lineCap="round",g.lineJoin="round",g.beginPath(),g.moveTo(M.points[0].x,M.points[0].y);for(let D=1;D<M.points.length;D++)g.lineTo(M.points[D].x,M.points[D].y);g.stroke()}})},[h]),ee=u.useCallback((g,M)=>{if(!(M.points.length<2)){g.strokeStyle=j,g.lineWidth=P,g.lineCap="round",g.lineJoin="round",g.beginPath(),g.moveTo(M.points[0].x,M.points[0].y);for(let D=1;D<M.points.length;D++)g.lineTo(M.points[D].x,M.points[D].y);g.stroke()}},[j,P]),X=u.useCallback(()=>{const g=e.current;if(!g)return;const M=g.getContext("2d");M&&(M.clearRect(0,0,g.width,g.height),W.current&&M.drawImage(W.current,0,0,g.width,g.height),B(M),c&&c.points.length>0&&ee(M,c))},[h,B,c,ee]);u.useEffect(()=>{if(y){const g=new Image;g.onload=()=>{W.current=g,X()},g.src=y}else W.current=null,X()},[y,X]),u.useEffect(()=>{X()},[c,X]),u.useEffect(()=>{const g=e.current;if(!g)return;const M=g.getContext("2d");if(!M)return;g.width=E.width,g.height=E.height,g.style.width=`${E.width}px`,g.style.height=`${E.height}px`,M.lineCap="round",M.lineJoin="round";const D=ce=>{if(!t||r)return;ce.preventDefault(),ce.stopPropagation();const ye=ce.touches[0],f=g.getBoundingClientRect(),S=g.width/f.width,C=g.height/f.height,_={x:(ye.clientX-f.left)*S,y:(ye.clientY-f.top)*C};i(!0);const U={id:`stroke_${Date.now()}`,points:[_]};l(U)},se=ce=>{if(!o||!c||!t||r)return;ce.preventDefault(),ce.stopPropagation();const ye=ce.touches[0],f=g.getBoundingClientRect(),S=g.width/f.width,C=g.height/f.height,_={x:(ye.clientX-f.left)*S,y:(ye.clientY-f.top)*C},R={...c,points:[...c.points,_]};l(R)},he=ce=>{ce.preventDefault(),ce.stopPropagation(),!(!o||!c||!t||r)&&(i(!1),d(ye=>[...ye,c]),l(null))};return g.addEventListener("touchstart",D,{passive:!1}),g.addEventListener("touchmove",se,{passive:!1}),g.addEventListener("touchend",he,{passive:!1}),X(),()=>{g.removeEventListener("touchstart",D),g.removeEventListener("touchmove",se),g.removeEventListener("touchend",he)}},[E,X,t,r,o,c]);const le=u.useCallback(()=>{const g=e.current;if(!g)return;const M=g.getContext("2d");M&&(M.clearRect(0,0,g.width,g.height),W.current&&M.drawImage(W.current,0,0,g.width,g.height))},[]),je=g=>{const M=e.current;if(!M)return{x:0,y:0};const D=M.getBoundingClientRect(),se=M.width/D.width,he=M.height/D.height,ce=(g.clientX-D.left)*se,ye=(g.clientY-D.top)*he;return{x:ce,y:ye}},Ae=g=>{if(!t||r)return;g.preventDefault(),i(!0);const M=je(g),se={id:`stroke_${Date.now()}`,points:[M]};l(se)},ge=g=>{if(!o||!c||!t||r)return;g.preventDefault();const M=je(g),D={...c,points:[...c.points,M]};l(D)},Me=()=>{!o||!c||(i(!1),d(g=>[...g,c]),l(null))},ve=()=>{if(!m.trim()){K.error("请输入会话名称");return}n(!0),a(!1),xe(Date.now()),O(0),d([]),le(),K.success("开始录制轨迹")},tt=()=>{t&&(r?(N&&O(g=>g+(Date.now()-N)),k(null),a(!1),K.info("恢复录制")):(k(Date.now()),a(!0),i(!1),l(null),K.info("暂停录制")))},Be=()=>{n(!1),a(!1),i(!1),l(null),N&&(O(g=>g+(Date.now()-N)),k(null)),K.success("录制已停止")},lt=()=>{d([]),le(),K.info("画布已清空")},Nt=(g,M=3)=>g.filter(D=>{if(D.points.length<2)return!1;let se=0;for(let he=1;he<D.points.length;he++){const ce=D.points[he].x-D.points[he-1].x,ye=D.points[he].y-D.points[he-1].y;se+=Math.sqrt(ce*ce+ye*ye)}return se>=M}).map(D=>{if(D.points.length<10)return D;const se=qe(D.points),he=ls(se);return{...D,points:he}}),qe=(g,M=2)=>{if(g.length<=3)return g;const D=[];D.push(g[0]);for(let se=1;se<g.length-1;se++){const he=g[se-1],ce=g[se],ye=g[se+1],f=(he.x+ce.x*2+ye.x)/4,S=(he.y+ce.y*2+ye.y)/4;D.push({x:f,y:S})}return D.push(g[g.length-1]),D},ls=(g,M=.5)=>{if(g.length<=3)return g;const D=[g[0]];for(let se=1;se<g.length-1;se++){const he=D[D.length-1],ce=g[se];Math.sqrt(Math.pow(ce.x-he.x,2)+Math.pow(ce.y-he.y,2))>M&&D.push(ce)}return D.push(g[g.length-1]),D},ue=async()=>{if(h.length===0){K.error("没有轨迹数据可保存");return}de(!0);try{const g=te?Date.now()-te:0,M=Nt(h);console.log(`轨迹优化完成: 原始笔画数 ${h.length}, 优化后笔画数 ${M.length}`);const D={name:m,description:L,strokes:M,canvasSize:E,duration:g-w};try{const se=await Te.createSession(D);if(se.success&&se.data)K.success("轨迹数据已保存到服务器"),console.log("保存成功，会话ID:",se.data.id);else throw new Error(se.message)}catch(se){console.warn("API保存失败，使用本地存储:",se);const he={id:`session_${Date.now()}`,name:m,description:L,strokes:h,canvasSize:E,createdAt:new Date().toISOString(),duration:g-w};Te.saveToLocalStorage(he),K.success("轨迹数据已保存到本地")}v(""),b(""),d([]),le()}catch(g){console.error("保存失败:",g),K.error("保存失败，请重试")}finally{de(!1)}},cs=g=>{if(!["image/jpeg","image/jpg","image/png","image/gif","image/webp"].includes(g.type))return K.error("请上传图片文件 (JPG, PNG, GIF, WebP)"),!1;const D=10*1024*1024;if(g.size>D)return K.error("图片文件大小不能超过 10MB"),!1;const se=new FileReader;return se.onload=he=>{var f;const ce=(f=he.target)==null?void 0:f.result;I(ce);const ye=new Image;ye.onload=()=>{const S=Math.min(window.innerWidth*.8,1200),C=Math.min(window.innerHeight*.7,800);let _=ye.width,R=ye.height;_>S&&(R=R*S/_,_=S),R>C&&(_=_*C/R,R=C);const U={width:Math.round(_),height:Math.round(R)};q(U),K.success(`背景图已加载，画布尺寸调整为 ${Math.round(_)} × ${Math.round(R)}`)},ye.src=ce},se.readAsDataURL(g),!1},ct=()=>{I(null),q({width:800,height:600}),K.info("背景图已移除")},ds=()=>{d([]),ct(),le(),K.info("画布和背景图已清空")},Et=()=>{if(h.length===0){K.error("没有轨迹数据可导出");return}const g=te?Date.now()-te:0,M=Nt(h);console.log(`导出轨迹优化完成: 原始笔画数 ${h.length}, 优化后笔画数 ${M.length}`);const D={id:`session_${Date.now()}`,name:m||"未命名会话",description:L,strokes:M,canvasSize:E,createdAt:new Date().toISOString(),duration:g-w};Te.exportSessionAsJson(D),K.success("轨迹数据已导出")};return s.jsxs("div",{className:"trace-recorder min-h-screen p-3 bg-gray-50",children:[s.jsx("div",{className:"max-w-7xl mx-auto",children:s.jsx(re.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},children:s.jsxs("div",{className:"grid grid-cols-1 xl:grid-cols-4 gap-4",children:[s.jsx("div",{className:"xl:col-span-1",children:s.jsx(ie,{size:"small",className:"mb-3",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx($e,{strong:!0,className:"block mb-2",children:"会话信息"}),s.jsxs(fe,{direction:"vertical",className:"w-full",size:"small",children:[s.jsx(Us,{placeholder:"会话名称",value:m,onChange:g=>v(g.target.value),disabled:t,size:"small"}),s.jsx(Us.TextArea,{placeholder:"会话描述（可选）",value:L,onChange:g=>b(g.target.value),disabled:t,rows:2,size:"small"})]})]}),s.jsxs("div",{children:[s.jsx($e,{strong:!0,className:"block mb-2",children:"背景图"}),s.jsxs(fe,{direction:"vertical",className:"w-full",size:"small",children:[s.jsx(H,{icon:s.jsx(nt,{}),onClick:()=>{const g=document.createElement("input");g.type="file",g.accept="image/*",g.onchange=M=>{var se;const D=(se=M.target.files)==null?void 0:se[0];D&&cs(D)},g.click()},disabled:t,className:"w-full",size:"small",children:"上传背景图"}),y&&s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx($e,{type:"success",style:{fontSize:"11px"},children:"✓ 已加载"}),s.jsx(H,{icon:s.jsx(ys,{}),size:"small",danger:!0,onClick:ct,disabled:t,children:"移除"})]})]})]}),s.jsxs("div",{children:[s.jsx($e,{strong:!0,className:"block mb-2",children:"画笔设置"}),s.jsxs(fe,{direction:"vertical",className:"w-full",size:"small",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx($e,{style:{fontSize:"12px",width:"30px"},children:"颜色"}),s.jsx("input",{type:"color",value:j,onChange:g=>x(g.target.value),disabled:t&&!r,className:"w-8 h-6 border rounded"})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx($e,{style:{fontSize:"12px",width:"30px"},children:"大小"}),s.jsxs(Bt,{value:P,onChange:T,disabled:t&&!r,className:"flex-1",size:"small",children:[s.jsx(Lt,{value:1,children:"细 (1px)"}),s.jsx(Lt,{value:3,children:"中 (3px)"}),s.jsx(Lt,{value:5,children:"粗 (5px)"}),s.jsx(Lt,{value:8,children:"很粗 (8px)"})]})]})]})]}),s.jsxs("div",{children:[s.jsx(yl,{level:4,children:"录制控制"}),s.jsxs(fe,{wrap:!0,children:[t?s.jsxs(s.Fragment,{children:[s.jsx(H,{icon:r?s.jsx(ze,{}):s.jsx(kn,{}),onClick:tt,size:"large",children:r?"恢复":"暂停"}),s.jsx(H,{icon:s.jsx($r,{}),onClick:Be,size:"large",danger:!0,children:"停止"})]}):s.jsx(H,{type:"primary",icon:s.jsx(ze,{}),onClick:ve,size:"large",children:"开始录制"}),s.jsx(H,{icon:s.jsx(Nn,{}),onClick:lt,disabled:t&&!r,children:"清空轨迹"}),y&&s.jsx(H,{icon:s.jsx(ys,{}),onClick:ds,disabled:t&&!r,danger:!0,children:"清空全部"})]})]}),s.jsxs("div",{children:[s.jsx($e,{strong:!0,className:"block mb-2",children:"保存操作"}),s.jsxs(fe,{direction:"vertical",className:"w-full",size:"small",children:[s.jsx(H,{type:"primary",icon:s.jsx(En,{}),onClick:ue,disabled:h.length===0||Z,loading:Z,className:"w-full",size:"small",children:Z?"保存中...":"保存轨迹"}),s.jsx(H,{icon:s.jsx(_s,{}),onClick:Et,disabled:h.length===0,className:"w-full",size:"small",children:"导出JSON"}),s.jsx(H,{icon:s.jsx(Cn,{}),onClick:()=>J(!0),disabled:h.length===0,className:"w-full",size:"small",children:"预览轨迹"})]})]}),t&&s.jsxs("div",{className:"mt-4 p-2 bg-gray-100 rounded",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:`w-2 h-2 rounded-full ${r?"bg-yellow-500":"bg-red-500 animate-pulse"}`}),s.jsx($e,{style:{fontSize:"11px"},strong:!0,children:r?"录制已暂停":"正在录制..."})]}),s.jsxs($e,{style:{fontSize:"11px"},type:"secondary",children:["笔画数: ",h.length]})]})]})})}),s.jsx("div",{className:"xl:col-span-3",children:s.jsx(ie,{size:"small",children:s.jsxs("div",{className:"flex flex-col items-center",children:[s.jsx("div",{className:"mb-2 text-center",children:s.jsxs($e,{type:"secondary",style:{fontSize:"12px"},children:["画布: ",E.width," × ",E.height,y&&" • 已加载背景图"]})}),s.jsx("div",{className:"relative",children:s.jsx("canvas",{ref:e,width:E.width,height:E.height,className:`border-2 border-gray-300 rounded-lg cursor-crosshair shadow-md ${y?"bg-transparent":"bg-white"}`,style:{touchAction:"none",userSelect:"none",maxWidth:"100%",maxHeight:"70vh",position:"relative",margin:0,padding:0,border:"none",outline:"none",display:"block"},onMouseDown:Ae,onMouseMove:ge,onMouseUp:Me,onMouseLeave:()=>i(!1)})})]})})})]})})}),s.jsx(Mt,{title:"轨迹预览",open:G,onCancel:()=>J(!1),footer:null,width:900,children:s.jsxs("div",{className:"text-center",children:[s.jsxs($e,{children:["会话: ",m||"未命名"]}),s.jsx("br",{}),s.jsxs($e,{type:"secondary",children:["笔画数: ",h.length]}),s.jsx("br",{}),s.jsxs($e,{type:"secondary",children:["录制时长: ",Math.round((Date.now()-(te||Date.now())-w)/1e3),"秒"]})]})})]})},{Title:jl,Text:Oe}=Le,{Option:gs}=Bt,vl=({onGenerateGuide:e})=>{const[t,n]=u.useState([]),[r,a]=u.useState(!1),[o,i]=u.useState(null),[c,l]=u.useState(!1),[h,d]=u.useState(!1),[m,v]=u.useState(null),[L,b]=u.useState({simplification_level:"medium",min_stroke_length:20,merge_distance:50}),[j,x]=u.useState(!1),P=async()=>{a(!0);try{let N=[];try{const k=await Te.getSessions();N=[...k],console.log("从API加载了",k.length,"个会话")}catch(k){console.warn("API加载失败:",k)}try{const k=Te.getFromLocalStorage();console.log("从本地存储加载了",k.length,"个会话");const G=new Set(N.map(Z=>Z.id)),J=k.filter(Z=>!G.has(Z.id));N=[...N,...J]}catch(k){console.warn("本地存储加载失败:",k)}N.sort((k,G)=>new Date(G.createdAt).getTime()-new Date(k.createdAt).getTime()),n(N),console.log("总共加载了",N.length,"个会话")}catch(N){console.error("加载轨迹会话失败:",N),K.error("加载轨迹会话失败")}finally{a(!1)}};u.useEffect(()=>{P()},[]);const T=async N=>{try{try{await Te.deleteSession(N)}catch(k){console.warn("API删除失败，从本地存储删除:",k),Te.deleteFromLocalStorage(N)}K.success("会话删除成功"),P()}catch(k){console.error("删除会话失败:",k),K.error("删除会话失败")}},E=N=>{i(N),l(!0)},q=N=>{Te.exportSessionAsJson(N),K.success("会话数据已导出")},Q=()=>{const N=document.createElement("input");N.type="file",N.accept=".json",N.onchange=async k=>{var J;const G=(J=k.target.files)==null?void 0:J[0];if(G)try{const Z=await Te.importSessionFromJson(G);try{await Te.createSession({name:Z.name,description:Z.description,strokes:Z.strokes,canvasSize:Z.canvasSize,duration:Z.duration})}catch(de){console.warn("API保存失败，保存到本地存储:",de),Te.saveToLocalStorage(Z)}K.success("会话导入成功"),P()}catch(Z){console.error("导入会话失败:",Z),K.error("导入会话失败: "+Z.message)}},N.click()},oe=N=>{v(N),d(!0)},te=async()=>{if(m){x(!0);try{let N=m,k=!1;try{await Te.getSession(m.id)}catch{console.log("会话不存在于API中，尝试上传..."),k=!0}if(k)try{const J=await Te.createSession({name:m.name,description:m.description,strokes:m.strokes,canvasSize:m.canvasSize,duration:m.duration});if(J.success&&J.data)N=J.data,console.log("会话上传成功，新ID:",N.id);else throw new Error("上传会话失败")}catch(J){console.error("上传会话失败:",J),K.error("无法上传会话到服务器，请检查网络连接");return}const G=await Te.generateGuidePaths(N.id,L);if(G.success&&G.guide_paths)K.success("引导线生成成功"),d(!1),e&&G.canvas_size&&e(G.guide_paths,G.canvas_size,void 0),k&&P();else throw new Error(G.message||"生成引导线失败")}catch(N){console.error("生成引导线失败:",N),K.error("生成引导线失败: "+N.message)}finally{x(!1)}}},xe=N=>{const k=Math.floor(N/1e3),G=Math.floor(k/60),J=k%60;return`${G}:${J.toString().padStart(2,"0")}`},w=N=>Te.calculateSessionStats(N),O=[{title:"会话名称",dataIndex:"name",key:"name",render:(N,k)=>s.jsxs("div",{children:[s.jsx(Oe,{strong:!0,children:N}),k.description&&s.jsx("div",{children:s.jsx(Oe,{type:"secondary",style:{fontSize:"12px"},children:k.description})})]})},{title:"统计信息",key:"stats",render:(N,k)=>{const G=w(k);return s.jsxs(fe,{direction:"vertical",size:"small",children:[s.jsxs(Ge,{color:"blue",children:[G.totalStrokes," 笔画"]}),s.jsxs(Ge,{color:"green",children:[G.totalPoints," 点"]}),s.jsx(Ge,{color:"orange",children:xe(G.duration)})]})}},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",render:N=>new Date(N).toLocaleString()},{title:"操作",key:"actions",render:(N,k)=>s.jsxs(fe,{children:[s.jsx(Ct,{title:"预览",children:s.jsx(H,{icon:s.jsx(Cn,{}),onClick:()=>E(k),size:"small"})}),s.jsx(Ct,{title:"生成引导线",children:s.jsx(H,{icon:s.jsx(ze,{}),onClick:()=>oe(k),size:"small",type:"primary"})}),s.jsx(Ct,{title:"导出",children:s.jsx(H,{icon:s.jsx(_s,{}),onClick:()=>q(k),size:"small"})}),s.jsx(Ct,{title:"删除",children:s.jsx(wn,{title:"确定要删除这个会话吗？",onConfirm:()=>T(k.id),okText:"确定",cancelText:"取消",children:s.jsx(H,{icon:s.jsx(ys,{}),danger:!0,size:"small"})})})]})}];return s.jsxs("div",{className:"trace-manager",children:[s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ie,{children:[s.jsxs("div",{className:"flex justify-between items-center mb-6",children:[s.jsx(jl,{level:3,children:"轨迹管理"}),s.jsxs(fe,{children:[s.jsx(H,{icon:s.jsx(rt,{}),onClick:P,loading:r,children:"刷新"}),s.jsx(H,{icon:s.jsx(Or,{}),onClick:Q,children:"导入会话"})]})]}),s.jsx(Dr,{columns:O,dataSource:t,rowKey:"id",loading:r,pagination:{pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:N=>`共 ${N} 个会话`}})]})}),s.jsx(Mt,{title:`预览会话: ${o==null?void 0:o.name}`,open:c,onCancel:()=>l(!1),footer:null,width:800,children:o&&s.jsx("div",{children:s.jsxs(fe,{direction:"vertical",className:"w-full",children:[s.jsxs("div",{children:[s.jsx(Oe,{strong:!0,children:"描述: "}),s.jsx(Oe,{children:o.description||"无"})]}),s.jsxs("div",{children:[s.jsx(Oe,{strong:!0,children:"画布尺寸: "}),s.jsxs(Oe,{children:[o.canvasSize.width," × ",o.canvasSize.height]})]}),s.jsxs("div",{children:[s.jsx(Oe,{strong:!0,children:"背景图: "}),s.jsx(Oe,{children:"无"})]}),s.jsxs("div",{children:[s.jsx(Oe,{strong:!0,children:"统计信息: "}),(()=>{const N=w(o);return s.jsxs("div",{children:[s.jsxs(Ge,{color:"blue",children:[N.totalStrokes," 笔画"]}),s.jsxs(Ge,{color:"green",children:[N.totalPoints," 点"]}),s.jsx(Ge,{color:"orange",children:xe(N.duration)}),s.jsxs(Ge,{color:"purple",children:[Math.round(N.totalLength)," 像素长度"]})]})})()]})]})})}),s.jsx(Mt,{title:`生成引导线: ${m==null?void 0:m.name}`,open:h,onCancel:()=>d(!1),onOk:te,confirmLoading:j,okText:"生成",cancelText:"取消",children:s.jsxs(fe,{direction:"vertical",className:"w-full",children:[s.jsxs("div",{children:[s.jsx(Oe,{strong:!0,children:"简化级别:"}),s.jsxs(Bt,{value:L.simplification_level,onChange:N=>b(k=>({...k,simplification_level:N})),className:"w-full mt-2",children:[s.jsx(gs,{value:"low",children:"低 (保留更多细节)"}),s.jsx(gs,{value:"medium",children:"中 (平衡)"}),s.jsx(gs,{value:"high",children:"高 (更简化)"})]})]}),s.jsxs("div",{children:[s.jsx(Oe,{strong:!0,children:"最小笔画长度 (像素):"}),s.jsx(zs,{value:L.min_stroke_length,onChange:N=>b(k=>({...k,min_stroke_length:N||20})),min:1,max:200,className:"w-full mt-2"})]}),s.jsxs("div",{children:[s.jsx(Oe,{strong:!0,children:"合并距离 (像素):"}),s.jsx(zs,{value:L.merge_distance,onChange:N=>b(k=>({...k,merge_distance:N||50})),min:1,max:200,className:"w-full mt-2"})]}),s.jsx(Fr,{}),s.jsx(Oe,{type:"secondary",children:"这些设置将影响生成的引导线的复杂度和精度。较高的简化级别会产生更简单的引导线， 较大的合并距离会将相近的笔画合并为一条路径。"})]})})]})},{Title:wl,Text:Je}=Le,Sl=({guidePaths:e,canvasSize:t,onExport:n})=>{const r=u.useRef(null),[a,o]=u.useState(!1),[i,c]=u.useState(0),[l,h]=u.useState(1),[d,m]=u.useState(!0),[v,L]=u.useState(0),b=u.useRef(null),j=()=>{const w=r.current;if(!w)return;const O=w.getContext("2d");O&&O.clearRect(0,0,w.width,w.height)},x=()=>{const w=r.current;if(!w)return;const O=w.getContext("2d");O&&(j(),d?e.forEach((N,k)=>{P(O,N,k<=v)}):v<e.length&&P(O,e[v],!0))},P=(w,O,N)=>{if(!(O.points.length<2)){w.strokeStyle=N?"#FFD700":"#E5E7EB",w.lineWidth=N?3:2,w.lineCap="round",w.lineJoin="round",N?w.setLineDash([10,5]):w.setLineDash([5,5]),w.beginPath(),w.moveTo(O.points[0].x,O.points[0].y);for(let k=1;k<O.points.length;k++)w.lineTo(O.points[k].x,O.points[k].y);w.stroke(),w.setLineDash([])}},T=()=>{const w=r.current;if(!w)return;const O=w.getContext("2d");O&&(j(),d?e.forEach((N,k)=>{k<v?P(O,N,!0):k===v?E(O,N,i):P(O,N,!1)}):v<e.length&&E(O,e[v],i))},E=(w,O,N)=>{if(O.points.length<2)return;const k=O.points.length,G=Math.floor(k*N);if(w.strokeStyle="#FFD700",w.lineWidth=3,w.lineCap="round",w.lineJoin="round",w.setLineDash([10,5]),w.beginPath(),G>0){w.moveTo(O.points[0].x,O.points[0].y);for(let J=1;J<G;J++)w.lineTo(O.points[J].x,O.points[J].y);if(G<k){const J=O.points[G-1],Z=O.points[G],de=k*N-G,Y=J.x+(Z.x-J.x)*de,pe=J.y+(Z.y-J.y)*de;w.lineTo(Y,pe)}}w.stroke(),w.setLineDash([]),N>.1&&G>0&&q(w,O,N)},q=(w,O,N)=>{const k=O.points.length,G=Math.floor(k*N);let J,Z,de,Y;if(G<k){const W=O.points[G-1],B=O.points[G],ee=k*N-G;J=W.x+(B.x-W.x)*ee,Z=W.y+(B.y-W.y)*ee,de=B.x-W.x,Y=B.y-W.y}else{J=O.points[k-1].x,Z=O.points[k-1].y,de=0,Y=0;const W=Math.min(5,k-1);for(let B=1;B<=W;B++){const ee=O.points[k-B],X=O.points[k-B-1];de+=ee.x-X.x,Y+=ee.y-X.y}de/=W,Y/=W}const pe=Math.sqrt(de*de+Y*Y);if(pe<.1)return;de/=pe,Y/=pe;const y=Math.atan2(Y,de),I=15;w.fillStyle="#FFD700",w.strokeStyle="#FFD700",w.lineWidth=2,w.beginPath(),w.moveTo(J,Z),w.lineTo(J-I*Math.cos(y-Math.PI/6),Z-I*Math.sin(y-Math.PI/6)),w.lineTo(J-I*Math.cos(y+Math.PI/6),Z-I*Math.sin(y+Math.PI/6)),w.closePath(),w.fill(),w.stroke()},Q=()=>{a&&(c(w=>{const O=w+.01*l;return O>=1?d&&v<e.length-1?(L(N=>N+1),0):(o(!1),1):O}),b.current=requestAnimationFrame(Q))},oe=()=>{a?(o(!1),b.current&&cancelAnimationFrame(b.current)):o(!0)},te=()=>{o(!1),c(0),L(0),b.current&&cancelAnimationFrame(b.current)},xe=()=>{if(n)n(e);else{const w=JSON.stringify({guidePaths:e,canvasSize:t,exportedAt:new Date().toISOString()},null,2),O=new Blob([w],{type:"application/json"}),N=URL.createObjectURL(O),k=document.createElement("a");k.href=N,k.download=`guide_paths_${Date.now()}.json`,k.click(),URL.revokeObjectURL(N),K.success("引导线数据已导出")}};return u.useEffect(()=>(a&&(b.current=requestAnimationFrame(Q)),()=>{b.current&&cancelAnimationFrame(b.current)}),[a,l,v,d]),u.useEffect(()=>{a?T():x()},[e,i,v,d,a]),e.length===0?s.jsx(ie,{children:s.jsx("div",{className:"text-center py-8",children:s.jsx(Je,{type:"secondary",children:"暂无引导线数据"})})}):s.jsx("div",{className:"guide-preview",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ie,{children:[s.jsxs("div",{className:"flex justify-between items-center mb-4",children:[s.jsx(wl,{level:4,children:"引导线预览"}),s.jsxs(fe,{children:[s.jsx(H,{icon:a?s.jsx(kn,{}):s.jsx(ze,{}),onClick:oe,type:"primary",children:a?"暂停":"播放"}),s.jsx(H,{icon:s.jsx(rt,{}),onClick:te,children:"重置"}),s.jsx(H,{icon:s.jsx(_s,{}),onClick:xe,children:"导出"})]})]}),s.jsx("div",{className:"mb-4 p-4 bg-gray-50 rounded-lg",children:s.jsxs(fe,{direction:"vertical",className:"w-full",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(Je,{children:"动画速度:"}),s.jsx("div",{className:"w-48",children:s.jsx(Sn,{min:.1,max:3,step:.1,value:l,onChange:h,disabled:a})})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(Je,{children:"显示所有路径:"}),s.jsx(Mr,{checked:d,onChange:m,disabled:a})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(Je,{children:"路径信息:"}),s.jsxs(Je,{type:"secondary",children:["共 ",e.length," 条路径，当前第 ",v+1," 条"]})]})]})}),s.jsx("div",{className:"flex justify-center",children:s.jsx("canvas",{ref:r,width:t.width,height:t.height,className:"border-2 border-gray-300 rounded-lg bg-white",style:{maxWidth:"100%",maxHeight:"600px"}})}),s.jsx("div",{className:"mt-4 text-center",children:s.jsxs(fe,{children:[s.jsxs(Je,{type:"secondary",children:["画布尺寸: ",t.width," × ",t.height]}),s.jsxs(Je,{type:"secondary",children:["路径数量: ",e.length]}),s.jsxs(Je,{type:"secondary",children:["总点数: ",e.reduce((w,O)=>w+O.points.length,0)]})]})})]})})})},Nl=()=>{const[e,t]=u.useState("recorder"),[n,r]=u.useState([]),[a,o]=u.useState({width:800,height:600}),[i,c]=u.useState(),l=(h,d,m)=>{r(h),o(d),c(m),t("preview")};return s.jsx("div",{className:"trace-recorder-page min-h-screen",style:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)"},children:s.jsx("div",{className:"container mx-auto px-4 py-6",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsx(ie,{className:"shadow-lg",children:s.jsx(Ur,{activeKey:e,onChange:t,size:"large",className:"trace-tabs",items:[{key:"recorder",label:"📝 轨迹记录",children:s.jsx(bl,{})},{key:"manager",label:"📁 轨迹管理",children:s.jsx(vl,{onGenerateGuide:l})},{key:"preview",label:`🎯 引导线预览 ${n.length>0?`(${n.length})`:""}`,children:s.jsx(Sl,{guidePaths:n,canvasSize:a,backgroundImage:i})}]})})})})})},{Title:yn,Text:bn}=Le,El=()=>{const e=We(),t=[{id:"L1-1",name:"第一关第一阶段",type:"L1"},{id:"L1-2",name:"第一关第二阶段",type:"L1"},{id:"L1-3",name:"第一关第三阶段",type:"L1"},{id:"L1-4",name:"第一关第四阶段",type:"L1"},{id:"L1-5",name:"第一关第五阶段",type:"L1"},{id:"L2-1",name:"第二关第一阶段 (统一背景)",type:"other"},{id:"L3-1",name:"第三关第一阶段 (统一背景)",type:"other"},{id:"L4-1",name:"第四关第一阶段 (统一背景)",type:"other"}],n=r=>{const[a,o]=r.split("-");e(`/game/${a}/${o}`)};return s.jsx("div",{className:"min-h-screen pt-8",style:{background:"transparent"},children:s.jsxs("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[s.jsxs("div",{className:"text-center mb-8",children:[s.jsx(yn,{level:1,className:"text-white",style:{textShadow:"2px 2px 4px rgba(0,0,0,0.8)"},children:"背景图片测试"}),s.jsx(bn,{className:"text-white text-lg",style:{textShadow:"1px 1px 2px rgba(0,0,0,0.8)"},children:"点击下面的按钮测试不同关卡的背景图片切换"})]}),s.jsx("div",{className:"text-center",children:s.jsxs(fe,{direction:"vertical",size:"large",children:[s.jsx(fe,{wrap:!0,size:"middle",children:t.map(r=>s.jsx(H,{type:r.type==="L1"?"primary":"default",size:"large",onClick:()=>n(r.id),style:{minWidth:"200px",height:"50px",fontSize:"16px",backgroundColor:r.type==="L1"?"rgba(139, 92, 246, 0.9)":"rgba(34, 197, 94, 0.9)",borderColor:r.type==="L1"?"rgba(139, 92, 246, 0.9)":"rgba(34, 197, 94, 0.9)",color:"white",backdropFilter:"blur(10px)"},children:r.name},r.id))}),s.jsx(H,{type:"default",size:"large",onClick:()=>e("/"),style:{minWidth:"200px",height:"50px",fontSize:"16px",backgroundColor:"rgba(255, 255, 255, 0.9)",backdropFilter:"blur(10px)"},children:"返回首页"})]})}),s.jsx("div",{className:"mt-12 text-center",children:s.jsxs("div",{className:"inline-block p-6 rounded-lg",style:{backgroundColor:"rgba(255, 255, 255, 0.9)",backdropFilter:"blur(10px)",maxWidth:"600px"},children:[s.jsx(yn,{level:3,children:"测试说明"}),s.jsxs(bn,{children:[s.jsx("strong",{children:"紫色按钮 (L1关卡)"}),"：使用各自目录下的background.jpg文件作为背景图片。",s.jsx("br",{}),"例如：L1-2关卡显示 /L1-2/background.jpg 作为背景。",s.jsx("br",{}),s.jsx("strong",{children:"绿色按钮 (其他关卡)"}),"：统一使用 /background.jpg 作为背景图片。",s.jsx("br",{}),s.jsx("strong",{children:"其他页面"}),"：轨迹记录、首页等非游戏页面也使用统一背景 /background.jpg。"]})]})})]})})};class kl extends u.Component{constructor(n){super(n);kt(this,"handleReload",()=>{window.location.reload()});kt(this,"handleGoHome",()=>{window.location.href="/"});this.state={hasError:!1}}static getDerivedStateFromError(n){return{hasError:!0,error:n}}componentDidCatch(n,r){console.error("ErrorBoundary caught an error:",n,r),this.setState({error:n,errorInfo:r})}render(){return this.state.hasError?s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:s.jsx("div",{className:"max-w-2xl mx-auto p-8",children:s.jsx(zr,{status:"error",title:"哎呀，出现了一些问题",subTitle:"不用担心，这不是您的错误。我们的技术团队会尽快解决这个问题。",extra:[s.jsx(H,{type:"primary",icon:s.jsx(rt,{}),onClick:this.handleReload,size:"large",children:"重新加载"},"reload"),s.jsx(H,{icon:s.jsx(Tn,{}),onClick:this.handleGoHome,size:"large",children:"返回首页"},"home")],children:s.jsxs("div",{className:"text-center mt-8",children:[s.jsx("p",{className:"text-gray-600 mb-4",children:"如果问题持续存在，请联系我们的客服团队："}),s.jsxs("p",{className:"text-gray-600",children:["📧 <EMAIL>",s.jsx("br",{}),"📞 400-123-4567"]}),s.jsx("div",{className:"mt-6 p-4 bg-orange-50 rounded-lg",children:s.jsx("p",{className:"text-orange-600 font-medium",children:"💝 记忆画笔团队始终为您提供温暖的技术支持"})})]})})})}):this.props.children}}const Cl=({visible:e,onClose:t,onEnterFullscreen:n})=>{const r=()=>{n(),t()};return s.jsx(Mt,{open:e,onCancel:t,footer:null,centered:!0,width:500,closable:!1,maskClosable:!1,className:"fullscreen-prompt-modal",children:s.jsxs("div",{className:"text-center p-6",children:[s.jsxs("div",{className:"mb-6",children:[s.jsx("div",{className:"w-20 h-20 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(bs,{className:"text-3xl text-white"})}),s.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-3",children:"获得最佳体验"}),s.jsxs("p",{className:"text-lg text-gray-600 leading-relaxed",children:["为了获得最佳的绘画体验，建议您使用全屏模式。",s.jsx("br",{}),"全屏模式可以让您专注于创作，减少干扰。"]})]}),s.jsxs(fe,{size:"large",className:"w-full justify-center",children:[s.jsx(H,{type:"primary",size:"large",icon:s.jsx(bs,{}),onClick:r,className:"h-12 px-8 text-lg bg-gradient-to-r from-purple-500 to-purple-600 border-0",children:"进入全屏"}),s.jsx(H,{size:"large",icon:s.jsx(Pn,{}),onClick:t,className:"h-12 px-8 text-lg",children:"稍后再说"})]}),s.jsx("div",{className:"mt-6 text-sm text-gray-500",children:s.jsx("p",{children:"您也可以随时通过右上角的全屏按钮切换全屏模式"})})]})})},Tl=()=>{const e=We(),t=Gt(),[n,r]=u.useState(!1),[a,o]=u.useState(!!document.fullscreenElement);Ve.useEffect(()=>{const l=()=>{o(!!document.fullscreenElement)};return document.addEventListener("fullscreenchange",l),()=>{document.removeEventListener("fullscreenchange",l)}},[]);const i=async()=>{try{document.fullscreenElement?await document.exitFullscreen():await document.documentElement.requestFullscreen()}catch(l){console.error("全屏切换失败:",l)}},c=[{key:"home",icon:s.jsx(Tn,{}),label:"首页",onClick:()=>{e("/"),r(!1)}},{key:"game",icon:s.jsx(ze,{}),label:"开始绘画",onClick:()=>{e("/game"),r(!1)}},{key:"gallery",icon:s.jsx(nt,{}),label:"作品画廊",onClick:()=>{e("/gallery"),r(!1)}},{key:"leaderboard",icon:s.jsx(Dt,{}),label:"排行榜",onClick:()=>{e("/leaderboard"),r(!1)}},{key:"trace-recorder",icon:s.jsx(qr,{}),label:"轨迹记录",onClick:()=>{e("/trace-recorder"),r(!1)}},{key:"settings",icon:s.jsx(Rs,{}),label:"设置",onClick:()=>{e("/settings"),r(!1)}},{type:"divider"},{key:"fullscreen",icon:a?s.jsx(Hr,{}):s.jsx(bs,{}),label:a?"退出全屏":"进入全屏",onClick:()=>{i(),r(!1)}}];return s.jsxs("div",{className:"fixed top-6 right-6 z-50",children:[s.jsx(Jr,{children:n&&s.jsx(re.div,{initial:{opacity:0,scale:.8,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.8,y:20},transition:{duration:.2},className:"mb-4",children:s.jsx("div",{className:"bg-white rounded-2xl shadow-2xl border border-gray-100 overflow-hidden",children:s.jsx("div",{className:"p-2",children:c.map((l,h)=>{if(l.type==="divider")return s.jsx("div",{className:"h-px bg-gray-200 my-2"},h);const d=l.key==="home"&&t.pathname==="/"||l.key==="game"&&t.pathname.startsWith("/game")||l.key==="gallery"&&t.pathname==="/gallery"||l.key==="leaderboard"&&t.pathname==="/leaderboard"||l.key==="trace-recorder"&&t.pathname==="/trace-recorder"||l.key==="settings"&&t.pathname==="/settings";return s.jsx(H,{type:d?"primary":"text",icon:l.icon,onClick:l.onClick,className:"w-full justify-start mb-1 h-12 text-left",size:"large",children:l.label},l.key)})})})})}),s.jsx(re.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(H,{type:"primary",shape:"circle",size:"large",icon:n?s.jsx(Pn,{}):s.jsx(Br,{}),onClick:()=>r(!n),className:"w-14 h-14 bg-gradient-to-br from-purple-500 to-purple-600 border-0 shadow-2xl hover:shadow-3xl",style:{fontSize:"18px"}})})]})},{Content:Pl}=At,{Title:Rl,Paragraph:_l}=Le,Ll=()=>{const[e,t]=Ve.useState(!1),n=We();xo(),Ve.useEffect(()=>{if(!localStorage.getItem("memorybrush-fullscreen-prompt-seen")&&!document.fullscreenElement){const i=setTimeout(()=>{t(!0)},2e3);return()=>clearTimeout(i)}},[]);const r=async()=>{try{await document.documentElement.requestFullscreen(),localStorage.setItem("memorybrush-fullscreen-prompt-seen","true")}catch(o){console.error("进入全屏失败:",o)}},a=()=>{t(!1),localStorage.setItem("memorybrush-fullscreen-prompt-seen","true")};return s.jsxs(kl,{children:[s.jsx(At,{className:"min-h-screen",style:{background:"transparent"},children:s.jsx(At,{children:s.jsx(At,{className:"transition-all duration-300",children:s.jsx(Pl,{className:"min-h-screen",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:s.jsxs(Wa,{children:[s.jsx(De,{path:"/",element:s.jsx(go,{})}),s.jsx(De,{path:"/game",element:s.jsx(xs,{})}),s.jsx(De,{path:"/game/:level",element:s.jsx(xs,{})}),s.jsx(De,{path:"/game/:level/:stage",element:s.jsx(xs,{})}),s.jsx(De,{path:"/profile",element:s.jsx(il,{})}),s.jsx(De,{path:"/settings",element:s.jsx(dl,{})}),s.jsx(De,{path:"/gallery",element:s.jsx(ml,{})}),s.jsx(De,{path:"/leaderboard",element:s.jsx(xl,{})}),s.jsx(De,{path:"/trace-recorder",element:s.jsx(Nl,{})}),s.jsx(De,{path:"/background-test",element:s.jsx(El,{})}),s.jsx(De,{path:"*",element:s.jsx("div",{className:"min-h-screen pt-8",style:{background:"transparent"},children:s.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ie,{className:"text-center p-12 shadow-soft",children:[s.jsxs("div",{className:"mb-8",children:[s.jsx("div",{className:"w-24 h-24 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx("span",{className:"text-4xl text-white",children:"🔍"})}),s.jsx(Rl,{level:1,className:"text-gray-600 mb-4",children:"页面建设中"}),s.jsxs(_l,{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:["您访问的页面正在建设中，敬请期待！",s.jsx("br",{}),"我们正在努力为您提供更好的功能体验。"]})]}),s.jsx(fe,{size:"large",children:s.jsx(H,{size:"large",icon:s.jsx(He,{}),onClick:()=>n("/"),className:"h-12 px-8 text-lg",children:"返回首页"})})]})})})})})]})})})})})}),s.jsx(Tl,{}),s.jsx(Cl,{visible:e,onClose:a,onEnterFullscreen:r})]})},Al={token:{colorPrimary:"#8b5cf6",colorSuccess:"#10b981",colorWarning:"#f59e0b",colorError:"#ef4444",borderRadius:8,fontSize:16,fontFamily:"Inter, system-ui, sans-serif"},components:{Button:{fontSize:18,paddingContentHorizontal:24,paddingContentVertical:12},Input:{fontSize:16,paddingBlock:12},Card:{borderRadius:12}}};js.createRoot(document.getElementById("root")).render(s.jsx(Ve.StrictMode,{children:s.jsx(Va,{children:s.jsx(Wr,{locale:po,theme:Al,componentSize:"large",children:s.jsx(Ll,{})})})}));
//# sourceMappingURL=index-Dabka6em.js.map
