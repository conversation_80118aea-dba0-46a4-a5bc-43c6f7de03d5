var yr=Object.defineProperty;var br=(e,t,n)=>t in e?yr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var vt=(e,t,n)=>br(e,typeof t!="symbol"?t+"":t,n);import{r as u,c as vr,b as jr,g as wr,R as Ue}from"./vendor-BTWMFwqw.js";import{T as Te,C as ce,R as De,a as Ke,b as _t,P as fn,c as Ss,d as ot,e as pn,f as Ns,g as Es,h as Sr,i as It,B as J,j as gn,k as Qe,l as Nr,m as Er,n as kr,o as qe,s as K,S as me,p as xn,q as Cr,r as yn,t as bn,u as it,v as Is,I as As,w as Be,x as Pr,y as Lt,z as Os,A as hs,D as vn,E as Tr,F as ks,G as jn,M as At,H as Rr,J as _r,K as $s,L as Ir,N as jt,O as Ar,Q as Or,U as $r,V as wn,W as ms,X as Sn,Y as Lr,Z as Dr,_ as Fr,$ as Ct,a0 as Mr}from"./antd-CEDOBsB3.js";import{m as ae,A as Br}from"./animation-BAuEqiwG.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))r(a);new MutationObserver(a=>{for(const o of a)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(a){const o={};return a.integrity&&(o.integrity=a.integrity),a.referrerPolicy&&(o.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?o.credentials="include":a.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(a){if(a.ep)return;a.ep=!0;const o=n(a);fetch(a.href,o)}})();var Nn={exports:{}},Dt={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ur=u,zr=Symbol.for("react.element"),qr=Symbol.for("react.fragment"),Wr=Object.prototype.hasOwnProperty,Hr=Ur.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Jr={key:!0,ref:!0,__self:!0,__source:!0};function En(e,t,n){var r,a={},o=null,i=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)Wr.call(t,r)&&!Jr.hasOwnProperty(r)&&(a[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)a[r]===void 0&&(a[r]=t[r]);return{$$typeof:zr,type:e,key:o,ref:i,props:a,_owner:Hr.current}}Dt.Fragment=qr;Dt.jsx=En;Dt.jsxs=En;Nn.exports=Dt;var s=Nn.exports,fs={},Ls=vr;fs.createRoot=Ls.createRoot,fs.hydrateRoot=Ls.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function lt(){return lt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},lt.apply(this,arguments)}var ze;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(ze||(ze={}));const Ds="popstate";function Gr(e){e===void 0&&(e={});function t(r,a){let{pathname:o,search:i,hash:l}=r.location;return ps("",{pathname:o,search:i,hash:l},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function n(r,a){return typeof a=="string"?a:Cn(a)}return Yr(t,n,null,e)}function ye(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function kn(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Vr(){return Math.random().toString(36).substr(2,8)}function Fs(e,t){return{usr:e.state,key:e.key,idx:t}}function ps(e,t,n,r){return n===void 0&&(n=null),lt({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Ze(t):t,{state:n,key:t&&t.key||r||Vr()})}function Cn(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function Ze(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function Yr(e,t,n,r){r===void 0&&(r={});let{window:a=document.defaultView,v5Compat:o=!1}=r,i=a.history,l=ze.Pop,c=null,h=d();h==null&&(h=0,i.replaceState(lt({},i.state,{idx:h}),""));function d(){return(i.state||{idx:null}).idx}function f(){l=ze.Pop;let g=d(),T=g==null?null:g-h;h=g,c&&c({action:l,location:v.location,delta:T})}function j(g,T){l=ze.Push;let _=ps(v.location,g,T);h=d()+1;let P=Fs(_,h),W=v.createHref(_);try{i.pushState(P,"",W)}catch(Z){if(Z instanceof DOMException&&Z.name==="DataCloneError")throw Z;a.location.assign(W)}o&&c&&c({action:l,location:v.location,delta:1})}function I(g,T){l=ze.Replace;let _=ps(v.location,g,T);h=d();let P=Fs(_,h),W=v.createHref(_);i.replaceState(P,"",W),o&&c&&c({action:l,location:v.location,delta:0})}function b(g){let T=a.location.origin!=="null"?a.location.origin:a.location.href,_=typeof g=="string"?g:Cn(g);return _=_.replace(/ $/,"%20"),ye(T,"No window.location.(origin|href) available to create URL for href: "+_),new URL(_,T)}let v={get action(){return l},get location(){return e(a,i)},listen(g){if(c)throw new Error("A history only accepts one active listener");return a.addEventListener(Ds,f),c=g,()=>{a.removeEventListener(Ds,f),c=null}},createHref(g){return t(a,g)},createURL:b,encodeLocation(g){let T=b(g);return{pathname:T.pathname,search:T.search,hash:T.hash}},push:j,replace:I,go(g){return i.go(g)}};return v}var Ms;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Ms||(Ms={}));function Xr(e,t,n){return n===void 0&&(n="/"),Kr(e,t,n)}function Kr(e,t,n,r){let a=typeof t=="string"?Ze(t):t,o=Rn(a.pathname||"/",n);if(o==null)return null;let i=Pn(e);Qr(i);let l=null;for(let c=0;l==null&&c<i.length;++c){let h=da(o);l=ia(i[c],h)}return l}function Pn(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let a=(o,i,l)=>{let c={relativePath:l===void 0?o.path||"":l,caseSensitive:o.caseSensitive===!0,childrenIndex:i,route:o};c.relativePath.startsWith("/")&&(ye(c.relativePath.startsWith(r),'Absolute route path "'+c.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),c.relativePath=c.relativePath.slice(r.length));let h=He([r,c.relativePath]),d=n.concat(c);o.children&&o.children.length>0&&(ye(o.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+h+'".')),Pn(o.children,t,d,h)),!(o.path==null&&!o.index)&&t.push({path:h,score:aa(h,o.index),routesMeta:d})};return e.forEach((o,i)=>{var l;if(o.path===""||!((l=o.path)!=null&&l.includes("?")))a(o,i);else for(let c of Tn(o.path))a(o,i,c)}),t}function Tn(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,a=n.endsWith("?"),o=n.replace(/\?$/,"");if(r.length===0)return a?[o,""]:[o];let i=Tn(r.join("/")),l=[];return l.push(...i.map(c=>c===""?o:[o,c].join("/"))),a&&l.push(...i),l.map(c=>e.startsWith("/")&&c===""?"/":c)}function Qr(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:oa(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const Zr=/^:[\w-]+$/,ea=3,ta=2,sa=1,na=10,ra=-2,Bs=e=>e==="*";function aa(e,t){let n=e.split("/"),r=n.length;return n.some(Bs)&&(r+=ra),t&&(r+=ta),n.filter(a=>!Bs(a)).reduce((a,o)=>a+(Zr.test(o)?ea:o===""?sa:na),r)}function oa(e,t){return e.length===t.length&&e.slice(0,-1).every((r,a)=>r===t[a])?e[e.length-1]-t[t.length-1]:0}function ia(e,t,n){let{routesMeta:r}=e,a={},o="/",i=[];for(let l=0;l<r.length;++l){let c=r[l],h=l===r.length-1,d=o==="/"?t:t.slice(o.length)||"/",f=la({path:c.relativePath,caseSensitive:c.caseSensitive,end:h},d),j=c.route;if(!f)return null;Object.assign(a,f.params),i.push({params:a,pathname:He([o,f.pathname]),pathnameBase:ga(He([o,f.pathnameBase])),route:j}),f.pathnameBase!=="/"&&(o=He([o,f.pathnameBase]))}return i}function la(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=ca(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let o=a[0],i=o.replace(/(.)\/+$/,"$1"),l=a.slice(1);return{params:r.reduce((h,d,f)=>{let{paramName:j,isOptional:I}=d;if(j==="*"){let v=l[f]||"";i=o.slice(0,o.length-v.length).replace(/(.)\/+$/,"$1")}const b=l[f];return I&&!b?h[j]=void 0:h[j]=(b||"").replace(/%2F/g,"/"),h},{}),pathname:o,pathnameBase:i,pattern:e}}function ca(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),kn(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,l,c)=>(r.push({paramName:l,isOptional:c!=null}),c?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function da(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return kn(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Rn(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function ua(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:a=""}=typeof e=="string"?Ze(e):e;return{pathname:n?n.startsWith("/")?n:ha(n,t):t,search:xa(r),hash:ya(a)}}function ha(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?n.length>1&&n.pop():a!=="."&&n.push(a)}),n.length>1?n.join("/"):"/"}function as(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function ma(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function fa(e,t){let n=ma(e);return t?n.map((r,a)=>a===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function pa(e,t,n,r){r===void 0&&(r=!1);let a;typeof e=="string"?a=Ze(e):(a=lt({},e),ye(!a.pathname||!a.pathname.includes("?"),as("?","pathname","search",a)),ye(!a.pathname||!a.pathname.includes("#"),as("#","pathname","hash",a)),ye(!a.search||!a.search.includes("#"),as("#","search","hash",a)));let o=e===""||a.pathname==="",i=o?"/":a.pathname,l;if(i==null)l=n;else{let f=t.length-1;if(!r&&i.startsWith("..")){let j=i.split("/");for(;j[0]==="..";)j.shift(),f-=1;a.pathname=j.join("/")}l=f>=0?t[f]:"/"}let c=ua(a,l),h=i&&i!=="/"&&i.endsWith("/"),d=(o||i===".")&&n.endsWith("/");return!c.pathname.endsWith("/")&&(h||d)&&(c.pathname+="/"),c}const He=e=>e.join("/").replace(/\/\/+/g,"/"),ga=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),xa=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,ya=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function ba(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const _n=["post","put","patch","delete"];new Set(_n);const va=["get",..._n];new Set(va);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ct(){return ct=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ct.apply(this,arguments)}const Cs=u.createContext(null),ja=u.createContext(null),Ft=u.createContext(null),Mt=u.createContext(null),Ve=u.createContext({outlet:null,matches:[],isDataRoute:!1}),In=u.createContext(null);function Bt(){return u.useContext(Mt)!=null}function Ut(){return Bt()||ye(!1),u.useContext(Mt).location}function An(e){u.useContext(Ft).static||u.useLayoutEffect(e)}function Fe(){let{isDataRoute:e}=u.useContext(Ve);return e?$a():wa()}function wa(){Bt()||ye(!1);let e=u.useContext(Cs),{basename:t,future:n,navigator:r}=u.useContext(Ft),{matches:a}=u.useContext(Ve),{pathname:o}=Ut(),i=JSON.stringify(fa(a,n.v7_relativeSplatPath)),l=u.useRef(!1);return An(()=>{l.current=!0}),u.useCallback(function(h,d){if(d===void 0&&(d={}),!l.current)return;if(typeof h=="number"){r.go(h);return}let f=pa(h,JSON.parse(i),o,d.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:He([t,f.pathname])),(d.replace?r.replace:r.push)(f,d.state,d)},[t,r,i,o,e])}function Sa(){let{matches:e}=u.useContext(Ve),t=e[e.length-1];return t?t.params:{}}function Na(e,t){return Ea(e,t)}function Ea(e,t,n,r){Bt()||ye(!1);let{navigator:a}=u.useContext(Ft),{matches:o}=u.useContext(Ve),i=o[o.length-1],l=i?i.params:{};i&&i.pathname;let c=i?i.pathnameBase:"/";i&&i.route;let h=Ut(),d;if(t){var f;let g=typeof t=="string"?Ze(t):t;c==="/"||(f=g.pathname)!=null&&f.startsWith(c)||ye(!1),d=g}else d=h;let j=d.pathname||"/",I=j;if(c!=="/"){let g=c.replace(/^\//,"").split("/");I="/"+j.replace(/^\//,"").split("/").slice(g.length).join("/")}let b=Xr(e,{pathname:I}),v=Ra(b&&b.map(g=>Object.assign({},g,{params:Object.assign({},l,g.params),pathname:He([c,a.encodeLocation?a.encodeLocation(g.pathname).pathname:g.pathname]),pathnameBase:g.pathnameBase==="/"?c:He([c,a.encodeLocation?a.encodeLocation(g.pathnameBase).pathname:g.pathnameBase])})),o,n,r);return t&&v?u.createElement(Mt.Provider,{value:{location:ct({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:ze.Pop}},v):v}function ka(){let e=Oa(),t=ba(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return u.createElement(u.Fragment,null,u.createElement("h2",null,"Unexpected Application Error!"),u.createElement("h3",{style:{fontStyle:"italic"}},t),n?u.createElement("pre",{style:a},n):null,null)}const Ca=u.createElement(ka,null);class Pa extends u.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?u.createElement(Ve.Provider,{value:this.props.routeContext},u.createElement(In.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Ta(e){let{routeContext:t,match:n,children:r}=e,a=u.useContext(Cs);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),u.createElement(Ve.Provider,{value:t},r)}function Ra(e,t,n,r){var a;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var o;if(!n)return null;if(n.errors)e=n.matches;else if((o=r)!=null&&o.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let i=e,l=(a=n)==null?void 0:a.errors;if(l!=null){let d=i.findIndex(f=>f.route.id&&(l==null?void 0:l[f.route.id])!==void 0);d>=0||ye(!1),i=i.slice(0,Math.min(i.length,d+1))}let c=!1,h=-1;if(n&&r&&r.v7_partialHydration)for(let d=0;d<i.length;d++){let f=i[d];if((f.route.HydrateFallback||f.route.hydrateFallbackElement)&&(h=d),f.route.id){let{loaderData:j,errors:I}=n,b=f.route.loader&&j[f.route.id]===void 0&&(!I||I[f.route.id]===void 0);if(f.route.lazy||b){c=!0,h>=0?i=i.slice(0,h+1):i=[i[0]];break}}}return i.reduceRight((d,f,j)=>{let I,b=!1,v=null,g=null;n&&(I=l&&f.route.id?l[f.route.id]:void 0,v=f.route.errorElement||Ca,c&&(h<0&&j===0?(La("route-fallback"),b=!0,g=null):h===j&&(b=!0,g=f.route.hydrateFallbackElement||null)));let T=t.concat(i.slice(0,j+1)),_=()=>{let P;return I?P=v:b?P=g:f.route.Component?P=u.createElement(f.route.Component,null):f.route.element?P=f.route.element:P=d,u.createElement(Ta,{match:f,routeContext:{outlet:d,matches:T,isDataRoute:n!=null},children:P})};return n&&(f.route.ErrorBoundary||f.route.errorElement||j===0)?u.createElement(Pa,{location:n.location,revalidation:n.revalidation,component:v,error:I,children:_(),routeContext:{outlet:null,matches:T,isDataRoute:!0}}):_()},null)}var On=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(On||{}),$n=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}($n||{});function _a(e){let t=u.useContext(Cs);return t||ye(!1),t}function Ia(e){let t=u.useContext(ja);return t||ye(!1),t}function Aa(e){let t=u.useContext(Ve);return t||ye(!1),t}function Ln(e){let t=Aa(),n=t.matches[t.matches.length-1];return n.route.id||ye(!1),n.route.id}function Oa(){var e;let t=u.useContext(In),n=Ia(),r=Ln();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function $a(){let{router:e}=_a(On.UseNavigateStable),t=Ln($n.UseNavigateStable),n=u.useRef(!1);return An(()=>{n.current=!0}),u.useCallback(function(a,o){o===void 0&&(o={}),n.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,ct({fromRouteId:t},o)))},[e,t])}const Us={};function La(e,t,n){Us[e]||(Us[e]=!0)}function Da(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function Ae(e){ye(!1)}function Fa(e){let{basename:t="/",children:n=null,location:r,navigationType:a=ze.Pop,navigator:o,static:i=!1,future:l}=e;Bt()&&ye(!1);let c=t.replace(/^\/*/,"/"),h=u.useMemo(()=>({basename:c,navigator:o,static:i,future:ct({v7_relativeSplatPath:!1},l)}),[c,l,o,i]);typeof r=="string"&&(r=Ze(r));let{pathname:d="/",search:f="",hash:j="",state:I=null,key:b="default"}=r,v=u.useMemo(()=>{let g=Rn(d,c);return g==null?null:{location:{pathname:g,search:f,hash:j,state:I,key:b},navigationType:a}},[c,d,f,j,I,b,a]);return v==null?null:u.createElement(Ft.Provider,{value:h},u.createElement(Mt.Provider,{children:n,value:v}))}function Ma(e){let{children:t,location:n}=e;return Na(gs(t),n)}new Promise(()=>{});function gs(e,t){t===void 0&&(t=[]);let n=[];return u.Children.forEach(e,(r,a)=>{if(!u.isValidElement(r))return;let o=[...t,a];if(r.type===u.Fragment){n.push.apply(n,gs(r.props.children,o));return}r.type!==Ae&&ye(!1),!r.props.index||!r.props.children||ye(!1);let i={id:r.props.id||o.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(i.children=gs(r.props.children,o)),n.push(i)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const Ba="6";try{window.__reactRouterVersion=Ba}catch{}const Ua="startTransition",zs=jr[Ua];function za(e){let{basename:t,children:n,future:r,window:a}=e,o=u.useRef();o.current==null&&(o.current=Gr({window:a,v5Compat:!0}));let i=o.current,[l,c]=u.useState({action:i.action,location:i.location}),{v7_startTransition:h}=r||{},d=u.useCallback(f=>{h&&zs?zs(()=>c(f)):c(f)},[c,h]);return u.useLayoutEffect(()=>i.listen(d),[i,d]),u.useEffect(()=>Da(r),[r]),u.createElement(Fa,{basename:t,children:n,location:l.location,navigationType:l.action,navigator:i,future:r})}var qs;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(qs||(qs={}));var Ws;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Ws||(Ws={}));var zt={},Dn={exports:{}};(function(e){function t(n){return n&&n.__esModule?n:{default:n}}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})(Dn);var qt=Dn.exports,Wt={};Object.defineProperty(Wt,"__esModule",{value:!0});Wt.default=void 0;var qa={items_per_page:"条/页",jump_to:"跳至",jump_to_confirm:"确定",page:"页",prev_page:"上一页",next_page:"下一页",prev_5:"向前 5 页",next_5:"向后 5 页",prev_3:"向前 3 页",next_3:"向后 3 页",page_size:"页码"};Wt.default=qa;var Ht={},ut={},Jt={},Fn={exports:{}},Mn={exports:{}},Bn={exports:{}},Un={exports:{}};(function(e){function t(n){"@babel/helpers - typeof";return e.exports=t=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(r){return typeof r}:function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})(Un);var zn=Un.exports,qn={exports:{}};(function(e){var t=zn.default;function n(r,a){if(t(r)!="object"||!r)return r;var o=r[Symbol.toPrimitive];if(o!==void 0){var i=o.call(r,a||"default");if(t(i)!="object")return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return(a==="string"?String:Number)(r)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports})(qn);var Wa=qn.exports;(function(e){var t=zn.default,n=Wa;function r(a){var o=n(a,"string");return t(o)=="symbol"?o:o+""}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports})(Bn);var Ha=Bn.exports;(function(e){var t=Ha;function n(r,a,o){return(a=t(a))in r?Object.defineProperty(r,a,{value:o,enumerable:!0,configurable:!0,writable:!0}):r[a]=o,r}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports})(Mn);var Ja=Mn.exports;(function(e){var t=Ja;function n(a,o){var i=Object.keys(a);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(a);o&&(l=l.filter(function(c){return Object.getOwnPropertyDescriptor(a,c).enumerable})),i.push.apply(i,l)}return i}function r(a){for(var o=1;o<arguments.length;o++){var i=arguments[o]!=null?arguments[o]:{};o%2?n(Object(i),!0).forEach(function(l){t(a,l,i[l])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(i)):n(Object(i)).forEach(function(l){Object.defineProperty(a,l,Object.getOwnPropertyDescriptor(i,l))})}return a}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports})(Fn);var Ga=Fn.exports,Gt={};Object.defineProperty(Gt,"__esModule",{value:!0});Gt.commonLocale=void 0;Gt.commonLocale={yearFormat:"YYYY",dayFormat:"D",cellMeridiemFormat:"A",monthBeforeYear:!0};var Va=qt.default;Object.defineProperty(Jt,"__esModule",{value:!0});Jt.default=void 0;var Hs=Va(Ga),Ya=Gt,Xa=(0,Hs.default)((0,Hs.default)({},Ya.commonLocale),{},{locale:"zh_CN",today:"今天",now:"此刻",backToToday:"返回今天",ok:"确定",timeSelect:"选择时间",dateSelect:"选择日期",weekSelect:"选择周",clear:"清除",week:"周",month:"月",year:"年",previousMonth:"上个月 (翻页上键)",nextMonth:"下个月 (翻页下键)",monthSelect:"选择月份",yearSelect:"选择年份",decadeSelect:"选择年代",previousYear:"上一年 (Control键加左方向键)",nextYear:"下一年 (Control键加右方向键)",previousDecade:"上一年代",nextDecade:"下一年代",previousCentury:"上一世纪",nextCentury:"下一世纪",yearFormat:"YYYY年",cellDateFormat:"D",monthBeforeYear:!1});Jt.default=Xa;var ht={};Object.defineProperty(ht,"__esModule",{value:!0});ht.default=void 0;const Ka={placeholder:"请选择时间",rangePlaceholder:["开始时间","结束时间"]};ht.default=Ka;var Wn=qt.default;Object.defineProperty(ut,"__esModule",{value:!0});ut.default=void 0;var Qa=Wn(Jt),Za=Wn(ht);const Hn={lang:Object.assign({placeholder:"请选择日期",yearPlaceholder:"请选择年份",quarterPlaceholder:"请选择季度",monthPlaceholder:"请选择月份",weekPlaceholder:"请选择周",rangePlaceholder:["开始日期","结束日期"],rangeYearPlaceholder:["开始年份","结束年份"],rangeMonthPlaceholder:["开始月份","结束月份"],rangeQuarterPlaceholder:["开始季度","结束季度"],rangeWeekPlaceholder:["开始周","结束周"]},Qa.default),timePickerLocale:Object.assign({},Za.default)};Hn.lang.ok="确定";ut.default=Hn;var eo=qt.default;Object.defineProperty(Ht,"__esModule",{value:!0});Ht.default=void 0;var to=eo(ut);Ht.default=to.default;var Vt=qt.default;Object.defineProperty(zt,"__esModule",{value:!0});zt.default=void 0;var so=Vt(Wt),no=Vt(Ht),ro=Vt(ut),ao=Vt(ht);const Re="${label}不是一个有效的${type}",oo={locale:"zh-cn",Pagination:so.default,DatePicker:ro.default,TimePicker:ao.default,Calendar:no.default,global:{placeholder:"请选择",close:"关闭"},Table:{filterTitle:"筛选",filterConfirm:"确定",filterReset:"重置",filterEmptyText:"无筛选项",filterCheckAll:"全选",filterSearchPlaceholder:"在筛选项中搜索",emptyText:"暂无数据",selectAll:"全选当页",selectInvert:"反选当页",selectNone:"清空所有",selectionAll:"全选所有",sortTitle:"排序",expand:"展开行",collapse:"关闭行",triggerDesc:"点击降序",triggerAsc:"点击升序",cancelSort:"取消排序"},Modal:{okText:"确定",cancelText:"取消",justOkText:"知道了"},Tour:{Next:"下一步",Previous:"上一步",Finish:"结束导览"},Popconfirm:{cancelText:"取消",okText:"确定"},Transfer:{titles:["",""],searchPlaceholder:"请输入搜索内容",itemUnit:"项",itemsUnit:"项",remove:"删除",selectCurrent:"全选当页",removeCurrent:"删除当页",selectAll:"全选所有",deselectAll:"取消全选",removeAll:"删除全部",selectInvert:"反选当页"},Upload:{uploading:"文件上传中",removeFile:"删除文件",uploadError:"上传错误",previewFile:"预览文件",downloadFile:"下载文件"},Empty:{description:"暂无数据"},Icon:{icon:"图标"},Text:{edit:"编辑",copy:"复制",copied:"复制成功",expand:"展开",collapse:"收起"},Form:{optional:"（可选）",defaultValidateMessages:{default:"字段验证错误${label}",required:"请输入${label}",enum:"${label}必须是其中一个[${enum}]",whitespace:"${label}不能为空字符",date:{format:"${label}日期格式无效",parse:"${label}不能转换为日期",invalid:"${label}是一个无效日期"},types:{string:Re,method:Re,array:Re,object:Re,number:Re,date:Re,boolean:Re,integer:Re,float:Re,regexp:Re,email:Re,url:Re,hex:Re},string:{len:"${label}须为${len}个字符",min:"${label}最少${min}个字符",max:"${label}最多${max}个字符",range:"${label}须在${min}-${max}字符之间"},number:{len:"${label}必须等于${len}",min:"${label}最小值为${min}",max:"${label}最大值为${max}",range:"${label}须在${min}-${max}之间"},array:{len:"须为${len}个${label}",min:"最少${min}个${label}",max:"最多${max}个${label}",range:"${label}数量须在${min}-${max}之间"},pattern:{mismatch:"${label}与模式不匹配${pattern}"}}},Image:{preview:"预览"},QRCode:{expired:"二维码过期",refresh:"点击刷新",scanned:"已扫描"},ColorPicker:{presetEmpty:"暂无",transparent:"无色",singleColor:"单色",gradientColor:"渐变色"}};zt.default=oo;var io=zt;const lo=wr(io),co=()=>{const e=Ut();u.useEffect(()=>{const t=e.pathname.split("/").filter(o=>o.length>0);let n="";if(t[0]==="game"&&t.length>=3){const o=t[1],i=t[2];if(i.startsWith("L")&&i.includes("-"))n=i;else if(o.startsWith("level-")&&i.startsWith("stage-")){const l=o.replace("level-",""),c=i.replace("stage-","");n=`L${l}-${c}`}else o.startsWith("L")&&!o.includes("-")?n=`${o}-${i}`:o.startsWith("level-")&&i.startsWith("L")&&(n=i)}const r=o=>{document.body.style.backgroundImage=`url('${o}')`,document.documentElement.style.backgroundImage=`url('${o}')`};return n?(async o=>{if(o.startsWith("L1-")){const i=["jpg","jpeg","png"];for(const l of i)try{const c=`/${o}/background.${l}`,h=new Image;await new Promise((d,f)=>{h.onload=d,h.onerror=f,h.src=c}),r(c),console.log("✅ 成功加载L1关卡背景:",c);return}catch{continue}r("/background.jpg"),console.log("⚠️ L1关卡背景不存在，使用统一背景: /background.jpg")}else r("/background.jpg"),console.log("✅ 使用统一背景:","/background.jpg")})(n):(r("/background.jpg"),console.log("✅ 非游戏页面使用统一背景:","/background.jpg")),()=>{}},[e.pathname])},{Title:wt,Text:je}=Te,uo=()=>{const e=Fe(),t={currentLevel:2,completedStages:5,overallProgress:42,consecutiveDays:5};return s.jsx("div",{className:"min-h-screen",style:{background:"transparent"},children:s.jsxs("div",{className:"max-w-6xl mx-auto px-4 pt-8 pb-12",children:[s.jsx(ae.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"mb-16",children:s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto",children:[s.jsx(ae.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(ce,{className:"bg-gradient-to-br from-orange-400 to-pink-500 border-0 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer h-48",onClick:()=>e("/game"),children:s.jsxs("div",{className:"text-center text-white h-full flex flex-col justify-center",children:[s.jsx(De,{className:"text-6xl mb-4"}),s.jsx(wt,{level:2,className:"text-white mb-2",children:"开始绘画"}),s.jsx(je,{className:"text-white text-xl opacity-90",children:"让创意自由流淌"})]})})}),s.jsx(ae.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(ce,{className:"bg-white border-2 border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer h-48",onClick:()=>e("/gallery"),children:s.jsxs("div",{className:"text-center h-full flex flex-col justify-center",children:[s.jsx(Ke,{className:"text-6xl text-purple-500 mb-4"}),s.jsx(wt,{level:2,className:"text-gray-700 mb-2",children:"作品画廊"}),s.jsx(je,{className:"text-gray-600 text-xl",children:"欣赏美好作品"})]})})})]})}),s.jsx(ae.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},className:"mb-16",children:s.jsx(ce,{className:"bg-white shadow-soft border-0 max-w-4xl mx-auto",children:s.jsxs("div",{className:"text-center p-8",children:[s.jsxs(wt,{level:2,className:"text-gray-700 mb-8",children:[s.jsx(_t,{className:"mr-3 text-yellow-500"}),"您的学习进度"]}),s.jsxs("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-8 mb-8",children:[s.jsxs("div",{className:"flex justify-between items-center mb-6",children:[s.jsx(je,{className:"text-2xl text-gray-700",children:"艺术之旅进度"}),s.jsxs(je,{className:"text-4xl font-bold text-purple-600",children:[t.overallProgress,"%"]})]}),s.jsx(fn,{percent:t.overallProgress,strokeColor:{"0%":"#3b82f6","50%":"#8b5cf6","100%":"#ec4899"},strokeWidth:20,className:"mb-4"}),s.jsx(je,{className:"text-xl text-gray-600",children:"每一步都是成长，每一画都是进步 ✨"})]}),s.jsxs(Ss,{gutter:24,children:[s.jsx(ot,{span:8,children:s.jsxs("div",{className:"text-center p-6 bg-gradient-to-br from-orange-100 to-orange-200 rounded-xl",children:[s.jsx("div",{className:"text-5xl mb-3",children:"🎨"}),s.jsx(je,{className:"text-4xl font-bold text-orange-600 block",children:t.currentLevel}),s.jsx(je,{className:"text-xl text-gray-600",children:"当前级别"})]})}),s.jsx(ot,{span:8,children:s.jsxs("div",{className:"text-center p-6 bg-gradient-to-br from-green-100 to-green-200 rounded-xl",children:[s.jsx("div",{className:"text-5xl mb-3",children:"⭐"}),s.jsx(je,{className:"text-4xl font-bold text-green-600 block",children:t.completedStages}),s.jsx(je,{className:"text-xl text-gray-600",children:"完成阶段"})]})}),s.jsx(ot,{span:8,children:s.jsxs("div",{className:"text-center p-6 bg-gradient-to-br from-pink-100 to-pink-200 rounded-xl",children:[s.jsx("div",{className:"text-5xl mb-3",children:"🔥"}),s.jsx(je,{className:"text-4xl font-bold text-pink-600 block",children:t.consecutiveDays}),s.jsx(je,{className:"text-xl text-gray-600",children:"连续天数"})]})})]})]})})}),s.jsx(ae.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:1},className:"mb-16",children:s.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto",children:[s.jsx(ae.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsxs(ce,{className:"text-center cursor-pointer bg-gradient-to-br from-blue-50 to-blue-100 border-0 shadow-soft hover:shadow-lg transition-all duration-300 h-32",onClick:()=>e("/profile"),children:[s.jsx(pn,{className:"text-4xl text-blue-500 mb-2"}),s.jsx(je,{className:"text-lg font-medium text-gray-700",children:"个人档案"})]})}),s.jsx(ae.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsxs(ce,{className:"text-center cursor-pointer bg-gradient-to-br from-yellow-50 to-yellow-100 border-0 shadow-soft hover:shadow-lg transition-all duration-300 h-32",onClick:()=>e("/leaderboard"),children:[s.jsx(_t,{className:"text-4xl text-yellow-500 mb-2"}),s.jsx(je,{className:"text-lg font-medium text-gray-700",children:"排行榜"})]})}),s.jsx(ae.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsxs(ce,{className:"text-center cursor-pointer bg-gradient-to-br from-purple-50 to-purple-100 border-0 shadow-soft hover:shadow-lg transition-all duration-300 h-32",onClick:()=>e("/achievements"),children:[s.jsx(Ns,{className:"text-4xl text-purple-500 mb-2"}),s.jsx(je,{className:"text-lg font-medium text-gray-700",children:"成就"})]})}),s.jsx(ae.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsxs(ce,{className:"text-center cursor-pointer bg-gradient-to-br from-green-50 to-green-100 border-0 shadow-soft hover:shadow-lg transition-all duration-300 h-32",onClick:()=>e("/settings"),children:[s.jsx(Es,{className:"text-4xl text-green-500 mb-2"}),s.jsx(je,{className:"text-lg font-medium text-gray-700",children:"设置"})]})})]})}),s.jsx(ae.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:1,delay:1.2},className:"text-center mt-16 py-12",children:s.jsx("div",{className:"max-w-3xl mx-auto",children:s.jsxs("div",{className:"bg-gradient-to-r from-orange-100 via-pink-100 to-purple-100 rounded-2xl p-8 shadow-soft",children:[s.jsx("div",{className:"flex justify-center mb-6",children:s.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-orange-400 via-pink-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg",children:s.jsx(Sr,{className:"text-2xl text-white"})})}),s.jsx(wt,{level:2,className:"text-3xl mb-4",children:s.jsx("span",{className:"bg-gradient-to-r from-orange-500 via-pink-500 to-purple-600 bg-clip-text text-transparent",children:"让艺术点亮记忆，让创造温暖心灵"})}),s.jsx(je,{className:"text-xl text-gray-700 mb-6",children:"每一笔都是希望，每一画都是奇迹"}),s.jsx(je,{className:"text-lg text-gray-600",children:"奇迹创造者团队 · 用心陪伴您的艺术之旅 ✨"})]})})})]})})},{Title:Js,Text:Gs}=Te,ho=({onSelectLevel:e,unlockedLevels:t=["level-1"],onResetProgress:n})=>{const r=[{id:"level-1",title:"线条启蒙",description:"从基础的线条开始，培养手部协调能力",icon:s.jsx(Nr,{className:"text-4xl"}),difficulty:1,stages:[{id:"free-lines",title:"自由画线",description:"随意画线，创作属于您的艺术作品",type:"free-draw"},{id:"trace-lines",title:"描线练习",description:"跟随引导线条，提高绘画精准度",type:"trace"},{id:"straight-shapes",title:"直线图形",description:"绘制三角形、正方形、矩形等直线图形",type:"shape-straight"},{id:"curved-shapes",title:"曲线图形",description:"绘制圆形、椭圆等曲线图形",type:"shape-curved"}]},{id:"level-2",title:"立体空间",description:"从二维图形到三维立体，提升空间想象力",icon:s.jsx(Er,{className:"text-4xl"}),difficulty:2,stages:[{id:"3d-shapes",title:"立体图形",description:"绘制锥形、立方体、圆柱体等三维图形",type:"coming-soon",comingSoon:!0},{id:"color-fill",title:"色彩填充",description:"为几何图形填色，学习色系和色谱搭配",type:"coming-soon",comingSoon:!0},{id:"texture-brush",title:"质感画笔",description:"复杂曲线描边，选择不同质感的画笔填色",type:"coming-soon",comingSoon:!0}]},{id:"level-3",title:"画面构图",description:"通过引导线条，完成完整的艺术画面",icon:s.jsx(Ke,{className:"text-4xl"}),difficulty:3,stages:[{id:"abstract-art",title:"抽象艺术",description:"用抽象线条和色块创作现代艺术作品",type:"coming-soon",comingSoon:!0},{id:"geometric-still",title:"几何静物",description:"绘制几何形状组成的静物画",type:"coming-soon",comingSoon:!0},{id:"life-objects",title:"生活物品",description:"描绘日常生活中的物品和场景",type:"coming-soon",comingSoon:!0}]},{id:"level-4",title:"智能创作",description:"上传照片，AI辅助创作个性化艺术作品",icon:s.jsx(kr,{className:"text-4xl"}),difficulty:4,stages:[{id:"photo-trace",title:"照片描边",description:"上传照片，提取轮廓进行描边练习",type:"coming-soon",comingSoon:!0},{id:"style-render",title:"风格渲染",description:"选择不同艺术风格，AI辅助渲染作品",type:"coming-soon",comingSoon:!0},{id:"ai-creation",title:"AI协作",description:"与AI协作，创作独特的个人艺术作品",type:"coming-soon",comingSoon:!0}]}],a=i=>{switch(i){case 1:return"text-green-600 bg-green-100";case 2:return"text-blue-600 bg-blue-100";case 3:return"text-purple-600 bg-purple-100";case 4:return"text-orange-600 bg-orange-100";default:return"text-gray-600 bg-gray-100"}},o=i=>{switch(i){case 1:return"入门";case 2:return"初级";case 3:return"中级";case 4:return"高级";default:return"未知"}};return s.jsxs("div",{className:"max-w-6xl mx-auto px-4 py-8",children:[s.jsxs(ae.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-center mb-8",children:[s.jsx(Js,{level:1,className:"text-purple-600 mb-4",children:"选择游戏级别"}),s.jsx(Gs,{className:"text-xl text-gray-600",children:"从简单的线条开始，逐步提升您的绘画技能"})]}),s.jsx(Ss,{gutter:[24,24],children:r.map((i,l)=>{const c=t.includes(i.id);return s.jsx(ot,{xs:24,lg:12,children:s.jsx(ae.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:l*.1},children:s.jsxs(ce,{className:`h-full shadow-lg transition-all duration-300 ${c?"hover:shadow-xl cursor-pointer":"opacity-60 cursor-not-allowed"}`,bodyStyle:{padding:"24px"},children:[s.jsxs("div",{className:"text-center mb-6",children:[s.jsxs("div",{className:`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-white relative ${c?"bg-gradient-to-br from-purple-400 to-purple-600":"bg-gradient-to-br from-gray-400 to-gray-500"}`,children:[!c&&s.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:s.jsx(It,{className:"text-2xl"})}),c&&i.icon]}),s.jsx(Js,{level:3,className:`mb-2 ${c?"":"text-gray-500"}`,children:i.title}),s.jsxs("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium mb-3 ${c?a(i.difficulty):"bg-gray-100 text-gray-500"}`,children:[s.jsx(Ns,{className:"mr-1"}),o(i.difficulty)]}),s.jsx(Gs,{className:`block ${c?"text-gray-600":"text-gray-500"}`,children:c?i.description:"完成前面的级别来解锁"})]}),s.jsx("div",{className:"mt-6",children:s.jsx(ae.div,{whileHover:c?{scale:1.02}:{},whileTap:c?{scale:.98}:{},children:s.jsx(J,{type:c?"primary":"default",size:"large",icon:c?s.jsx(De,{}):s.jsx(It,{}),onClick:()=>c&&e(i.id),disabled:!c,className:"w-full h-12 text-lg",children:c?"进入级别":"级别锁定"})})})]})})},i.id)})}),n&&s.jsx("div",{className:"text-center mt-8",children:s.jsx(gn,{title:"重置游戏进度",description:"确定要重置所有游戏进度吗？这将清除所有解锁的级别。",onConfirm:n,okText:"确定",cancelText:"取消",children:s.jsx(J,{icon:s.jsx(Qe,{}),size:"small",className:"text-gray-500 hover:text-gray-700",children:"重置进度"})})})]})},{Title:os,Text:Vs}=Te,mo=({level:e,onSelectStage:t,onBack:n,unlockedStages:r=[]})=>{const a=l=>{switch(l){case 1:return"text-green-600 bg-green-100";case 2:return"text-blue-600 bg-blue-100";case 3:return"text-purple-600 bg-purple-100";case 4:return"text-orange-600 bg-orange-100";default:return"text-gray-600 bg-gray-100"}},o=l=>{switch(l){case 1:return"入门";case 2:return"初级";case 3:return"中级";case 4:return"高级";default:return"未知"}},i=(l,c)=>c===0?!0:r.includes(l);return s.jsx("div",{className:"max-w-6xl mx-auto px-4 py-8",children:s.jsxs(ae.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[s.jsx(ce,{className:"mb-6 shadow-sm",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg flex items-center justify-center text-white text-2xl",children:(typeof e.icon=="string",e.icon)}),s.jsxs("div",{children:[s.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[s.jsx(os,{level:2,className:"mb-0 text-purple-600",children:e.title}),s.jsxs("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${a(e.difficulty)}`,children:[s.jsx(Ns,{className:"mr-1"}),o(e.difficulty)]})]}),s.jsx(Vs,{className:"text-lg text-gray-600",children:e.description})]})]}),s.jsx(J,{icon:s.jsx(qe,{}),onClick:n,size:"large",className:"h-12 px-6",children:"返回级别选择"})]})}),s.jsx(Ss,{gutter:[24,24],children:e.stages.map((l,c)=>{const h=i(l.id,c),d=!l.comingSoon&&h;return s.jsx(ot,{xs:24,sm:12,lg:8,children:s.jsx(ae.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:c*.1},whileHover:d?{scale:1.02}:{},whileTap:d?{scale:.98}:{},children:s.jsx(ce,{className:`h-full shadow-lg transition-all duration-300 ${d?"hover:shadow-xl cursor-pointer":"opacity-60 cursor-not-allowed"}`,bodyStyle:{padding:"24px"},onClick:()=>d&&t(l.id),children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-white ${d?"bg-gradient-to-br from-blue-400 to-blue-600":"bg-gradient-to-br from-gray-400 to-gray-500"}`,children:d?s.jsx(De,{className:"text-2xl"}):s.jsx(It,{className:"text-2xl"})}),s.jsxs(os,{level:4,className:`mb-3 ${d?"":"text-gray-500"}`,children:[l.title,l.comingSoon&&s.jsx("div",{className:"mt-2",children:s.jsx("span",{className:"text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded-full",children:"即将推出"})})]}),s.jsx(Vs,{className:`block mb-6 ${d?"text-gray-600":"text-gray-500"}`,children:l.description}),s.jsx(J,{type:d?"primary":"default",size:"large",icon:d?s.jsx(De,{}):s.jsx(It,{}),disabled:!d,className:"w-full h-12",children:d?"开始练习":l.comingSoon?"即将推出":"完成前面练习解锁"})]})})})},l.id)})}),s.jsx(ae.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"mt-8",children:s.jsx(ce,{className:"bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200",children:s.jsxs("div",{className:"text-center",children:[s.jsx(os,{level:4,className:"text-blue-600 mb-3",children:"💡 学习建议"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-700",children:[s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-blue-400 rounded-full"}),s.jsx("span",{children:"按顺序完成练习效果更佳"})]}),s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-purple-400 rounded-full"}),s.jsx("span",{children:"每次练习时间不宜过长"})]}),s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-pink-400 rounded-full"}),s.jsx("span",{children:"重复练习有助于提高技能"})]})]})]})})})]})})},Jn=({width:e=800,height:t=600,onSave:n,disabled:r=!1,backgroundImage:a,showReference:o=!1,hideCanvas:i=!1,onClearRef:l,isCompleted:c=!1,onPathsChange:h})=>{const d=u.useRef(null),[f,j]=u.useState(!1),[I,b]=u.useState(5),[v,g]=u.useState("#000000"),[T,_]=u.useState([]),[P,W]=u.useState([]),[Z,le]=u.useState(!1),[ee,pe]=u.useState(!1),w=c,F=u.useCallback(S=>{const N=d.current;if(!N)return{x:0,y:0};const $=N.getBoundingClientRect(),G=N.width/$.width,X=N.height/$.height;let H,ne;if("touches"in S){const fe=S.touches[0]||S.changedTouches[0];H=fe.clientX,ne=fe.clientY}else H=S.clientX,ne=S.clientY;const q=(H-$.left)*G,re=(ne-$.top)*X;return{x:q,y:re}},[]),E=u.useCallback(S=>{if(r||w)return;S.preventDefault(),j(!0);const N=F(S);W([N])},[r,w,F]),C=u.useCallback(S=>{if(!f||r||w)return;S.preventDefault();const N=F(S);W($=>[...$,N])},[f,r,w,F]),Y=u.useCallback(()=>{if(!(!f||r||w)){if(j(!1),P.length>1){const S={points:P,color:v,size:I};_(N=>{const $=[...N,S];return le(!0),pe(!1),h==null||h($),$})}W([])}},[f,r,w,P,v,I]),V=u.useCallback((S,N,$,G)=>{if(!(N.length<2)){S.strokeStyle=$,S.lineWidth=G,S.lineCap="round",S.lineJoin="round",S.beginPath(),S.moveTo(N[0].x,N[0].y);for(let X=1;X<N.length;X++)S.lineTo(N[X].x,N[X].y);S.stroke()}},[]),te=u.useCallback(()=>{const S=d.current;if(!S)return;const N=S.getContext("2d",{alpha:!0});if(N)if(S.style.backgroundColor="rgba(0,0,0,0)",S.style.background="none",N.clearRect(0,0,S.width,S.height),N.globalCompositeOperation="source-over",a&&o){const $=new Image;$.onload=()=>{N.globalAlpha=.3,N.drawImage($,0,0,S.width,S.height),N.globalAlpha=1,T.forEach(G=>{V(N,G.points,G.color,G.size)}),P.length>1&&V(N,P,v,I)},$.src=a}else T.forEach($=>{V(N,$.points,$.color,$.size)}),P.length>1&&V(N,P,v,I)},[T,P,v,I,a,o,V]),de=u.useCallback(()=>{const S=d.current;if(S){const N=S.getContext("2d",{alpha:!0});N&&N.clearRect(0,0,S.width,S.height)}_([]),W([]),le(!1),pe(!1),h==null||h([])},[h]),be=u.useCallback(()=>{de()},[de]),ie=u.useCallback(()=>{const S=d.current;if(!S)return;const N=S.toDataURL("image/png");n==null||n(N),pe(!0),K.success("画作已保存！")},[n]);return u.useEffect(()=>{const S=d.current;if(S){const N=S.getContext("2d",{alpha:!0});N&&(N.globalCompositeOperation="source-over",S.style.backgroundColor="rgba(0,0,0,0)",S.style.background="none",N.clearRect(0,0,S.width,S.height),S.setAttribute("style","background-color: rgba(0,0,0,0) !important; background: none !important; touch-action: none; border-radius: 8px; display: block;"),S.style.setProperty("background-color","rgba(0,0,0,0)","important"),S.style.setProperty("background","none","important"),console.log("Canvas initialized with alpha channel"))}},[]),u.useEffect(()=>{te()},[te]),u.useEffect(()=>{const S=d.current;if(S){S.width=e,S.height=t,S.style.width=`${e}px`,S.style.height=`${t}px`;const N=X=>{if(r||w)return;X.preventDefault(),X.stopPropagation();const H=X.touches[0],ne=S.getBoundingClientRect(),q=S.width/ne.width,re=S.height/ne.height,fe={x:(H.clientX-ne.left)*q,y:(H.clientY-ne.top)*re};j(!0),W([fe])},$=X=>{if(!f||r||w)return;X.preventDefault(),X.stopPropagation();const H=X.touches[0],ne=S.getBoundingClientRect(),q=S.width/ne.width,re=S.height/ne.height,fe={x:(H.clientX-ne.left)*q,y:(H.clientY-ne.top)*re};W(Ne=>[...Ne,fe])},G=X=>{if(X.preventDefault(),X.stopPropagation(),!(!f||r||w)){if(j(!1),P.length>1){const H={points:P,color:v,size:I};_(ne=>{const q=[...ne,H];return le(!0),pe(!1),h==null||h(q),q})}W([])}};return S.addEventListener("touchstart",N,{passive:!1}),S.addEventListener("touchmove",$,{passive:!1}),S.addEventListener("touchend",G,{passive:!1}),te(),()=>{S.removeEventListener("touchstart",N),S.removeEventListener("touchmove",$),S.removeEventListener("touchend",G)}}},[e,t,te,r,w,f,P,v,I,h]),u.useEffect(()=>{l&&l(de)},[l,de]),u.useEffect(()=>{if(!i&&T.length===0){const S=d.current;if(S){const N=S.getContext("2d",{alpha:!0});N&&N.clearRect(0,0,S.width,S.height)}}},[i,T]),s.jsxs("div",{className:"flex flex-col items-center gap-4",style:{backgroundColor:"transparent"},children:[s.jsxs("div",{className:"flex flex-wrap items-center gap-4 p-4 bg-white rounded-lg shadow-sm border",children:[s.jsxs(me,{children:[s.jsx("span",{className:"text-sm font-medium",children:"画笔大小:"}),s.jsx(xn,{min:1,max:20,value:I,onChange:b,style:{width:100},disabled:w}),s.jsxs("span",{className:"text-sm text-gray-500",children:[I,"px"]})]}),s.jsxs(me,{children:[s.jsx("span",{className:"text-sm font-medium",children:"颜色:"}),s.jsx(Cr,{value:v,onChange:S=>g(S.toHexString()),showText:!0,disabled:w})]}),s.jsxs(me,{children:[s.jsx(J,{icon:s.jsx(yn,{}),onClick:be,disabled:!Z||ee||w,title:"清空",children:"清空"}),s.jsx(J,{icon:s.jsx(bn,{}),onClick:ie,type:"primary",disabled:!Z||ee||w,title:"保存",children:"保存"})]})]}),!i&&s.jsx("div",{style:{backgroundColor:"rgba(0,0,0,0)",background:"none",position:"relative",borderRadius:"8px",border:"2px solid #e5e7eb",overflow:"hidden"},children:s.jsx("canvas",{ref:d,width:e,height:t,className:w?"cursor-not-allowed opacity-60":"cursor-crosshair",onMouseDown:E,onMouseMove:C,onMouseUp:Y,onMouseLeave:Y,style:{touchAction:"none",backgroundColor:"rgba(0,0,0,0)",background:"none",borderRadius:"8px",display:"block",opacity:"1",position:"relative",margin:0,padding:0,border:"none",outline:"none"}})})]})};function Gn(e,t){return function(){return e.apply(t,arguments)}}const{toString:fo}=Object.prototype,{getPrototypeOf:Ps}=Object,{iterator:Yt,toStringTag:Vn}=Symbol,Xt=(e=>t=>{const n=fo.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),$e=e=>(e=e.toLowerCase(),t=>Xt(t)===e),Kt=e=>t=>typeof t===e,{isArray:et}=Array,dt=Kt("undefined");function mt(e){return e!==null&&!dt(e)&&e.constructor!==null&&!dt(e.constructor)&&Ce(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Yn=$e("ArrayBuffer");function po(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Yn(e.buffer),t}const go=Kt("string"),Ce=Kt("function"),Xn=Kt("number"),ft=e=>e!==null&&typeof e=="object",xo=e=>e===!0||e===!1,Pt=e=>{if(Xt(e)!=="object")return!1;const t=Ps(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Vn in e)&&!(Yt in e)},yo=e=>{if(!ft(e)||mt(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},bo=$e("Date"),vo=$e("File"),jo=$e("Blob"),wo=$e("FileList"),So=e=>ft(e)&&Ce(e.pipe),No=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Ce(e.append)&&((t=Xt(e))==="formdata"||t==="object"&&Ce(e.toString)&&e.toString()==="[object FormData]"))},Eo=$e("URLSearchParams"),[ko,Co,Po,To]=["ReadableStream","Request","Response","Headers"].map($e),Ro=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function pt(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,a;if(typeof e!="object"&&(e=[e]),et(e))for(r=0,a=e.length;r<a;r++)t.call(null,e[r],r,e);else{if(mt(e))return;const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let l;for(r=0;r<i;r++)l=o[r],t.call(null,e[l],l,e)}}function Kn(e,t){if(mt(e))return null;t=t.toLowerCase();const n=Object.keys(e);let r=n.length,a;for(;r-- >0;)if(a=n[r],t===a.toLowerCase())return a;return null}const We=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Qn=e=>!dt(e)&&e!==We;function xs(){const{caseless:e}=Qn(this)&&this||{},t={},n=(r,a)=>{const o=e&&Kn(t,a)||a;Pt(t[o])&&Pt(r)?t[o]=xs(t[o],r):Pt(r)?t[o]=xs({},r):et(r)?t[o]=r.slice():t[o]=r};for(let r=0,a=arguments.length;r<a;r++)arguments[r]&&pt(arguments[r],n);return t}const _o=(e,t,n,{allOwnKeys:r}={})=>(pt(t,(a,o)=>{n&&Ce(a)?e[o]=Gn(a,n):e[o]=a},{allOwnKeys:r}),e),Io=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Ao=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Oo=(e,t,n,r)=>{let a,o,i;const l={};if(t=t||{},e==null)return t;do{for(a=Object.getOwnPropertyNames(e),o=a.length;o-- >0;)i=a[o],(!r||r(i,e,t))&&!l[i]&&(t[i]=e[i],l[i]=!0);e=n!==!1&&Ps(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},$o=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},Lo=e=>{if(!e)return null;if(et(e))return e;let t=e.length;if(!Xn(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Do=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Ps(Uint8Array)),Fo=(e,t)=>{const r=(e&&e[Yt]).call(e);let a;for(;(a=r.next())&&!a.done;){const o=a.value;t.call(e,o[0],o[1])}},Mo=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},Bo=$e("HTMLFormElement"),Uo=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,a){return r.toUpperCase()+a}),Ys=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),zo=$e("RegExp"),Zn=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};pt(n,(a,o)=>{let i;(i=t(a,o,e))!==!1&&(r[o]=i||a)}),Object.defineProperties(e,r)},qo=e=>{Zn(e,(t,n)=>{if(Ce(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(Ce(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Wo=(e,t)=>{const n={},r=a=>{a.forEach(o=>{n[o]=!0})};return et(e)?r(e):r(String(e).split(t)),n},Ho=()=>{},Jo=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Go(e){return!!(e&&Ce(e.append)&&e[Vn]==="FormData"&&e[Yt])}const Vo=e=>{const t=new Array(10),n=(r,a)=>{if(ft(r)){if(t.indexOf(r)>=0)return;if(mt(r))return r;if(!("toJSON"in r)){t[a]=r;const o=et(r)?[]:{};return pt(r,(i,l)=>{const c=n(i,a+1);!dt(c)&&(o[l]=c)}),t[a]=void 0,o}}return r};return n(e,0)},Yo=$e("AsyncFunction"),Xo=e=>e&&(ft(e)||Ce(e))&&Ce(e.then)&&Ce(e.catch),er=((e,t)=>e?setImmediate:t?((n,r)=>(We.addEventListener("message",({source:a,data:o})=>{a===We&&o===n&&r.length&&r.shift()()},!1),a=>{r.push(a),We.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",Ce(We.postMessage)),Ko=typeof queueMicrotask<"u"?queueMicrotask.bind(We):typeof process<"u"&&process.nextTick||er,Qo=e=>e!=null&&Ce(e[Yt]),p={isArray:et,isArrayBuffer:Yn,isBuffer:mt,isFormData:No,isArrayBufferView:po,isString:go,isNumber:Xn,isBoolean:xo,isObject:ft,isPlainObject:Pt,isEmptyObject:yo,isReadableStream:ko,isRequest:Co,isResponse:Po,isHeaders:To,isUndefined:dt,isDate:bo,isFile:vo,isBlob:jo,isRegExp:zo,isFunction:Ce,isStream:So,isURLSearchParams:Eo,isTypedArray:Do,isFileList:wo,forEach:pt,merge:xs,extend:_o,trim:Ro,stripBOM:Io,inherits:Ao,toFlatObject:Oo,kindOf:Xt,kindOfTest:$e,endsWith:$o,toArray:Lo,forEachEntry:Fo,matchAll:Mo,isHTMLForm:Bo,hasOwnProperty:Ys,hasOwnProp:Ys,reduceDescriptors:Zn,freezeMethods:qo,toObjectSet:Wo,toCamelCase:Uo,noop:Ho,toFiniteNumber:Jo,findKey:Kn,global:We,isContextDefined:Qn,isSpecCompliantForm:Go,toJSONObject:Vo,isAsyncFn:Yo,isThenable:Xo,setImmediate:er,asap:Ko,isIterable:Qo};function Q(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a,this.status=a.status?a.status:null)}p.inherits(Q,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:p.toJSONObject(this.config),code:this.code,status:this.status}}});const tr=Q.prototype,sr={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{sr[e]={value:e}});Object.defineProperties(Q,sr);Object.defineProperty(tr,"isAxiosError",{value:!0});Q.from=(e,t,n,r,a,o)=>{const i=Object.create(tr);return p.toFlatObject(e,i,function(c){return c!==Error.prototype},l=>l!=="isAxiosError"),Q.call(i,e.message,t,n,r,a),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const Zo=null;function ys(e){return p.isPlainObject(e)||p.isArray(e)}function nr(e){return p.endsWith(e,"[]")?e.slice(0,-2):e}function Xs(e,t,n){return e?e.concat(t).map(function(a,o){return a=nr(a),!n&&o?"["+a+"]":a}).join(n?".":""):t}function ei(e){return p.isArray(e)&&!e.some(ys)}const ti=p.toFlatObject(p,{},null,function(t){return/^is[A-Z]/.test(t)});function Qt(e,t,n){if(!p.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=p.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(v,g){return!p.isUndefined(g[v])});const r=n.metaTokens,a=n.visitor||d,o=n.dots,i=n.indexes,c=(n.Blob||typeof Blob<"u"&&Blob)&&p.isSpecCompliantForm(t);if(!p.isFunction(a))throw new TypeError("visitor must be a function");function h(b){if(b===null)return"";if(p.isDate(b))return b.toISOString();if(p.isBoolean(b))return b.toString();if(!c&&p.isBlob(b))throw new Q("Blob is not supported. Use a Buffer instead.");return p.isArrayBuffer(b)||p.isTypedArray(b)?c&&typeof Blob=="function"?new Blob([b]):Buffer.from(b):b}function d(b,v,g){let T=b;if(b&&!g&&typeof b=="object"){if(p.endsWith(v,"{}"))v=r?v:v.slice(0,-2),b=JSON.stringify(b);else if(p.isArray(b)&&ei(b)||(p.isFileList(b)||p.endsWith(v,"[]"))&&(T=p.toArray(b)))return v=nr(v),T.forEach(function(P,W){!(p.isUndefined(P)||P===null)&&t.append(i===!0?Xs([v],W,o):i===null?v:v+"[]",h(P))}),!1}return ys(b)?!0:(t.append(Xs(g,v,o),h(b)),!1)}const f=[],j=Object.assign(ti,{defaultVisitor:d,convertValue:h,isVisitable:ys});function I(b,v){if(!p.isUndefined(b)){if(f.indexOf(b)!==-1)throw Error("Circular reference detected in "+v.join("."));f.push(b),p.forEach(b,function(T,_){(!(p.isUndefined(T)||T===null)&&a.call(t,T,p.isString(_)?_.trim():_,v,j))===!0&&I(T,v?v.concat(_):[_])}),f.pop()}}if(!p.isObject(e))throw new TypeError("data must be an object");return I(e),t}function Ks(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function Ts(e,t){this._pairs=[],e&&Qt(e,this,t)}const rr=Ts.prototype;rr.append=function(t,n){this._pairs.push([t,n])};rr.toString=function(t){const n=t?function(r){return t.call(this,r,Ks)}:Ks;return this._pairs.map(function(a){return n(a[0])+"="+n(a[1])},"").join("&")};function si(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ar(e,t,n){if(!t)return e;const r=n&&n.encode||si;p.isFunction(n)&&(n={serialize:n});const a=n&&n.serialize;let o;if(a?o=a(t,n):o=p.isURLSearchParams(t)?t.toString():new Ts(t,n).toString(r),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class Qs{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){p.forEach(this.handlers,function(r){r!==null&&t(r)})}}const or={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ni=typeof URLSearchParams<"u"?URLSearchParams:Ts,ri=typeof FormData<"u"?FormData:null,ai=typeof Blob<"u"?Blob:null,oi={isBrowser:!0,classes:{URLSearchParams:ni,FormData:ri,Blob:ai},protocols:["http","https","file","blob","url","data"]},Rs=typeof window<"u"&&typeof document<"u",bs=typeof navigator=="object"&&navigator||void 0,ii=Rs&&(!bs||["ReactNative","NativeScript","NS"].indexOf(bs.product)<0),li=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",ci=Rs&&window.location.href||"http://localhost",di=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Rs,hasStandardBrowserEnv:ii,hasStandardBrowserWebWorkerEnv:li,navigator:bs,origin:ci},Symbol.toStringTag,{value:"Module"})),Se={...di,...oi};function ui(e,t){return Qt(e,new Se.classes.URLSearchParams,{visitor:function(n,r,a,o){return Se.isNode&&p.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)},...t})}function hi(e){return p.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function mi(e){const t={},n=Object.keys(e);let r;const a=n.length;let o;for(r=0;r<a;r++)o=n[r],t[o]=e[o];return t}function ir(e){function t(n,r,a,o){let i=n[o++];if(i==="__proto__")return!0;const l=Number.isFinite(+i),c=o>=n.length;return i=!i&&p.isArray(a)?a.length:i,c?(p.hasOwnProp(a,i)?a[i]=[a[i],r]:a[i]=r,!l):((!a[i]||!p.isObject(a[i]))&&(a[i]=[]),t(n,r,a[i],o)&&p.isArray(a[i])&&(a[i]=mi(a[i])),!l)}if(p.isFormData(e)&&p.isFunction(e.entries)){const n={};return p.forEachEntry(e,(r,a)=>{t(hi(r),a,n,0)}),n}return null}function fi(e,t,n){if(p.isString(e))try{return(t||JSON.parse)(e),p.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const gt={transitional:or,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",a=r.indexOf("application/json")>-1,o=p.isObject(t);if(o&&p.isHTMLForm(t)&&(t=new FormData(t)),p.isFormData(t))return a?JSON.stringify(ir(t)):t;if(p.isArrayBuffer(t)||p.isBuffer(t)||p.isStream(t)||p.isFile(t)||p.isBlob(t)||p.isReadableStream(t))return t;if(p.isArrayBufferView(t))return t.buffer;if(p.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return ui(t,this.formSerializer).toString();if((l=p.isFileList(t))||r.indexOf("multipart/form-data")>-1){const c=this.env&&this.env.FormData;return Qt(l?{"files[]":t}:t,c&&new c,this.formSerializer)}}return o||a?(n.setContentType("application/json",!1),fi(t)):t}],transformResponse:[function(t){const n=this.transitional||gt.transitional,r=n&&n.forcedJSONParsing,a=this.responseType==="json";if(p.isResponse(t)||p.isReadableStream(t))return t;if(t&&p.isString(t)&&(r&&!this.responseType||a)){const i=!(n&&n.silentJSONParsing)&&a;try{return JSON.parse(t)}catch(l){if(i)throw l.name==="SyntaxError"?Q.from(l,Q.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Se.classes.FormData,Blob:Se.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};p.forEach(["delete","get","head","post","put","patch"],e=>{gt.headers[e]={}});const pi=p.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),gi=e=>{const t={};let n,r,a;return e&&e.split(`
`).forEach(function(i){a=i.indexOf(":"),n=i.substring(0,a).trim().toLowerCase(),r=i.substring(a+1).trim(),!(!n||t[n]&&pi[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},Zs=Symbol("internals");function rt(e){return e&&String(e).trim().toLowerCase()}function Tt(e){return e===!1||e==null?e:p.isArray(e)?e.map(Tt):String(e)}function xi(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const yi=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function is(e,t,n,r,a){if(p.isFunction(r))return r.call(this,t,n);if(a&&(t=n),!!p.isString(t)){if(p.isString(r))return t.indexOf(r)!==-1;if(p.isRegExp(r))return r.test(t)}}function bi(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function vi(e,t){const n=p.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(a,o,i){return this[r].call(this,t,a,o,i)},configurable:!0})})}let Pe=class{constructor(t){t&&this.set(t)}set(t,n,r){const a=this;function o(l,c,h){const d=rt(c);if(!d)throw new Error("header name must be a non-empty string");const f=p.findKey(a,d);(!f||a[f]===void 0||h===!0||h===void 0&&a[f]!==!1)&&(a[f||c]=Tt(l))}const i=(l,c)=>p.forEach(l,(h,d)=>o(h,d,c));if(p.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(p.isString(t)&&(t=t.trim())&&!yi(t))i(gi(t),n);else if(p.isObject(t)&&p.isIterable(t)){let l={},c,h;for(const d of t){if(!p.isArray(d))throw TypeError("Object iterator must return a key-value pair");l[h=d[0]]=(c=l[h])?p.isArray(c)?[...c,d[1]]:[c,d[1]]:d[1]}i(l,n)}else t!=null&&o(n,t,r);return this}get(t,n){if(t=rt(t),t){const r=p.findKey(this,t);if(r){const a=this[r];if(!n)return a;if(n===!0)return xi(a);if(p.isFunction(n))return n.call(this,a,r);if(p.isRegExp(n))return n.exec(a);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=rt(t),t){const r=p.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||is(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let a=!1;function o(i){if(i=rt(i),i){const l=p.findKey(r,i);l&&(!n||is(r,r[l],l,n))&&(delete r[l],a=!0)}}return p.isArray(t)?t.forEach(o):o(t),a}clear(t){const n=Object.keys(this);let r=n.length,a=!1;for(;r--;){const o=n[r];(!t||is(this,this[o],o,t,!0))&&(delete this[o],a=!0)}return a}normalize(t){const n=this,r={};return p.forEach(this,(a,o)=>{const i=p.findKey(r,o);if(i){n[i]=Tt(a),delete n[o];return}const l=t?bi(o):String(o).trim();l!==o&&delete n[o],n[l]=Tt(a),r[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return p.forEach(this,(r,a)=>{r!=null&&r!==!1&&(n[a]=t&&p.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(a=>r.set(a)),r}static accessor(t){const r=(this[Zs]=this[Zs]={accessors:{}}).accessors,a=this.prototype;function o(i){const l=rt(i);r[l]||(vi(a,i),r[l]=!0)}return p.isArray(t)?t.forEach(o):o(t),this}};Pe.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);p.reduceDescriptors(Pe.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});p.freezeMethods(Pe);function ls(e,t){const n=this||gt,r=t||n,a=Pe.from(r.headers);let o=r.data;return p.forEach(e,function(l){o=l.call(n,o,a.normalize(),t?t.status:void 0)}),a.normalize(),o}function lr(e){return!!(e&&e.__CANCEL__)}function tt(e,t,n){Q.call(this,e??"canceled",Q.ERR_CANCELED,t,n),this.name="CanceledError"}p.inherits(tt,Q,{__CANCEL__:!0});function cr(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new Q("Request failed with status code "+n.status,[Q.ERR_BAD_REQUEST,Q.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function ji(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function wi(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a=0,o=0,i;return t=t!==void 0?t:1e3,function(c){const h=Date.now(),d=r[o];i||(i=h),n[a]=c,r[a]=h;let f=o,j=0;for(;f!==a;)j+=n[f++],f=f%e;if(a=(a+1)%e,a===o&&(o=(o+1)%e),h-i<t)return;const I=d&&h-d;return I?Math.round(j*1e3/I):void 0}}function Si(e,t){let n=0,r=1e3/t,a,o;const i=(h,d=Date.now())=>{n=d,a=null,o&&(clearTimeout(o),o=null),e(...h)};return[(...h)=>{const d=Date.now(),f=d-n;f>=r?i(h,d):(a=h,o||(o=setTimeout(()=>{o=null,i(a)},r-f)))},()=>a&&i(a)]}const Ot=(e,t,n=3)=>{let r=0;const a=wi(50,250);return Si(o=>{const i=o.loaded,l=o.lengthComputable?o.total:void 0,c=i-r,h=a(c),d=i<=l;r=i;const f={loaded:i,total:l,progress:l?i/l:void 0,bytes:c,rate:h||void 0,estimated:h&&l&&d?(l-i)/h:void 0,event:o,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(f)},n)},en=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},tn=e=>(...t)=>p.asap(()=>e(...t)),Ni=Se.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Se.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Se.origin),Se.navigator&&/(msie|trident)/i.test(Se.navigator.userAgent)):()=>!0,Ei=Se.hasStandardBrowserEnv?{write(e,t,n,r,a,o){const i=[e+"="+encodeURIComponent(t)];p.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),p.isString(r)&&i.push("path="+r),p.isString(a)&&i.push("domain="+a),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function ki(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Ci(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function dr(e,t,n){let r=!ki(t);return e&&(r||n==!1)?Ci(e,t):t}const sn=e=>e instanceof Pe?{...e}:e;function Ge(e,t){t=t||{};const n={};function r(h,d,f,j){return p.isPlainObject(h)&&p.isPlainObject(d)?p.merge.call({caseless:j},h,d):p.isPlainObject(d)?p.merge({},d):p.isArray(d)?d.slice():d}function a(h,d,f,j){if(p.isUndefined(d)){if(!p.isUndefined(h))return r(void 0,h,f,j)}else return r(h,d,f,j)}function o(h,d){if(!p.isUndefined(d))return r(void 0,d)}function i(h,d){if(p.isUndefined(d)){if(!p.isUndefined(h))return r(void 0,h)}else return r(void 0,d)}function l(h,d,f){if(f in t)return r(h,d);if(f in e)return r(void 0,h)}const c={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:l,headers:(h,d,f)=>a(sn(h),sn(d),f,!0)};return p.forEach(Object.keys({...e,...t}),function(d){const f=c[d]||a,j=f(e[d],t[d],d);p.isUndefined(j)&&f!==l||(n[d]=j)}),n}const ur=e=>{const t=Ge({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:a,xsrfCookieName:o,headers:i,auth:l}=t;t.headers=i=Pe.from(i),t.url=ar(dr(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&i.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let c;if(p.isFormData(n)){if(Se.hasStandardBrowserEnv||Se.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((c=i.getContentType())!==!1){const[h,...d]=c?c.split(";").map(f=>f.trim()).filter(Boolean):[];i.setContentType([h||"multipart/form-data",...d].join("; "))}}if(Se.hasStandardBrowserEnv&&(r&&p.isFunction(r)&&(r=r(t)),r||r!==!1&&Ni(t.url))){const h=a&&o&&Ei.read(o);h&&i.set(a,h)}return t},Pi=typeof XMLHttpRequest<"u",Ti=Pi&&function(e){return new Promise(function(n,r){const a=ur(e);let o=a.data;const i=Pe.from(a.headers).normalize();let{responseType:l,onUploadProgress:c,onDownloadProgress:h}=a,d,f,j,I,b;function v(){I&&I(),b&&b(),a.cancelToken&&a.cancelToken.unsubscribe(d),a.signal&&a.signal.removeEventListener("abort",d)}let g=new XMLHttpRequest;g.open(a.method.toUpperCase(),a.url,!0),g.timeout=a.timeout;function T(){if(!g)return;const P=Pe.from("getAllResponseHeaders"in g&&g.getAllResponseHeaders()),Z={data:!l||l==="text"||l==="json"?g.responseText:g.response,status:g.status,statusText:g.statusText,headers:P,config:e,request:g};cr(function(ee){n(ee),v()},function(ee){r(ee),v()},Z),g=null}"onloadend"in g?g.onloadend=T:g.onreadystatechange=function(){!g||g.readyState!==4||g.status===0&&!(g.responseURL&&g.responseURL.indexOf("file:")===0)||setTimeout(T)},g.onabort=function(){g&&(r(new Q("Request aborted",Q.ECONNABORTED,e,g)),g=null)},g.onerror=function(){r(new Q("Network Error",Q.ERR_NETWORK,e,g)),g=null},g.ontimeout=function(){let W=a.timeout?"timeout of "+a.timeout+"ms exceeded":"timeout exceeded";const Z=a.transitional||or;a.timeoutErrorMessage&&(W=a.timeoutErrorMessage),r(new Q(W,Z.clarifyTimeoutError?Q.ETIMEDOUT:Q.ECONNABORTED,e,g)),g=null},o===void 0&&i.setContentType(null),"setRequestHeader"in g&&p.forEach(i.toJSON(),function(W,Z){g.setRequestHeader(Z,W)}),p.isUndefined(a.withCredentials)||(g.withCredentials=!!a.withCredentials),l&&l!=="json"&&(g.responseType=a.responseType),h&&([j,b]=Ot(h,!0),g.addEventListener("progress",j)),c&&g.upload&&([f,I]=Ot(c),g.upload.addEventListener("progress",f),g.upload.addEventListener("loadend",I)),(a.cancelToken||a.signal)&&(d=P=>{g&&(r(!P||P.type?new tt(null,e,g):P),g.abort(),g=null)},a.cancelToken&&a.cancelToken.subscribe(d),a.signal&&(a.signal.aborted?d():a.signal.addEventListener("abort",d)));const _=ji(a.url);if(_&&Se.protocols.indexOf(_)===-1){r(new Q("Unsupported protocol "+_+":",Q.ERR_BAD_REQUEST,e));return}g.send(o||null)})},Ri=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,a;const o=function(h){if(!a){a=!0,l();const d=h instanceof Error?h:this.reason;r.abort(d instanceof Q?d:new tt(d instanceof Error?d.message:d))}};let i=t&&setTimeout(()=>{i=null,o(new Q(`timeout ${t} of ms exceeded`,Q.ETIMEDOUT))},t);const l=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(h=>{h.unsubscribe?h.unsubscribe(o):h.removeEventListener("abort",o)}),e=null)};e.forEach(h=>h.addEventListener("abort",o));const{signal:c}=r;return c.unsubscribe=()=>p.asap(l),c}},_i=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,a;for(;r<n;)a=r+t,yield e.slice(r,a),r=a},Ii=async function*(e,t){for await(const n of Ai(e))yield*_i(n,t)},Ai=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},nn=(e,t,n,r)=>{const a=Ii(e,t);let o=0,i,l=c=>{i||(i=!0,r&&r(c))};return new ReadableStream({async pull(c){try{const{done:h,value:d}=await a.next();if(h){l(),c.close();return}let f=d.byteLength;if(n){let j=o+=f;n(j)}c.enqueue(new Uint8Array(d))}catch(h){throw l(h),h}},cancel(c){return l(c),a.return()}},{highWaterMark:2})},Zt=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",hr=Zt&&typeof ReadableStream=="function",Oi=Zt&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),mr=(e,...t)=>{try{return!!e(...t)}catch{return!1}},$i=hr&&mr(()=>{let e=!1;const t=new Request(Se.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),rn=64*1024,vs=hr&&mr(()=>p.isReadableStream(new Response("").body)),$t={stream:vs&&(e=>e.body)};Zt&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!$t[t]&&($t[t]=p.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new Q(`Response type '${t}' is not supported`,Q.ERR_NOT_SUPPORT,r)})})})(new Response);const Li=async e=>{if(e==null)return 0;if(p.isBlob(e))return e.size;if(p.isSpecCompliantForm(e))return(await new Request(Se.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(p.isArrayBufferView(e)||p.isArrayBuffer(e))return e.byteLength;if(p.isURLSearchParams(e)&&(e=e+""),p.isString(e))return(await Oi(e)).byteLength},Di=async(e,t)=>{const n=p.toFiniteNumber(e.getContentLength());return n??Li(t)},Fi=Zt&&(async e=>{let{url:t,method:n,data:r,signal:a,cancelToken:o,timeout:i,onDownloadProgress:l,onUploadProgress:c,responseType:h,headers:d,withCredentials:f="same-origin",fetchOptions:j}=ur(e);h=h?(h+"").toLowerCase():"text";let I=Ri([a,o&&o.toAbortSignal()],i),b;const v=I&&I.unsubscribe&&(()=>{I.unsubscribe()});let g;try{if(c&&$i&&n!=="get"&&n!=="head"&&(g=await Di(d,r))!==0){let Z=new Request(t,{method:"POST",body:r,duplex:"half"}),le;if(p.isFormData(r)&&(le=Z.headers.get("content-type"))&&d.setContentType(le),Z.body){const[ee,pe]=en(g,Ot(tn(c)));r=nn(Z.body,rn,ee,pe)}}p.isString(f)||(f=f?"include":"omit");const T="credentials"in Request.prototype;b=new Request(t,{...j,signal:I,method:n.toUpperCase(),headers:d.normalize().toJSON(),body:r,duplex:"half",credentials:T?f:void 0});let _=await fetch(b,j);const P=vs&&(h==="stream"||h==="response");if(vs&&(l||P&&v)){const Z={};["status","statusText","headers"].forEach(w=>{Z[w]=_[w]});const le=p.toFiniteNumber(_.headers.get("content-length")),[ee,pe]=l&&en(le,Ot(tn(l),!0))||[];_=new Response(nn(_.body,rn,ee,()=>{pe&&pe(),v&&v()}),Z)}h=h||"text";let W=await $t[p.findKey($t,h)||"text"](_,e);return!P&&v&&v(),await new Promise((Z,le)=>{cr(Z,le,{data:W,headers:Pe.from(_.headers),status:_.status,statusText:_.statusText,config:e,request:b})})}catch(T){throw v&&v(),T&&T.name==="TypeError"&&/Load failed|fetch/i.test(T.message)?Object.assign(new Q("Network Error",Q.ERR_NETWORK,e,b),{cause:T.cause||T}):Q.from(T,T&&T.code,e,b)}}),js={http:Zo,xhr:Ti,fetch:Fi};p.forEach(js,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const an=e=>`- ${e}`,Mi=e=>p.isFunction(e)||e===null||e===!1,fr={getAdapter:e=>{e=p.isArray(e)?e:[e];const{length:t}=e;let n,r;const a={};for(let o=0;o<t;o++){n=e[o];let i;if(r=n,!Mi(n)&&(r=js[(i=String(n)).toLowerCase()],r===void 0))throw new Q(`Unknown adapter '${i}'`);if(r)break;a[i||"#"+o]=r}if(!r){const o=Object.entries(a).map(([l,c])=>`adapter ${l} `+(c===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(an).join(`
`):" "+an(o[0]):"as no adapter specified";throw new Q("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:js};function cs(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new tt(null,e)}function on(e){return cs(e),e.headers=Pe.from(e.headers),e.data=ls.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),fr.getAdapter(e.adapter||gt.adapter)(e).then(function(r){return cs(e),r.data=ls.call(e,e.transformResponse,r),r.headers=Pe.from(r.headers),r},function(r){return lr(r)||(cs(e),r&&r.response&&(r.response.data=ls.call(e,e.transformResponse,r.response),r.response.headers=Pe.from(r.response.headers))),Promise.reject(r)})}const pr="1.11.0",es={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{es[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const ln={};es.transitional=function(t,n,r){function a(o,i){return"[Axios v"+pr+"] Transitional option '"+o+"'"+i+(r?". "+r:"")}return(o,i,l)=>{if(t===!1)throw new Q(a(i," has been removed"+(n?" in "+n:"")),Q.ERR_DEPRECATED);return n&&!ln[i]&&(ln[i]=!0,console.warn(a(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,l):!0}};es.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function Bi(e,t,n){if(typeof e!="object")throw new Q("options must be an object",Q.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const o=r[a],i=t[o];if(i){const l=e[o],c=l===void 0||i(l,o,e);if(c!==!0)throw new Q("option "+o+" must be "+c,Q.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new Q("Unknown option "+o,Q.ERR_BAD_OPTION)}}const Rt={assertOptions:Bi,validators:es},Le=Rt.validators;let Je=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Qs,response:new Qs}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let a={};Error.captureStackTrace?Error.captureStackTrace(a):a=new Error;const o=a.stack?a.stack.replace(/^.+\n/,""):"";try{r.stack?o&&!String(r.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+o):r.stack=o}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Ge(this.defaults,n);const{transitional:r,paramsSerializer:a,headers:o}=n;r!==void 0&&Rt.assertOptions(r,{silentJSONParsing:Le.transitional(Le.boolean),forcedJSONParsing:Le.transitional(Le.boolean),clarifyTimeoutError:Le.transitional(Le.boolean)},!1),a!=null&&(p.isFunction(a)?n.paramsSerializer={serialize:a}:Rt.assertOptions(a,{encode:Le.function,serialize:Le.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Rt.assertOptions(n,{baseUrl:Le.spelling("baseURL"),withXsrfToken:Le.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&p.merge(o.common,o[n.method]);o&&p.forEach(["delete","get","head","post","put","patch","common"],b=>{delete o[b]}),n.headers=Pe.concat(i,o);const l=[];let c=!0;this.interceptors.request.forEach(function(v){typeof v.runWhen=="function"&&v.runWhen(n)===!1||(c=c&&v.synchronous,l.unshift(v.fulfilled,v.rejected))});const h=[];this.interceptors.response.forEach(function(v){h.push(v.fulfilled,v.rejected)});let d,f=0,j;if(!c){const b=[on.bind(this),void 0];for(b.unshift(...l),b.push(...h),j=b.length,d=Promise.resolve(n);f<j;)d=d.then(b[f++],b[f++]);return d}j=l.length;let I=n;for(f=0;f<j;){const b=l[f++],v=l[f++];try{I=b(I)}catch(g){v.call(this,g);break}}try{d=on.call(this,I)}catch(b){return Promise.reject(b)}for(f=0,j=h.length;f<j;)d=d.then(h[f++],h[f++]);return d}getUri(t){t=Ge(this.defaults,t);const n=dr(t.baseURL,t.url,t.allowAbsoluteUrls);return ar(n,t.params,t.paramsSerializer)}};p.forEach(["delete","get","head","options"],function(t){Je.prototype[t]=function(n,r){return this.request(Ge(r||{},{method:t,url:n,data:(r||{}).data}))}});p.forEach(["post","put","patch"],function(t){function n(r){return function(o,i,l){return this.request(Ge(l||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}Je.prototype[t]=n(),Je.prototype[t+"Form"]=n(!0)});let Ui=class gr{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(a=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](a);r._listeners=null}),this.promise.then=a=>{let o;const i=new Promise(l=>{r.subscribe(l),o=l}).then(a);return i.cancel=function(){r.unsubscribe(o)},i},t(function(o,i,l){r.reason||(r.reason=new tt(o,i,l),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new gr(function(a){t=a}),cancel:t}}};function zi(e){return function(n){return e.apply(null,n)}}function qi(e){return p.isObject(e)&&e.isAxiosError===!0}const ws={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ws).forEach(([e,t])=>{ws[t]=e});function xr(e){const t=new Je(e),n=Gn(Je.prototype.request,t);return p.extend(n,Je.prototype,t,{allOwnKeys:!0}),p.extend(n,t,null,{allOwnKeys:!0}),n.create=function(a){return xr(Ge(e,a))},n}const ge=xr(gt);ge.Axios=Je;ge.CanceledError=tt;ge.CancelToken=Ui;ge.isCancel=lr;ge.VERSION=pr;ge.toFormData=Qt;ge.AxiosError=Q;ge.Cancel=ge.CanceledError;ge.all=function(t){return Promise.all(t)};ge.spread=zi;ge.isAxiosError=qi;ge.mergeConfig=Ge;ge.AxiosHeaders=Pe;ge.formToJSON=e=>ir(p.isHTMLForm(e)?new FormData(e):e);ge.getAdapter=fr.getAdapter;ge.HttpStatusCode=ws;ge.default=ge;const{Axios:Tl,AxiosError:Rl,CanceledError:_l,isCancel:Il,CancelToken:Al,VERSION:Ol,all:$l,Cancel:Ll,isAxiosError:Dl,spread:Fl,toFormData:Ml,AxiosHeaders:Bl,HttpStatusCode:Ul,formToJSON:zl,getAdapter:ql,mergeConfig:Wl}=ge,Wi="http://localhost:8000/api/v1",ke=ge.create({baseURL:Wi,timeout:3e4,headers:{"Content-Type":"application/json"}});ke.interceptors.request.use(e=>{var t;return console.log("API Request:",(t=e.method)==null?void 0:t.toUpperCase(),e.url),e},e=>(console.error("API Request Error:",e),Promise.reject(e)));ke.interceptors.response.use(e=>(console.log("API Response:",e.status,e.config.url),e),e=>{var t,n,r,a,o;return console.error("API Response Error:",{status:(t=e.response)==null?void 0:t.status,statusText:(n=e.response)==null?void 0:n.statusText,data:(r=e.response)==null?void 0:r.data,url:(a=e.config)==null?void 0:a.url,method:(o=e.config)==null?void 0:o.method,message:e.message}),Promise.reject(e)});class Hi{async analyzeLines(t){try{console.log("发送线条分析请求:",{canvas_data_length:t.canvas_data.length,paths_count:t.paths.length,paths_sample:t.paths.slice(0,2)});const n=await ke.post("/games/analyze-lines",t);return console.log("线条分析响应:",n.data),n.data}catch(n){throw console.error("线条分析失败:",n),console.error("请求数据:",t),new Error("线条分析失败，请稍后重试")}}async generateAIImage(t){try{console.log("发送AI生图请求");const n=await ke.post("/games/generate-ai-image",t);return console.log("AI生图响应:",n.data),n.data}catch(n){return console.error("AI生图失败:",n),{status:"failed",error:"AI生图失败，请稍后重试"}}}async getGameLevels(){try{return(await ke.get("/games/levels")).data}catch(t){throw console.error("获取游戏级别失败:",t),new Error("获取游戏级别失败")}}async getGameLevel(t){try{return(await ke.get(`/games/levels/${t}`)).data}catch(n){throw console.error("获取级别详情失败:",n),new Error("获取级别详情失败")}}async getFamousArtworks(){try{return(await ke.get("/games/artworks")).data}catch(t){throw console.error("获取名画列表失败:",t),new Error("获取名画列表失败")}}async getArtworkDetails(t){try{return(await ke.get(`/games/artworks/${t}`)).data.data}catch(n){throw console.error("获取名画详情失败:",n),new Error("获取名画详情失败")}}async startGameSession(t,n=1,r){try{return(await ke.post("/games/sessions/start",{level:t,stage:n,user_id:r})).data}catch(a){throw console.error("开始游戏会话失败:",a),new Error("开始游戏会话失败")}}async submitDrawing(t,n){try{return(await ke.post(`/games/sessions/${t}/submit`,n)).data}catch(r){throw console.error("提交作品失败:",r),new Error("提交作品失败")}}}const cn=new Hi,{Title:St,Text:we}=Te,Ji=({onBack:e,onComplete:t})=>{const[n,r]=u.useState(!1),[a,o]=u.useState(""),[i,l]=u.useState(null),[c,h]=u.useState(null),[d,f]=u.useState(!1),[j,I]=u.useState([]),[b,v]=u.useState(0),g=async P=>{var W;o(P),f(!0);try{const Z=await cn.analyzeLines({canvas_data:P,paths:j});if(h(Z),r(!0),f(!1),K.success("风格分析完成！AI正在为您创作艺术作品..."),((W=Z.ai_generated_image)==null?void 0:W.status)==="generating")try{const le=await cn.generateAIImage({canvas_data:P,paths:j});h(ee=>ee&&{...ee,ai_generated_image:le}),le.status==="success"?K.success("AI艺术作品生成完成！"):K.warning("AI生图暂时不可用，但风格分析已完成")}catch(le){console.error("AI生图失败:",le),h(ee=>ee&&{...ee,ai_generated_image:{status:"failed",error:"AI生图服务暂时不可用"}}),K.warning("AI生图暂时不可用，但风格分析已完成")}}catch(Z){console.error("线条分析失败:",Z),K.error("分析失败，但作品已保存"),r(!0),f(!1)}},T=()=>{a?t():K.warning("请先保存您的作品")},_=()=>{r(!1),o(""),h(null),I([]),v(P=>P+1),i&&i()};return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-orange-50 via-pink-50 to-purple-50",children:s.jsx("div",{className:"max-w-6xl mx-auto px-4 py-8",children:s.jsxs(ae.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[!n&&s.jsx(ce,{className:"mb-6 shadow-sm",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx(St,{level:2,className:"mb-2 text-purple-600",children:"自由绘画"}),s.jsx(we,{className:"text-lg text-gray-600",children:"发挥您的想象力，创作属于您的艺术作品。没有对错，只有表达。让您的创意自由流淌吧！"})]}),s.jsx(J,{icon:s.jsx(qe,{}),onClick:e,size:"large",className:"h-12 px-6",children:"返回"})]})}),n&&s.jsx(ae.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5},className:"mb-6",children:s.jsx(ce,{className:"bg-gradient-to-r from-green-50 to-blue-50 border-green-200",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(it,{className:"text-2xl text-white"})}),s.jsx(St,{level:3,className:"text-green-600 mb-3",children:"🎉 创作完成！"}),s.jsx(we,{className:"text-lg text-gray-700 block mb-6",children:"太棒了！您已经完成了一幅美丽的作品。每一笔都是您创意的体现！"}),d&&s.jsxs("div",{className:"mb-6",children:[s.jsx(Is,{size:"large"}),s.jsx(we,{className:"block mt-2 text-gray-600",children:"正在分析您的线条风格..."})]}),c&&s.jsxs("div",{className:"mb-6 p-4 bg-white rounded-lg border",children:[s.jsxs(St,{level:4,className:"text-blue-600 mb-3",children:[s.jsx(Ke,{className:"mr-2"}),"您的作品风格分析"]}),s.jsx("div",{className:"w-full",children:s.jsxs("div",{className:"grid grid-cols-4 gap-4",children:[s.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded",children:[s.jsx(we,{className:"block text-sm text-gray-600 mb-1",children:"复杂度"}),s.jsxs(we,{className:"font-medium text-lg",children:[(c.line_features.complexity*100).toFixed(1),"%"]})]}),s.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded",children:[s.jsx(we,{className:"block text-sm text-gray-600 mb-1",children:"节奏感"}),s.jsxs(we,{className:"font-medium text-lg",children:[(c.line_features.rhythm*100).toFixed(1),"%"]})]}),s.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded",children:[s.jsx(we,{className:"block text-sm text-gray-600 mb-1",children:"平滑度"}),s.jsxs(we,{className:"font-medium text-lg",children:[(c.line_features.smoothness*100).toFixed(1),"%"]})]}),s.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded",children:[s.jsx(we,{className:"block text-sm text-gray-600 mb-1",children:"曲线特征"}),s.jsx(we,{className:"font-medium text-lg",children:c.line_features.dominant_curves?"是":"否"})]})]})}),c.ai_generated_image&&s.jsxs("div",{className:"mt-6 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200",children:[s.jsx(St,{level:4,className:"text-purple-600 mb-3 text-center",children:"🎨 AI为您创作的艺术作品"}),c.ai_generated_image.status==="success"&&c.ai_generated_image.image_url&&s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsxs("div",{className:"text-center",children:[s.jsx(we,{className:"block text-lg font-medium text-gray-700 mb-3",children:"📝 您的原创作品"}),s.jsx(As,{src:a,alt:"用户原创作品",className:"rounded-lg shadow-lg max-w-full h-auto border-2 border-gray-200",style:{maxHeight:"300px"},preview:{mask:s.jsx("div",{className:"text-white",children:"🔍 查看原图"})}})]}),s.jsxs("div",{className:"text-center",children:[s.jsx(we,{className:"block text-lg font-medium text-purple-600 mb-3",children:"🤖 AI 创作版本"}),s.jsx(As,{src:c.ai_generated_image.image_url,alt:"AI生成的艺术作品",className:"rounded-lg shadow-lg max-w-full h-auto border-2 border-purple-200",style:{maxHeight:"300px"},preview:{mask:s.jsx("div",{className:"text-white",children:"🔍 查看AI作品"})}})]})]}),c.ai_generated_image.status==="failed"&&s.jsxs("div",{className:"text-center text-gray-500",children:[s.jsx(we,{children:"😔 AI创作暂时不可用"}),c.ai_generated_image.error&&s.jsx(we,{className:"text-xs block mt-1",children:c.ai_generated_image.error})]}),c.ai_generated_image.status==="generating"&&s.jsx("div",{className:"text-center py-8",children:s.jsxs("div",{className:"relative",children:[s.jsx(Is,{size:"large"}),s.jsxs("div",{className:"mt-4",children:[s.jsx(we,{className:"block text-lg font-medium text-purple-600",children:"🎨 AI正在为您创作艺术作品"}),s.jsx(we,{className:"block mt-2 text-gray-600",children:"请稍候，这可能需要10-30秒..."}),s.jsxs("div",{className:"mt-3 flex justify-center items-center space-x-2",children:[s.jsx("div",{className:"w-2 h-2 bg-purple-400 rounded-full animate-bounce"}),s.jsx("div",{className:"w-2 h-2 bg-pink-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),s.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]})]})})]})]}),s.jsxs(me,{size:"large",children:[s.jsx(J,{type:"primary",size:"large",icon:s.jsx(it,{}),onClick:T,className:"h-12 px-8 bg-green-500 hover:bg-green-600",children:"完成练习"}),s.jsx(J,{size:"large",icon:s.jsx(Qe,{}),onClick:_,className:"h-12 px-8",children:"重新创作"})]})]})})}),!n&&s.jsx("div",{className:"transparent-canvas-card",children:s.jsx(Jn,{width:768,height:1024,onSave:g,hideCanvas:!1,onClearRef:l,isCompleted:!1,onPathsChange:I},b)})]})})})},{Title:Nt,Text:Et}=Te,Gi=[{id:"triangle",name:"三角形",description:"绘制一个三角形",instruction:"画三条直线，让它们相互连接形成三角形",difficulty:1,type:"straight"},{id:"square",name:"正方形",description:"绘制一个正方形",instruction:"画四条相等的直线，形成四个直角",difficulty:1,type:"straight"},{id:"rectangle",name:"长方形",description:"绘制一个长方形",instruction:"画四条直线，对边相等且平行",difficulty:1,type:"straight"},{id:"pentagon",name:"五边形",description:"绘制一个五边形",instruction:"画五条直线，形成一个封闭的五边形",difficulty:2,type:"straight"},{id:"hexagon",name:"六边形",description:"绘制一个六边形",instruction:"画六条直线，形成一个封闭的六边形",difficulty:2,type:"straight"},{id:"circle",name:"圆形",description:"绘制一个圆形",instruction:"画一条连续的曲线，让起点和终点相接",difficulty:1,type:"curved"},{id:"oval",name:"椭圆形",description:"绘制一个椭圆形",instruction:"画一个拉长的圆形，像鸡蛋的形状",difficulty:2,type:"curved"},{id:"heart",name:"心形",description:"绘制一个心形",instruction:"画两个圆弧在顶部，底部汇聚成一个点",difficulty:3,type:"curved"},{id:"star",name:"星形",description:"绘制一个五角星",instruction:"画五个尖角，每个尖角之间用直线或曲线连接",difficulty:3,type:"curved"}],dn=({onBack:e,onComplete:t,shapeType:n})=>{const[r,a]=u.useState(0),[o,i]=u.useState(!1),[l,c]=u.useState(0),[h,d]=u.useState(!1),f=Gi.filter(W=>W.type===n),j=f[r],I=W=>{const le=(4-j.difficulty)*10,ee=Math.floor(Math.random()*20),pe=Math.min(100,60+le+ee);c(pe),i(!0),K.success(`太棒了！您的${j.name}得分：${pe}分`)},b=()=>{t(l)},v=()=>{r<f.length-1?(a(W=>W+1),i(!1),c(0),d(!1)):b()},g=()=>{i(!1),c(0)},T=()=>{d(!h)},_=W=>{switch(W){case 1:return"green";case 2:return"blue";case 3:return"purple";default:return"gray"}},P=W=>{switch(W){case 1:return"简单";case 2:return"中等";case 3:return"困难";default:return"未知"}};return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50",children:s.jsx("div",{className:"max-w-6xl mx-auto px-4 py-8",children:s.jsxs(ae.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[s.jsx(ce,{className:"mb-6 shadow-sm",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsxs(Nt,{level:2,className:"mb-2 text-purple-600",children:[n==="straight"?"直线图形":"曲线图形","绘制"]}),s.jsx(Et,{className:"text-lg text-gray-600",children:n==="straight"?"学习绘制各种直线构成的几何图形":"练习绘制优美的曲线图形"})]}),s.jsx(J,{icon:s.jsx(qe,{}),onClick:e,size:"large",className:"h-12 px-6",children:"返回"})]})}),s.jsx(ce,{className:"mb-6 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[s.jsxs(Nt,{level:4,className:"text-blue-600 mb-0",children:[j.name," (",r+1,"/",f.length,")"]}),s.jsx(Be,{color:_(j.difficulty),children:P(j.difficulty)})]}),s.jsx(Et,{className:"text-gray-700 block mb-2",children:j.description}),h&&s.jsx(ae.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},transition:{duration:.3},children:s.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-2",children:s.jsxs(Et,{className:"text-yellow-800",children:["💡 提示：",j.instruction]})})})]}),s.jsxs(J,{icon:s.jsx(Pr,{}),onClick:T,type:h?"primary":"default",children:[h?"隐藏":"显示","提示"]})]})}),s.jsx(ce,{className:"mb-6 bg-gradient-to-r from-green-50 to-blue-50 border-green-200",children:s.jsxs("div",{className:"text-center",children:[s.jsx(Nt,{level:4,className:"text-green-600 mb-3",children:"🎯 绘画要点"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-700",children:[s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-green-400 rounded-full"}),s.jsx("span",{children:n==="straight"?"保持线条笔直":"保持曲线流畅"})]}),s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-blue-400 rounded-full"}),s.jsx("span",{children:"注意图形的对称性"})]}),s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-purple-400 rounded-full"}),s.jsx("span",{children:"确保线条闭合"})]})]})]})}),s.jsx("div",{className:"transparent-canvas-card",children:s.jsx(Jn,{width:800,height:600,onSave:I})}),o&&s.jsx(ae.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5},className:"mt-6",children:s.jsx(ce,{className:"bg-gradient-to-r from-green-50 to-blue-50 border-green-200",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(it,{className:"text-2xl text-white"})}),s.jsxs(Nt,{level:3,className:"text-green-600 mb-3",children:["🎉 ",j.name,"绘制完成！"]}),s.jsxs("div",{className:"mb-4",children:[s.jsx(Et,{className:"text-lg text-gray-700 block mb-2",children:"绘制评分"}),s.jsx(fn,{percent:l,strokeColor:{"0%":"#108ee9","100%":"#87d068"},className:"max-w-md mx-auto"})]}),s.jsxs(me,{size:"large",children:[r<f.length-1?s.jsx(J,{type:"primary",size:"large",icon:s.jsx(it,{}),onClick:v,className:"h-12 px-8 bg-blue-500 hover:bg-blue-600",children:"下一个图形"}):s.jsx(J,{type:"primary",size:"large",icon:s.jsx(it,{}),onClick:b,className:"h-12 px-8 bg-green-500 hover:bg-green-600",children:"完成练习"}),s.jsx(J,{size:"large",icon:s.jsx(Qe,{}),onClick:g,className:"h-12 px-8",children:"重新绘制"})]})]})})})]})})})},Xe={accuracyThreshold:.7,tolerance:20,animationSpeed:.6,staticTraceDisplayTime:3e3},at={minCanvasSize:350,maxCanvasSize:700,sizeRatios:{small:.45,medium:.5,large:.55}},un=({guidePaths:e,originalCanvasSize:t,levelStage:n="L1-3",onPathComplete:r})=>{const a=n==="L1-4",o=u.useRef(null),[i,l]=u.useState(0),[c,h]=u.useState(!1),[d,f]=u.useState([]),[j,I]=u.useState([]),[b,v]=u.useState({}),[g,T]=u.useState(0),[_,P]=u.useState("entering"),[W,Z]=u.useState(.1),[le,ee]=u.useState(1),[pe,w]=u.useState(0),[F,E]=u.useState(1),[C,Y]=u.useState(""),[V,te]=u.useState(!1),[de,be]=u.useState(null),[ie,S]=u.useState({width:800,height:600}),[N,$]=u.useState(""),[G,X]=u.useState(""),[H,ne]=u.useState(null),[q,re]=u.useState("guide-animation"),[fe,Ne]=u.useState(null);u.useEffect(()=>{const m=new Image;m.onload=()=>{be(m)},m.src=`/${n}/arrow.png`},[n]),u.useEffect(()=>{$(""),X("");const m=k=>new Promise((R,O)=>{const U=new Image;U.onload=()=>R(U),U.onerror=()=>O(new Error(`Failed to load ${k}`)),U.src=k});(async()=>{const k=["png","jpg","jpeg"];let R=null,O="";for(const D of k)try{const A=`/${n}/bg.${D}`;R=await m(A),O=A;break}catch{}if(R&&O){$(O);const A=(()=>{const oe=window.innerHeight;let he;return oe<=600?he=oe*at.sizeRatios.small:oe<=900?he=oe*at.sizeRatios.medium:he=oe*at.sizeRatios.large,he=Math.max(at.minCanvasSize,he),he=Math.min(at.maxCanvasSize,he),he})(),L=R.width/R.height;let M,se;L>=1?(M=A,se=A/L,se>A&&(se=A,M=A*L)):(se=A,M=A*L,M>A&&(M=A,se=A/L)),S({width:Math.round(M),height:Math.round(se)})}else console.error("Failed to load canvas background image in any format"),S({width:400,height:300});const U=["jpg","jpeg","png"];for(const D of U)try{const A=`/${n}/full.${D}`;await m(A),X(A);break}catch{}})()},[n]);const Ye=m=>m,[Oe,st]=u.useState([]);u.useEffect(()=>{if(e.length>0){const m=Ye(e);st(m);let y=0;for(;y<m.length;){const k=m[y];if(!k||k.points.length>=2)break;I(R=>[...R,k.id]),r(k.id,[]),y++}y!==i&&l(y)}},[e]);const ue=Oe[i];u.useEffect(()=>{Oe.length>0&&ue&&(H&&(clearTimeout(H),ne(null)),a&&fe&&(clearTimeout(fe),Ne(null)),f([]),T(0),a&&re("guide-animation"))},[Oe,i,ue]),u.useEffect(()=>{if(_==="entering"){const m=Date.now(),y=3e3,k=()=>{const R=Date.now()-m,O=Math.min(R/y,1),D=(L=>L<.5?4*L*L*L:1-Math.pow(-2*L+2,3)/2)(O),A=.1+(1-.1)*D;Z(A),O<1?requestAnimationFrame(k):P("instruction")};requestAnimationFrame(k)}},[_]),u.useEffect(()=>{if(_==="instruction"){const m="请根据黄色引导线，描绘名画。您需要紧跟画笔，并且紧跟黄线描绘";te(!0),Y("");let y=0;const k=100,R=()=>{y<m.length?(Y(m.slice(0,y+1)),y++,setTimeout(R,k)):setTimeout(()=>{te(!1),P("active")},2e3)};setTimeout(R,500)}},[_]),u.useEffect(()=>{if(_==="completing"){const m=Date.now(),y=2e3,k=()=>{const R=Date.now()-m,O=Math.min(R/y,1);ee(1-O),w(O),O<1?requestAnimationFrame(k):P("showing")};requestAnimationFrame(k)}},[_]),u.useEffect(()=>{if(_==="showing"){if(a)return;{const m=setTimeout(()=>{P("exiting")},3e3);return()=>clearTimeout(m)}}},[_,a]),u.useEffect(()=>{if(_==="exiting"){if(a)return;const m=Date.now(),y=3e3,k=()=>{const R=Date.now()-m,O=Math.min(R/y,1),D=(M=>M<.5?2*M*M:-1+(4-2*M)*M)(O);E(1-D*.85);const A=.9;if(O>A){const M=(O-A)/(1-A);w(1-M)}else w(1);const L=.3;if(O>L)ee(0);else{const M=O/L;ee(1-M)}O<1&&requestAnimationFrame(k)};requestAnimationFrame(k)}},[_,a]),u.useEffect(()=>{const m=o.current;if(!m)return;const y=U=>{var D;(D=U.target)!=null&&D.closest("canvas")&&U.preventDefault()},k=U=>{var he;const D=((he=document.querySelector("[data-guide-stage]"))==null?void 0:he.getAttribute("data-guide-stage"))||q;if(U.preventDefault(),U.stopPropagation(),a&&(D==="guide-animation"||D==="static-trace"))return;H&&(clearTimeout(H),ne(null)),h(!0);const A=U.touches[0],L=m.getBoundingClientRect(),M=m.width/L.width,se=m.height/L.height,oe={x:(A.clientX-L.left)*M,y:(A.clientY-L.top)*se};f([oe])},R=U=>{var xe;U.preventDefault(),U.stopPropagation();const D=((xe=document.querySelector("[data-guide-stage]"))==null?void 0:xe.getAttribute("data-guide-stage"))||q;if(a&&(D==="guide-animation"||D==="static-trace")||!c)return;const A=U.touches[0],L=m.getBoundingClientRect(),M=m.width/L.width,se=m.height/L.height,oe={x:(A.clientX-L.left)*M,y:(A.clientY-L.top)*se},he=[...d,oe];f(he)},O=U=>{U.preventDefault(),U.stopPropagation(),x()};return m.addEventListener("touchstart",k,{passive:!1}),m.addEventListener("touchmove",R,{passive:!1}),m.addEventListener("touchend",O,{passive:!1}),document.addEventListener("touchmove",y,{passive:!1}),document.addEventListener("wheel",y,{passive:!1}),()=>{m.removeEventListener("touchstart",k),m.removeEventListener("touchmove",R),m.removeEventListener("touchend",O),document.removeEventListener("touchmove",y),document.removeEventListener("wheel",y)}},[c,d,H,q,a]),u.useEffect(()=>()=>{H&&clearTimeout(H),a&&fe&&clearTimeout(fe)},[H,fe,a]),u.useEffect(()=>{if(!ue||_!=="active")return;let m,y=!0;if(a)q==="guide-animation"&&(()=>{if(!y||q!=="guide-animation")return;const R=ue.points.length,O=3e3,U=Math.max(0,(R-10)*150),D=(O+U)/Xe.animationSpeed,A=Date.now(),L=()=>{if(!y||q!=="guide-animation")return;const se=Date.now()-A;let oe=Math.min(se/D,1);const xe=(ve=>ve<.5?2*ve*ve:-1+(4-2*ve)*ve)(oe);T(xe),oe<1?m=requestAnimationFrame(L):y&&ue&&!j.includes(ue.id)&&(re("static-trace"),T(1))};m=requestAnimationFrame(L)})();else{const k=()=>{if(!y)return;const R=ue.points.length,O=3e3,U=Math.max(0,(R-10)*150),D=(O+U)/Xe.animationSpeed,A=Date.now(),L=()=>{if(!y)return;const se=Date.now()-A;let oe=Math.min(se/D,1);const xe=(ve=>ve<.5?2*ve*ve:-1+(4-2*ve)*ve)(oe);T(xe),oe<1?m=requestAnimationFrame(L):y&&ue&&!j.includes(ue.id)&&setTimeout(()=>{y&&(T(0),k())},500)};m=requestAnimationFrame(L)};k()}return()=>{y=!1,m&&cancelAnimationFrame(m)}},[i,ue,_,q,a]),u.useEffect(()=>{if(!(!a||!ue||_!=="active")&&q==="static-trace"){const m=setTimeout(()=>{j.includes(ue.id)||(re("waiting-user"),T(0))},Xe.staticTraceDisplayTime);return Ne(m),()=>{clearTimeout(m)}}},[q,ue,a,_,j]),u.useEffect(()=>{const m=o.current;m&&(m.width=ie.width,m.height=ie.height,m.style.width=`${ie.width}px`,m.style.height=`${ie.height}px`)},[ie]),u.useEffect(()=>{const m=o.current;if(!m)return;const y=m.getContext("2d");if(!y)return;y.clearRect(0,0,m.width,m.height),k();function k(){!ue||!y||(j.forEach(R=>{const O=b[R];O&&O.length>0&&bt(y,O)}),ue&&(a?q==="guide-animation"?yt(y,ue):q==="static-trace"&&ts(y,ue):yt(y,ue)),d.length>0&&bt(y,d))}},[ue,d,j,b,g,ie,a?q:null]);const xt=(m,y,k,R,O,U,D=15)=>{const A=R-y,L=O-k;if(!de){const oe=Math.atan2(L,A);m.fillStyle=U,m.strokeStyle=U,m.lineWidth=2,m.beginPath(),m.moveTo(R,O),m.lineTo(R-D*Math.cos(oe-Math.PI/5),O-D*Math.sin(oe-Math.PI/5)),m.lineTo(R-D*Math.cos(oe+Math.PI/5),O-D*Math.sin(oe+Math.PI/5)),m.closePath(),m.fill(),m.stroke();return}const M=Math.atan2(L,A),se=D*2;m.save(),m.translate(R,O),m.rotate(M),m.drawImage(de,-se/2,-se/2,se,se),m.restore()},yt=(m,y)=>{if(y.points.length<2)return;const k=(t==null?void 0:t.width)||800,R=(t==null?void 0:t.height)||600,O=ie.width/k,U=ie.height/R,D=y.points.map(M=>({x:M.x*O,y:M.y*U}));m.strokeStyle="#FFD700",m.lineWidth=8,m.lineCap="round",m.lineJoin="round",m.setLineDash([]);const A=D.length,L=Math.floor(A*g);if(m.beginPath(),L>0){m.moveTo(D[0].x,D[0].y);for(let M=1;M<L;M++)m.lineTo(D[M].x,D[M].y);if(L<A){const M=D[L-1],se=D[L],oe=A*g-L,he=M.x+(se.x-M.x)*oe,xe=M.y+(se.y-M.y)*oe;m.lineTo(he,xe)}}if(m.stroke(),m.setLineDash([]),g>.1&&L>0){let M,se,oe,he;if(L<A){const xe=D[L-1],ve=D[L],_s=A*g-L;M=xe.x+(ve.x-xe.x)*_s,se=xe.y+(ve.y-xe.y)*_s,oe=xe.x,he=xe.y}else M=D[A-1].x,se=D[A-1].y,oe=D[Math.max(0,A-2)].x,he=D[Math.max(0,A-2)].y;xt(m,oe,he,M,se,"#FFD700",20)}},ts=(m,y)=>{if(y.points.length<2)return;const k=(t==null?void 0:t.width)||800,R=(t==null?void 0:t.height)||600,O=ie.width/k,U=ie.height/R,D=y.points.map(A=>({x:A.x*O,y:A.y*U}));m.strokeStyle="#888888",m.lineWidth=4,m.lineCap="round",m.lineJoin="round",m.setLineDash([10,5]),m.beginPath(),m.moveTo(D[0].x,D[0].y);for(let A=1;A<D.length;A++)m.lineTo(D[A].x,D[A].y);m.stroke(),m.setLineDash([])},bt=(m,y)=>{y.length<2||(m.strokeStyle="#000000",m.lineWidth=4,m.lineCap="round",m.lineJoin="round",ss(m,y))},ss=(m,y)=>{if(!(y.length<2)){if(m.beginPath(),m.moveTo(y[0].x,y[0].y),y.length===2)m.lineTo(y[1].x,y[1].y);else{for(let R=1;R<y.length-1;R++){const O=y[R],U=y[R+1],D=(O.x+U.x)/2,A=(O.y+U.y)/2;m.quadraticCurveTo(O.x,O.y,D,A)}const k=y[y.length-1];m.lineTo(k.x,k.y)}m.stroke()}},nt=m=>{const y=o.current;if(!y)return{x:0,y:0};const k=y.getBoundingClientRect(),R=y.width/k.width,O=y.height/k.height,U=(m.clientX-k.left)*R,D=(m.clientY-k.top)*O;return{x:U,y:D}},ns=m=>{if(m.preventDefault(),m.stopPropagation(),a&&(q==="guide-animation"||q==="static-trace"))return;H&&(clearTimeout(H),ne(null)),h(!0);const y=nt(m);f([y])},rs=m=>{if(m.preventDefault(),m.stopPropagation(),a&&(q==="guide-animation"||q==="static-trace")||!c)return;const y=nt(m),k=[...d,y];f(k)},x=m=>{if(m&&(m.preventDefault(),m.stopPropagation()),!c||!ue)return;if(h(!1),B(d,ue.points)>Xe.accuracyThreshold)if(H&&(clearTimeout(H),ne(null)),r(ue.id,d),I(k=>[...k,ue.id]),v(k=>({...k,[ue.id]:d})),i<Oe.length-1){let k=i+1;for(;k<Oe.length;){const R=Oe[k];if(!R||R.points.length>=2)break;I(O=>[...O,R.id]),r(R.id,[]),k++}k<Oe.length?(l(k),f([]),T(0)):setTimeout(()=>{P("completing")},500)}else setTimeout(()=>{P("completing")},500);else f([]),a&&(re("guide-animation"),T(0),fe&&(clearTimeout(fe),Ne(null)))},z=m=>{if(m.length<2)return m;const y=(t==null?void 0:t.width)||800,k=(t==null?void 0:t.height)||600,R=ie.width/y,O=ie.height/k,U=m.map(L=>({x:L.x*R,y:L.y*O})),D=[],A=5;for(let L=0;L<U.length-1;L++){const M=U[L],se=U[L+1],oe=Math.sqrt(Math.pow(se.x-M.x,2)+Math.pow(se.y-M.y,2)),he=Math.ceil(oe/A);for(let xe=0;xe<=he;xe++){const ve=xe/he;D.push({x:M.x+(se.x-M.x)*ve,y:M.y+(se.y-M.y)*ve})}}return D},B=(m,y)=>{if(m.length===0||y.length===0||m.length<2)return 0;const k=z(y);let R=0,O=0;for(const M of m){let se=1/0;for(const oe of k){const he=Math.sqrt(Math.pow(M.x-oe.x,2)+Math.pow(M.y-oe.y,2));se=Math.min(se,he)}se<=Xe.tolerance&&(R+=se,O++)}if(O===0)return 0;const U=R/O,D=O/m.length;return Math.max(0,1-U/Xe.tolerance)*D};return s.jsx("div",{className:"trace-practice min-h-screen","data-guide-stage":q,style:{background:"transparent"},children:s.jsx("div",{className:"flex items-center justify-center min-h-screen",children:s.jsxs("div",{className:"relative",style:{transform:"translateY(-120px) translateX(10px)"},children:[V&&s.jsx("div",{className:"absolute -top-20 left-1/2 transform -translate-x-1/2 z-20",children:s.jsx("div",{className:"bg-black bg-opacity-75 text-white px-6 py-3 rounded-lg shadow-lg",children:s.jsxs("div",{className:"text-lg font-medium text-center whitespace-nowrap",children:[C,s.jsx("span",{className:"animate-pulse",children:"|"})]})})}),s.jsxs("div",{className:"relative flex items-center justify-center",children:[N&&s.jsx("div",{style:{width:`${ie.width}px`,height:`${ie.height}px`,transform:`scale(${W})`,opacity:le,transition:"none"},children:s.jsx("img",{src:N,alt:"背景图片",className:"object-contain w-full h-full"})}),s.jsx("div",{className:"absolute inset-0 flex items-center justify-center",style:{transform:`scale(${W})`,opacity:le,transition:"none"},children:s.jsx("canvas",{ref:o,width:ie.width,height:ie.height,className:"bg-transparent",style:{touchAction:"none",userSelect:"none",width:`${ie.width}px`,height:`${ie.height}px`,position:"relative",margin:0,padding:0,border:"none",outline:"none",display:"block"},onMouseDown:_==="active"?ns:void 0,onMouseMove:_==="active"?rs:void 0,onMouseUp:_==="active"?x:void 0,onMouseLeave:_==="active"?()=>h(!1):void 0})}),(_==="completing"||_==="showing"||_==="exiting")&&G&&s.jsx("div",{className:"absolute inset-0 flex flex-col items-center justify-center",style:{opacity:pe,transform:`scale(${F})`,transition:"none"},children:s.jsx("img",{src:G,alt:"完整图片",className:"object-contain",style:{width:`${ie.width}px`,height:`${ie.height}px`},onError:m=>{console.error("奖励图片加载失败"),m.currentTarget.style.display="none"}})})]})]})})})},ds=()=>{const e=Fe(),{level:t,stage:n}=Sa(),[r,a]=u.useState("level-select"),[o,i]=u.useState(""),[l,c]=u.useState(""),[h,d]=u.useState("free-draw"),[f,j]=u.useState(()=>{const N=localStorage.getItem("memorybrush-unlocked-levels");return N?JSON.parse(N):["level-1"]}),[I,b]=u.useState(()=>{const N=localStorage.getItem("memorybrush-unlocked-stages");return N?JSON.parse(N):["L1-1"]}),[v,g]=u.useState(null),[T,_]=u.useState(!1),P=Ue.useCallback(async(N,$)=>{var G,X,H;_(!0);try{if(N==="trace"&&$)try{console.log(`尝试加载轨迹文件: /${$}/trace.json`);const re=await fetch(`/${$}/trace.json`);if(!re.ok)throw new Error(`HTTP ${re.status}: 无法加载 ${$}/trace.json`);const fe=re.headers.get("content-type");if(!fe||!fe.includes("application/json"))throw new Error(`文件不是JSON格式: ${fe}`);const Ne=await re.json();console.log("成功加载轨迹数据:",Ne);const Ye=((G=Ne.strokes)==null?void 0:G.map((Oe,st)=>({id:`path_${st}`,points:Oe.points||[]})))||[];console.log(`转换后的引导路径数量: ${Ye.length}`),g({sessionId:`trace_${Date.now()}`,guidePaths:Ye,guideImage:Ne.backgroundImage||"",artworkName:"trace",canvasSize:Ne.canvasSize||{width:800,height:600},levelStage:$}),_(!1),console.log("轨迹练习数据设置成功");return}catch(re){if(console.warn(`从文件加载失败: ${(re==null?void 0:re.message)||re}`),console.warn(`文件路径: /${$}/trace.json`),(X=re==null?void 0:re.message)!=null&&X.includes("404")||(H=re==null?void 0:re.message)!=null&&H.includes("HTTP 404")){K.warning(`${$} 目录下没有 trace.json 文件，该关卡暂不支持图像描线功能`),_(!1);return}console.warn("尝试使用后端API作为备选方案")}if(N==="trace")throw new Error("trace类型轨迹文件加载失败，且没有后端支持");const ne=await fetch("/api/v1/games/trace/start",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({artwork_name:N,difficulty:"medium"})});if(!ne.ok)throw new Error("启动描线练习失败");const q=await ne.json();if(q.success)g({sessionId:q.session_id,guidePaths:q.guide_paths,guideImage:q.guide_image,artworkName:q.artwork_name,canvasSize:q.canvas_size,levelStage:$});else throw new Error(q.error||"启动描线练习失败")}catch(ne){console.error("启动描线练习失败:",ne),K.error("启动描线练习失败，请重试")}finally{_(!1)}},[]),W=Ue.useCallback(N=>{P("trace",N)},[P]);Ue.useEffect(()=>{if(t&&n){let N=n;!n.includes("-")&&t&&(N=`${t}-${n}`),i(t),c(N),a("playing");const $=Z.find(X=>X.id===t),G=$==null?void 0:$.stages.find(X=>X.id===N);G&&(d(G.type),G.type==="image-trace"&&W(N))}else t?(i(t),a("stage-select")):a("level-select")},[t,n,W]);const Z=[{id:"level-1",title:"线条启蒙",description:"从基础的线条开始，培养手部协调能力",icon:"📈",difficulty:1,stages:[{id:"L1-1",title:"自由画线",description:"随意画线，创作属于您的艺术作品",type:"free-draw"},{id:"L1-2",title:"匀速直线",description:"跟随引导线条，提高绘画精准度",type:"image-trace"},{id:"L1-3",title:"匀速线条组合",description:"从真实图片中抽取线条进行描画练习",type:"image-trace"},{id:"L1-4",title:"曲线消除",description:"绘制三角形、正方形、矩形等直线图形",type:"image-trace"},{id:"L1-5",title:"TBD",description:"绘制圆形、椭圆等曲线图形",type:"shape-curved"}]},{id:"level-2",title:"立体空间",description:"从二维图形到三维立体，提升空间想象力",icon:"📦",difficulty:2,stages:[{id:"L2-1",title:"立体图形",description:"绘制锥形、立方体、圆柱体等三维图形",type:"coming-soon",comingSoon:!0},{id:"L2-2",title:"色彩填充",description:"为几何图形填色，学习色系和色谱搭配",type:"coming-soon",comingSoon:!0},{id:"L2-3",title:"质感画笔",description:"复杂曲线描边，选择不同质感的画笔填色",type:"coming-soon",comingSoon:!0}]},{id:"level-3",title:"画面构图",description:"通过引导线条，完成完整的艺术画面",icon:"🖼️",difficulty:3,stages:[{id:"abstract-art",title:"抽象艺术",description:"用抽象线条和色块创作现代艺术作品",type:"coming-soon",comingSoon:!0},{id:"geometric-still",title:"几何静物",description:"绘制几何形状组成的静物画",type:"coming-soon",comingSoon:!0},{id:"life-objects",title:"生活物品",description:"描绘日常生活中的物品和场景",type:"coming-soon",comingSoon:!0}]},{id:"level-4",title:"智能创作",description:"上传照片，AI辅助创作个性化艺术作品",icon:"📷",difficulty:4,stages:[{id:"photo-trace",title:"照片描边",description:"上传照片，提取轮廓进行描边练习",type:"coming-soon",comingSoon:!0},{id:"style-render",title:"风格渲染",description:"选择不同艺术风格，AI辅助渲染作品",type:"coming-soon",comingSoon:!0},{id:"ai-creation",title:"AI协作",description:"与AI协作，创作独特的个人艺术作品",type:"coming-soon",comingSoon:!0}]}],le=N=>{i(N),a("stage-select"),e(`/game/${N}`)},ee=Ue.useCallback(N=>{c(N),a("playing"),e(`/game/${o}/${N}`)},[o,e]),pe=()=>{a("level-select"),i(""),c(""),e("/game")},w=()=>{a("stage-select"),c(""),e(`/game/${o}`)},F=()=>{j(["level-1"]),b(["L1-1"]),localStorage.removeItem("memorybrush-unlocked-levels"),localStorage.removeItem("memorybrush-unlocked-stages"),K.success("游戏进度已重置")},E=async(N,$)=>{if(v)try{const G=await fetch("/api/v1/games/trace/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({session_id:v.sessionId,user_paths:$,completed_path_id:N})});if(!G.ok)throw new Error("提交描线进度失败");(await G.json()).success&&console.log("路径完成:",N)}catch(G){console.error("提交描线进度失败:",G)}},C=()=>{K.success("恭喜！描线练习完成！"),l&&V(l),w()},Y=(N,$)=>{const G=Z.find(q=>q.id===N);if(!G)return;const X=$||I;if(G.stages.filter(q=>!q.comingSoon).every(q=>X.includes(q.id))){const q=["level-1","level-2","level-3","level-4"],re=q.indexOf(N);if(re>=0&&re<q.length-1){const fe=q[re+1];if(!f.includes(fe)){const Ne=[...f,fe];j(Ne),localStorage.setItem("memorybrush-unlocked-levels",JSON.stringify(Ne)),K.success(`🎉 恭喜解锁新级别：${te(fe)}！`)}}}},V=N=>{const $=Z.find(H=>H.id===o);if(!$)return;const G=$.stages.map(H=>H.id),X=G.indexOf(N);if(X>=0&&X<G.length-1){const H=G[X+1];if(!I.includes(H)){const ne=[...I,H];b(ne),localStorage.setItem("memorybrush-unlocked-stages",JSON.stringify(ne));const q=$.stages.find(re=>re.id===H);q&&K.success(`🎉 恭喜解锁新练习：${q.title}！`),o&&Y(o,ne)}}else o&&Y(o)},te=N=>({"level-1":"线条启蒙","level-2":"立体空间","level-3":"画面构图","level-4":"智能创作"})[N]||"新级别",de=()=>{K.success("恭喜完成练习！"),l&&V(l),setTimeout(()=>{w()},2e3)},be=N=>{K.success(`恭喜完成！得分：${N}分`),l&&V(l),setTimeout(()=>{w()},2e3)},ie=()=>{switch(h){case"free-draw":return s.jsx(Ji,{onBack:w,onComplete:de});case"trace":return T?s.jsx("div",{className:"flex items-center justify-center min-h-screen",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"}),s.jsx("p",{className:"mt-4 text-lg",children:"正在准备描线练习..."})]})}):v?s.jsxs("div",{className:"container mx-auto px-4 py-8",children:[s.jsx("div",{className:"mb-4",children:s.jsx("button",{onClick:w,className:"px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600",children:"← 返回阶段选择"})}),s.jsx(un,{guidePaths:v.guidePaths,guideImage:v.guideImage,originalCanvasSize:v.canvasSize,levelStage:v.levelStage,onPathComplete:E,onAllComplete:C})]}):s.jsx("div",{className:"flex items-center justify-center min-h-screen",children:s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-lg",children:"描线练习数据加载失败"}),s.jsx("button",{onClick:w,className:"mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"返回阶段选择"})]})});case"image-trace":return T?s.jsx("div",{className:"flex items-center justify-center min-h-screen",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"}),s.jsx("p",{className:"mt-4 text-lg",children:"正在从图片抽取线条..."})]})}):v?s.jsxs("div",{className:"container mx-auto px-4 py-8",children:[s.jsx("div",{className:"mb-4",children:s.jsx("button",{onClick:w,className:"px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600",children:"← 返回阶段选择"})}),s.jsx(un,{guidePaths:v.guidePaths,guideImage:v.guideImage,originalCanvasSize:v.canvasSize,levelStage:v.levelStage,onPathComplete:E,onAllComplete:C})]}):s.jsx("div",{className:"flex items-center justify-center min-h-screen",children:s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-lg",children:"图像线条抽取失败"}),s.jsx("button",{onClick:w,className:"mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"返回阶段选择"})]})});case"shape-straight":return s.jsx(dn,{onBack:w,onComplete:be,shapeType:"straight"});case"shape-curved":return s.jsx(dn,{onBack:w,onComplete:be,shapeType:"curved"});default:return null}},S=()=>Z.find(N=>N.id===o);return s.jsxs("div",{className:"min-h-screen pt-8",style:{background:"transparent"},children:[r==="level-select"&&s.jsx(ho,{onSelectLevel:le,unlockedLevels:f,onResetProgress:F}),r==="stage-select"&&S()&&s.jsx(mo,{level:S(),onSelectStage:ee,onBack:pe,unlockedStages:I}),r==="playing"&&ie()]})},{Title:Vi,Paragraph:Yi}=Te,Xi=()=>{const e=Fe();return s.jsx("div",{className:"min-h-screen",style:{background:"transparent"},children:s.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:s.jsx(ae.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ce,{className:"text-center p-12 shadow-soft",children:[s.jsxs("div",{className:"mb-8",children:[s.jsx("div",{className:"w-24 h-24 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx(pn,{className:"text-4xl text-white"})}),s.jsx(Vi,{level:1,className:"text-blue-600 mb-4",children:"个人资料"}),s.jsxs(Yi,{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:["个人资料功能正在开发中，敬请期待！",s.jsx("br",{}),"我们正在为您打造个性化的用户体验。"]})]}),s.jsx(me,{size:"large",children:s.jsx(J,{size:"large",icon:s.jsx(qe,{}),onClick:()=>e("/"),className:"h-12 px-8 text-lg",children:"返回首页"})})]})})})})},{Title:Ki,Paragraph:Qi}=Te,Zi=()=>{const e=Fe();return s.jsx("div",{className:"min-h-screen",style:{background:"transparent"},children:s.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:s.jsx(ae.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ce,{className:"text-center p-12 shadow-soft",children:[s.jsxs("div",{className:"mb-8",children:[s.jsx("div",{className:"w-24 h-24 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx(Es,{className:"text-4xl text-white"})}),s.jsx(Ki,{level:1,className:"text-green-600 mb-4",children:"设置"}),s.jsxs(Qi,{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:["设置功能正在开发中，敬请期待！",s.jsx("br",{}),"我们正在为您准备贴心的个性化设置选项。"]})]}),s.jsx(me,{size:"large",children:s.jsx(J,{size:"large",icon:s.jsx(qe,{}),onClick:()=>e("/"),className:"h-12 px-8 text-lg",children:"返回首页"})})]})})})})},{Title:el,Paragraph:tl}=Te,sl=()=>{const e=Fe();return s.jsx("div",{className:"min-h-screen",style:{background:"transparent"},children:s.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:s.jsx(ae.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ce,{className:"text-center p-12 shadow-soft",children:[s.jsxs("div",{className:"mb-8",children:[s.jsx("div",{className:"w-24 h-24 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx(Ke,{className:"text-4xl text-white"})}),s.jsx(el,{level:1,className:"text-purple-600 mb-4",children:"作品画廊"}),s.jsxs(tl,{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:["作品画廊功能正在开发中，敬请期待！",s.jsx("br",{}),"我们正在为您打造精美的作品展示空间。"]})]}),s.jsx(me,{size:"large",children:s.jsx(J,{size:"large",icon:s.jsx(qe,{}),onClick:()=>e("/"),className:"h-12 px-8 text-lg",children:"返回首页"})})]})})})})},{Title:nl,Paragraph:rl}=Te,al=()=>{const e=Fe();return s.jsx("div",{className:"min-h-screen",style:{background:"transparent"},children:s.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:s.jsx(ae.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ce,{className:"text-center p-12 shadow-soft",children:[s.jsxs("div",{className:"mb-8",children:[s.jsx("div",{className:"w-24 h-24 bg-gradient-to-br from-yellow-400 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx(_t,{className:"text-4xl text-white"})}),s.jsx(nl,{level:1,className:"text-orange-600 mb-4",children:"排行榜"}),s.jsxs(rl,{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:["排行榜功能正在开发中，敬请期待！",s.jsx("br",{}),"我们正在为您打造激励性的成就展示系统。"]})]}),s.jsx(me,{size:"large",children:s.jsx(J,{size:"large",icon:s.jsx(qe,{}),onClick:()=>e("/"),className:"h-12 px-8 text-lg",children:"返回首页"})})]})})})})};class ol{constructor(){vt(this,"baseUrl","/traces")}async createSession(t){try{return(await ke.post(`${this.baseUrl}/sessions`,t)).data}catch(n){throw console.error("创建轨迹会话失败:",n),n}}async getSessions(t=50,n=0){try{return(await ke.get(`${this.baseUrl}/sessions`,{params:{limit:t,offset:n}})).data}catch(r){throw console.error("获取轨迹会话列表失败:",r),r}}async getSession(t){try{return(await ke.get(`${this.baseUrl}/sessions/${t}`)).data}catch(n){throw console.error("获取轨迹会话失败:",n),n}}async deleteSession(t){try{return(await ke.delete(`${this.baseUrl}/sessions/${t}`)).data}catch(n){throw console.error("删除轨迹会话失败:",n),n}}async generateGuidePaths(t,n={}){try{const r={session_id:t,simplification_level:n.simplification_level||"medium",min_stroke_length:n.min_stroke_length||20,merge_distance:n.merge_distance||50};return(await ke.post(`${this.baseUrl}/sessions/${t}/generate-guide`,r)).data}catch(r){throw console.error("生成引导线失败:",r),r}}exportSessionAsJson(t){const n=JSON.stringify(t,null,2),r=new Blob([n],{type:"application/json"}),a=URL.createObjectURL(r),o=document.createElement("a");o.href=a,o.download=`trace_${t.name}_${Date.now()}.json`,o.click(),URL.revokeObjectURL(a)}async importSessionFromJson(t){return new Promise((n,r)=>{const a=new FileReader;a.onload=o=>{var i;try{const l=(i=o.target)==null?void 0:i.result,c=JSON.parse(l);if(!this.validateTraceSession(c))throw new Error("无效的轨迹数据格式");n(c)}catch(l){r(new Error("解析JSON文件失败: "+l.message))}},a.onerror=()=>{r(new Error("读取文件失败"))},a.readAsText(t)})}validateTraceSession(t){if(!t||typeof t!="object")return!1;const n=["id","name","strokes","canvasSize","createdAt","duration"];for(const r of n)if(!(r in t))return!1;return!(!Array.isArray(t.strokes)||!t.canvasSize||typeof t.canvasSize!="object"||!("width"in t.canvasSize)||!("height"in t.canvasSize))}saveToLocalStorage(t){try{const n=this.getFromLocalStorage(),r=n.findIndex(a=>a.id===t.id);r>=0?n[r]=t:n.push(t),localStorage.setItem("traceSessions",JSON.stringify(n))}catch(n){throw console.error("保存到本地存储失败:",n),n}}getFromLocalStorage(){try{const t=localStorage.getItem("traceSessions");return t?JSON.parse(t):[]}catch(t){return console.error("从本地存储读取失败:",t),[]}}deleteFromLocalStorage(t){try{const r=this.getFromLocalStorage().filter(a=>a.id!==t);localStorage.setItem("traceSessions",JSON.stringify(r))}catch(n){throw console.error("从本地存储删除失败:",n),n}}clearLocalStorage(){try{localStorage.removeItem("traceSessions")}catch(t){throw console.error("清空本地存储失败:",t),t}}calculateSessionStats(t){const n=t.strokes.length;let r=0,a=0;return t.strokes.forEach(o=>{r+=o.points.length;for(let i=1;i<o.points.length;i++){const l=o.points[i].x-o.points[i-1].x,c=o.points[i].y-o.points[i-1].y;a+=Math.sqrt(l*l+c*c)}}),{totalStrokes:n,totalPoints:r,totalLength:a,averageStrokeLength:n>0?a/n:0,duration:t.duration}}}const Ee=new ol,{Title:il,Text:_e}=Te,{Option:kt}=Lt,ll=()=>{const e=u.useRef(null),[t,n]=u.useState(!1),[r,a]=u.useState(!1),[o,i]=u.useState(!1),[l,c]=u.useState(null),[h,d]=u.useState([]),[f,j]=u.useState(""),[I,b]=u.useState(""),[v,g]=u.useState("#000000"),[T,_]=u.useState(3),[P,W]=u.useState({width:800,height:600}),[Z,le]=u.useState(null),[ee,pe]=u.useState(null),[w,F]=u.useState(0),[E,C]=u.useState(null),[Y,V]=u.useState(!1),[te,de]=u.useState(!1),[be,ie]=u.useState(null),[S,N]=u.useState(null),$=u.useRef(null),G=u.useCallback(x=>{h.forEach(z=>{if(!(z.points.length<2)){x.strokeStyle="#000000",x.lineWidth=3,x.lineCap="round",x.lineJoin="round",x.beginPath(),x.moveTo(z.points[0].x,z.points[0].y);for(let B=1;B<z.points.length;B++)x.lineTo(z.points[B].x,z.points[B].y);x.stroke()}})},[h]),X=u.useCallback((x,z)=>{if(!(z.points.length<2)){x.strokeStyle=v,x.lineWidth=T,x.lineCap="round",x.lineJoin="round",x.beginPath(),x.moveTo(z.points[0].x,z.points[0].y);for(let B=1;B<z.points.length;B++)x.lineTo(z.points[B].x,z.points[B].y);x.stroke()}},[v,T]),H=u.useCallback(()=>{const x=e.current;if(!x)return;const z=x.getContext("2d");z&&(z.clearRect(0,0,x.width,x.height),$.current&&z.drawImage($.current,0,0,x.width,x.height),G(z),l&&l.points.length>0&&X(z,l))},[h,G,l,X]);u.useEffect(()=>{if(S){const x=new Image;x.onload=()=>{$.current=x,H()},x.src=S}else $.current=null,H()},[S,H]),u.useEffect(()=>{H()},[l,H]),u.useEffect(()=>{const x=e.current;if(!x)return;const z=x.getContext("2d");if(!z)return;x.width=P.width,x.height=P.height,x.style.width=`${P.width}px`,x.style.height=`${P.height}px`,z.lineCap="round",z.lineJoin="round";const B=k=>{if(!t||r)return;k.preventDefault(),k.stopPropagation();const R=k.touches[0],O=x.getBoundingClientRect(),U=x.width/O.width,D=x.height/O.height,A={x:(R.clientX-O.left)*U,y:(R.clientY-O.top)*D};i(!0);const M={id:`stroke_${Date.now()}`,points:[A]};c(M)},m=k=>{if(!o||!l||!t||r)return;k.preventDefault(),k.stopPropagation();const R=k.touches[0],O=x.getBoundingClientRect(),U=x.width/O.width,D=x.height/O.height,A={x:(R.clientX-O.left)*U,y:(R.clientY-O.top)*D},L={...l,points:[...l.points,A]};c(L)},y=k=>{k.preventDefault(),k.stopPropagation(),!(!o||!l||!t||r)&&(i(!1),d(R=>[...R,l]),c(null))};return x.addEventListener("touchstart",B,{passive:!1}),x.addEventListener("touchmove",m,{passive:!1}),x.addEventListener("touchend",y,{passive:!1}),H(),()=>{x.removeEventListener("touchstart",B),x.removeEventListener("touchmove",m),x.removeEventListener("touchend",y)}},[P,H,t,r,o,l]);const ne=u.useCallback(()=>{const x=e.current;if(!x)return;const z=x.getContext("2d");z&&(z.clearRect(0,0,x.width,x.height),$.current&&z.drawImage($.current,0,0,x.width,x.height))},[]),q=x=>{const z=e.current;if(!z)return{x:0,y:0};const B=z.getBoundingClientRect(),m=z.width/B.width,y=z.height/B.height,k=(x.clientX-B.left)*m,R=(x.clientY-B.top)*y;return{x:k,y:R}},re=x=>{if(!t||r)return;x.preventDefault(),i(!0);const z=q(x),m={id:`stroke_${Date.now()}`,points:[z]};c(m)},fe=x=>{if(!o||!l||!t||r)return;x.preventDefault();const z=q(x),B={...l,points:[...l.points,z]};c(B)},Ne=()=>{!o||!l||(i(!1),d(x=>[...x,l]),c(null))},Ye=()=>{if(!f.trim()){K.error("请输入会话名称");return}n(!0),a(!1),pe(Date.now()),F(0),d([]),ne(),K.success("开始录制轨迹")},Oe=()=>{t&&(r?(E&&F(x=>x+(Date.now()-E)),C(null),a(!1),K.info("恢复录制")):(C(Date.now()),a(!0),i(!1),c(null),K.info("暂停录制")))},st=()=>{n(!1),a(!1),i(!1),c(null),E&&(F(x=>x+(Date.now()-E)),C(null)),K.success("录制已停止")},ue=()=>{d([]),ne(),K.info("画布已清空")},xt=(x,z=3)=>x.filter(B=>{if(B.points.length<2)return!1;let m=0;for(let y=1;y<B.points.length;y++){const k=B.points[y].x-B.points[y-1].x,R=B.points[y].y-B.points[y-1].y;m+=Math.sqrt(k*k+R*R)}return m>=z}).map(B=>{if(B.points.length<10)return B;const m=yt(B.points),y=ts(m);return{...B,points:y}}),yt=(x,z=2)=>{if(x.length<=3)return x;const B=[];B.push(x[0]);for(let m=1;m<x.length-1;m++){const y=x[m-1],k=x[m],R=x[m+1],O=(y.x+k.x*2+R.x)/4,U=(y.y+k.y*2+R.y)/4;B.push({x:O,y:U})}return B.push(x[x.length-1]),B},ts=(x,z=.5)=>{if(x.length<=3)return x;const B=[x[0]];for(let m=1;m<x.length-1;m++){const y=B[B.length-1],k=x[m];Math.sqrt(Math.pow(k.x-y.x,2)+Math.pow(k.y-y.y,2))>z&&B.push(k)}return B.push(x[x.length-1]),B},bt=async()=>{if(h.length===0){K.error("没有轨迹数据可保存");return}de(!0);try{const x=ee?Date.now()-ee:0,z=xt(h);console.log(`轨迹优化完成: 原始笔画数 ${h.length}, 优化后笔画数 ${z.length}`);const B={name:f,description:I,strokes:z,canvasSize:P,duration:x-w};try{const m=await Ee.createSession(B);if(m.success&&m.data)K.success("轨迹数据已保存到服务器"),console.log("保存成功，会话ID:",m.data.id);else throw new Error(m.message)}catch(m){console.warn("API保存失败，使用本地存储:",m);const y={id:`session_${Date.now()}`,name:f,description:I,strokes:h,canvasSize:P,createdAt:new Date().toISOString(),duration:x-w};Ee.saveToLocalStorage(y),K.success("轨迹数据已保存到本地")}j(""),b(""),d([]),ne()}catch(x){console.error("保存失败:",x),K.error("保存失败，请重试")}finally{de(!1)}},ss=x=>{if(!["image/jpeg","image/jpg","image/png","image/gif","image/webp"].includes(x.type))return K.error("请上传图片文件 (JPG, PNG, GIF, WebP)"),!1;const B=10*1024*1024;if(x.size>B)return K.error("图片文件大小不能超过 10MB"),!1;const m=new FileReader;return m.onload=y=>{var O;const k=(O=y.target)==null?void 0:O.result;N(k);const R=new Image;R.onload=()=>{const U=Math.min(window.innerWidth*.8,1200),D=Math.min(window.innerHeight*.7,800);let A=R.width,L=R.height;A>U&&(L=L*U/A,A=U),L>D&&(A=A*D/L,L=D);const M={width:Math.round(A),height:Math.round(L)};W(M),K.success(`背景图已加载，画布尺寸调整为 ${Math.round(A)} × ${Math.round(L)}`)},R.src=k},m.readAsDataURL(x),!1},nt=()=>{N(null),W({width:800,height:600}),K.info("背景图已移除")},ns=()=>{d([]),nt(),ne(),K.info("画布和背景图已清空")},rs=()=>{if(h.length===0){K.error("没有轨迹数据可导出");return}const x=ee?Date.now()-ee:0,z=xt(h);console.log(`导出轨迹优化完成: 原始笔画数 ${h.length}, 优化后笔画数 ${z.length}`);const B={id:`session_${Date.now()}`,name:f||"未命名会话",description:I,strokes:z,canvasSize:P,createdAt:new Date().toISOString(),duration:x-w};Ee.exportSessionAsJson(B),K.success("轨迹数据已导出")};return s.jsxs("div",{className:"trace-recorder min-h-screen p-3 bg-gray-50",children:[s.jsx("div",{className:"max-w-7xl mx-auto",children:s.jsx(ae.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},children:s.jsxs("div",{className:"grid grid-cols-1 xl:grid-cols-4 gap-4",children:[s.jsx("div",{className:"xl:col-span-1",children:s.jsx(ce,{size:"small",className:"mb-3",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx(_e,{strong:!0,className:"block mb-2",children:"会话信息"}),s.jsxs(me,{direction:"vertical",className:"w-full",size:"small",children:[s.jsx(Os,{placeholder:"会话名称",value:f,onChange:x=>j(x.target.value),disabled:t,size:"small"}),s.jsx(Os.TextArea,{placeholder:"会话描述（可选）",value:I,onChange:x=>b(x.target.value),disabled:t,rows:2,size:"small"})]})]}),s.jsxs("div",{children:[s.jsx(_e,{strong:!0,className:"block mb-2",children:"背景图"}),s.jsxs(me,{direction:"vertical",className:"w-full",size:"small",children:[s.jsx(J,{icon:s.jsx(Ke,{}),onClick:()=>{const x=document.createElement("input");x.type="file",x.accept="image/*",x.onchange=z=>{var m;const B=(m=z.target.files)==null?void 0:m[0];B&&ss(B)},x.click()},disabled:t,className:"w-full",size:"small",children:"上传背景图"}),S&&s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(_e,{type:"success",style:{fontSize:"11px"},children:"✓ 已加载"}),s.jsx(J,{icon:s.jsx(hs,{}),size:"small",danger:!0,onClick:nt,disabled:t,children:"移除"})]})]})]}),s.jsxs("div",{children:[s.jsx(_e,{strong:!0,className:"block mb-2",children:"画笔设置"}),s.jsxs(me,{direction:"vertical",className:"w-full",size:"small",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(_e,{style:{fontSize:"12px",width:"30px"},children:"颜色"}),s.jsx("input",{type:"color",value:v,onChange:x=>g(x.target.value),disabled:t&&!r,className:"w-8 h-6 border rounded"})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(_e,{style:{fontSize:"12px",width:"30px"},children:"大小"}),s.jsxs(Lt,{value:T,onChange:_,disabled:t&&!r,className:"flex-1",size:"small",children:[s.jsx(kt,{value:1,children:"细 (1px)"}),s.jsx(kt,{value:3,children:"中 (3px)"}),s.jsx(kt,{value:5,children:"粗 (5px)"}),s.jsx(kt,{value:8,children:"很粗 (8px)"})]})]})]})]}),s.jsxs("div",{children:[s.jsx(il,{level:4,children:"录制控制"}),s.jsxs(me,{wrap:!0,children:[t?s.jsxs(s.Fragment,{children:[s.jsx(J,{icon:r?s.jsx(De,{}):s.jsx(vn,{}),onClick:Oe,size:"large",children:r?"恢复":"暂停"}),s.jsx(J,{icon:s.jsx(Tr,{}),onClick:st,size:"large",danger:!0,children:"停止"})]}):s.jsx(J,{type:"primary",icon:s.jsx(De,{}),onClick:Ye,size:"large",children:"开始录制"}),s.jsx(J,{icon:s.jsx(yn,{}),onClick:ue,disabled:t&&!r,children:"清空轨迹"}),S&&s.jsx(J,{icon:s.jsx(hs,{}),onClick:ns,disabled:t&&!r,danger:!0,children:"清空全部"})]})]}),s.jsxs("div",{children:[s.jsx(_e,{strong:!0,className:"block mb-2",children:"保存操作"}),s.jsxs(me,{direction:"vertical",className:"w-full",size:"small",children:[s.jsx(J,{type:"primary",icon:s.jsx(bn,{}),onClick:bt,disabled:h.length===0||te,loading:te,className:"w-full",size:"small",children:te?"保存中...":"保存轨迹"}),s.jsx(J,{icon:s.jsx(ks,{}),onClick:rs,disabled:h.length===0,className:"w-full",size:"small",children:"导出JSON"}),s.jsx(J,{icon:s.jsx(jn,{}),onClick:()=>V(!0),disabled:h.length===0,className:"w-full",size:"small",children:"预览轨迹"})]})]}),t&&s.jsxs("div",{className:"mt-4 p-2 bg-gray-100 rounded",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:`w-2 h-2 rounded-full ${r?"bg-yellow-500":"bg-red-500 animate-pulse"}`}),s.jsx(_e,{style:{fontSize:"11px"},strong:!0,children:r?"录制已暂停":"正在录制..."})]}),s.jsxs(_e,{style:{fontSize:"11px"},type:"secondary",children:["笔画数: ",h.length]})]})]})})}),s.jsx("div",{className:"xl:col-span-3",children:s.jsx(ce,{size:"small",children:s.jsxs("div",{className:"flex flex-col items-center",children:[s.jsx("div",{className:"mb-2 text-center",children:s.jsxs(_e,{type:"secondary",style:{fontSize:"12px"},children:["画布: ",P.width," × ",P.height,S&&" • 已加载背景图"]})}),s.jsx("div",{className:"relative",children:s.jsx("canvas",{ref:e,width:P.width,height:P.height,className:`border-2 border-gray-300 rounded-lg cursor-crosshair shadow-md ${S?"bg-transparent":"bg-white"}`,style:{touchAction:"none",userSelect:"none",maxWidth:"100%",maxHeight:"70vh",position:"relative",margin:0,padding:0,border:"none",outline:"none",display:"block"},onMouseDown:re,onMouseMove:fe,onMouseUp:Ne,onMouseLeave:()=>i(!1)})})]})})})]})})}),s.jsx(At,{title:"轨迹预览",open:Y,onCancel:()=>V(!1),footer:null,width:900,children:s.jsxs("div",{className:"text-center",children:[s.jsxs(_e,{children:["会话: ",f||"未命名"]}),s.jsx("br",{}),s.jsxs(_e,{type:"secondary",children:["笔画数: ",h.length]}),s.jsx("br",{}),s.jsxs(_e,{type:"secondary",children:["录制时长: ",Math.round((Date.now()-(ee||Date.now())-w)/1e3),"秒"]})]})})]})},{Title:cl,Text:Ie}=Te,{Option:us}=Lt,dl=({onGenerateGuide:e})=>{const[t,n]=u.useState([]),[r,a]=u.useState(!1),[o,i]=u.useState(null),[l,c]=u.useState(!1),[h,d]=u.useState(!1),[f,j]=u.useState(null),[I,b]=u.useState({simplification_level:"medium",min_stroke_length:20,merge_distance:50}),[v,g]=u.useState(!1),T=async()=>{a(!0);try{let E=[];try{const C=await Ee.getSessions();E=[...C],console.log("从API加载了",C.length,"个会话")}catch(C){console.warn("API加载失败:",C)}try{const C=Ee.getFromLocalStorage();console.log("从本地存储加载了",C.length,"个会话");const Y=new Set(E.map(te=>te.id)),V=C.filter(te=>!Y.has(te.id));E=[...E,...V]}catch(C){console.warn("本地存储加载失败:",C)}E.sort((C,Y)=>new Date(Y.createdAt).getTime()-new Date(C.createdAt).getTime()),n(E),console.log("总共加载了",E.length,"个会话")}catch(E){console.error("加载轨迹会话失败:",E),K.error("加载轨迹会话失败")}finally{a(!1)}};u.useEffect(()=>{T()},[]);const _=async E=>{try{try{await Ee.deleteSession(E)}catch(C){console.warn("API删除失败，从本地存储删除:",C),Ee.deleteFromLocalStorage(E)}K.success("会话删除成功"),T()}catch(C){console.error("删除会话失败:",C),K.error("删除会话失败")}},P=E=>{i(E),c(!0)},W=E=>{Ee.exportSessionAsJson(E),K.success("会话数据已导出")},Z=()=>{const E=document.createElement("input");E.type="file",E.accept=".json",E.onchange=async C=>{var V;const Y=(V=C.target.files)==null?void 0:V[0];if(Y)try{const te=await Ee.importSessionFromJson(Y);try{await Ee.createSession({name:te.name,description:te.description,strokes:te.strokes,canvasSize:te.canvasSize,duration:te.duration})}catch(de){console.warn("API保存失败，保存到本地存储:",de),Ee.saveToLocalStorage(te)}K.success("会话导入成功"),T()}catch(te){console.error("导入会话失败:",te),K.error("导入会话失败: "+te.message)}},E.click()},le=E=>{j(E),d(!0)},ee=async()=>{if(f){g(!0);try{let E=f,C=!1;try{await Ee.getSession(f.id)}catch{console.log("会话不存在于API中，尝试上传..."),C=!0}if(C)try{const V=await Ee.createSession({name:f.name,description:f.description,strokes:f.strokes,canvasSize:f.canvasSize,duration:f.duration});if(V.success&&V.data)E=V.data,console.log("会话上传成功，新ID:",E.id);else throw new Error("上传会话失败")}catch(V){console.error("上传会话失败:",V),K.error("无法上传会话到服务器，请检查网络连接");return}const Y=await Ee.generateGuidePaths(E.id,I);if(Y.success&&Y.guide_paths)K.success("引导线生成成功"),d(!1),e&&Y.canvas_size&&e(Y.guide_paths,Y.canvas_size,void 0),C&&T();else throw new Error(Y.message||"生成引导线失败")}catch(E){console.error("生成引导线失败:",E),K.error("生成引导线失败: "+E.message)}finally{g(!1)}}},pe=E=>{const C=Math.floor(E/1e3),Y=Math.floor(C/60),V=C%60;return`${Y}:${V.toString().padStart(2,"0")}`},w=E=>Ee.calculateSessionStats(E),F=[{title:"会话名称",dataIndex:"name",key:"name",render:(E,C)=>s.jsxs("div",{children:[s.jsx(Ie,{strong:!0,children:E}),C.description&&s.jsx("div",{children:s.jsx(Ie,{type:"secondary",style:{fontSize:"12px"},children:C.description})})]})},{title:"统计信息",key:"stats",render:(E,C)=>{const Y=w(C);return s.jsxs(me,{direction:"vertical",size:"small",children:[s.jsxs(Be,{color:"blue",children:[Y.totalStrokes," 笔画"]}),s.jsxs(Be,{color:"green",children:[Y.totalPoints," 点"]}),s.jsx(Be,{color:"orange",children:pe(Y.duration)})]})}},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",render:E=>new Date(E).toLocaleString()},{title:"操作",key:"actions",render:(E,C)=>s.jsxs(me,{children:[s.jsx(jt,{title:"预览",children:s.jsx(J,{icon:s.jsx(jn,{}),onClick:()=>P(C),size:"small"})}),s.jsx(jt,{title:"生成引导线",children:s.jsx(J,{icon:s.jsx(De,{}),onClick:()=>le(C),size:"small",type:"primary"})}),s.jsx(jt,{title:"导出",children:s.jsx(J,{icon:s.jsx(ks,{}),onClick:()=>W(C),size:"small"})}),s.jsx(jt,{title:"删除",children:s.jsx(gn,{title:"确定要删除这个会话吗？",onConfirm:()=>_(C.id),okText:"确定",cancelText:"取消",children:s.jsx(J,{icon:s.jsx(hs,{}),danger:!0,size:"small"})})})]})}];return s.jsxs("div",{className:"trace-manager",children:[s.jsx(ae.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ce,{children:[s.jsxs("div",{className:"flex justify-between items-center mb-6",children:[s.jsx(cl,{level:3,children:"轨迹管理"}),s.jsxs(me,{children:[s.jsx(J,{icon:s.jsx(Qe,{}),onClick:T,loading:r,children:"刷新"}),s.jsx(J,{icon:s.jsx(Rr,{}),onClick:Z,children:"导入会话"})]})]}),s.jsx(_r,{columns:F,dataSource:t,rowKey:"id",loading:r,pagination:{pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:E=>`共 ${E} 个会话`}})]})}),s.jsx(At,{title:`预览会话: ${o==null?void 0:o.name}`,open:l,onCancel:()=>c(!1),footer:null,width:800,children:o&&s.jsx("div",{children:s.jsxs(me,{direction:"vertical",className:"w-full",children:[s.jsxs("div",{children:[s.jsx(Ie,{strong:!0,children:"描述: "}),s.jsx(Ie,{children:o.description||"无"})]}),s.jsxs("div",{children:[s.jsx(Ie,{strong:!0,children:"画布尺寸: "}),s.jsxs(Ie,{children:[o.canvasSize.width," × ",o.canvasSize.height]})]}),s.jsxs("div",{children:[s.jsx(Ie,{strong:!0,children:"背景图: "}),s.jsx(Ie,{children:"无"})]}),s.jsxs("div",{children:[s.jsx(Ie,{strong:!0,children:"统计信息: "}),(()=>{const E=w(o);return s.jsxs("div",{children:[s.jsxs(Be,{color:"blue",children:[E.totalStrokes," 笔画"]}),s.jsxs(Be,{color:"green",children:[E.totalPoints," 点"]}),s.jsx(Be,{color:"orange",children:pe(E.duration)}),s.jsxs(Be,{color:"purple",children:[Math.round(E.totalLength)," 像素长度"]})]})})()]})]})})}),s.jsx(At,{title:`生成引导线: ${f==null?void 0:f.name}`,open:h,onCancel:()=>d(!1),onOk:ee,confirmLoading:v,okText:"生成",cancelText:"取消",children:s.jsxs(me,{direction:"vertical",className:"w-full",children:[s.jsxs("div",{children:[s.jsx(Ie,{strong:!0,children:"简化级别:"}),s.jsxs(Lt,{value:I.simplification_level,onChange:E=>b(C=>({...C,simplification_level:E})),className:"w-full mt-2",children:[s.jsx(us,{value:"low",children:"低 (保留更多细节)"}),s.jsx(us,{value:"medium",children:"中 (平衡)"}),s.jsx(us,{value:"high",children:"高 (更简化)"})]})]}),s.jsxs("div",{children:[s.jsx(Ie,{strong:!0,children:"最小笔画长度 (像素):"}),s.jsx($s,{value:I.min_stroke_length,onChange:E=>b(C=>({...C,min_stroke_length:E||20})),min:1,max:200,className:"w-full mt-2"})]}),s.jsxs("div",{children:[s.jsx(Ie,{strong:!0,children:"合并距离 (像素):"}),s.jsx($s,{value:I.merge_distance,onChange:E=>b(C=>({...C,merge_distance:E||50})),min:1,max:200,className:"w-full mt-2"})]}),s.jsx(Ir,{}),s.jsx(Ie,{type:"secondary",children:"这些设置将影响生成的引导线的复杂度和精度。较高的简化级别会产生更简单的引导线， 较大的合并距离会将相近的笔画合并为一条路径。"})]})})]})},{Title:ul,Text:Me}=Te,hl=({guidePaths:e,canvasSize:t,onExport:n})=>{const r=u.useRef(null),[a,o]=u.useState(!1),[i,l]=u.useState(0),[c,h]=u.useState(1),[d,f]=u.useState(!0),[j,I]=u.useState(0),b=u.useRef(null),v=()=>{const w=r.current;if(!w)return;const F=w.getContext("2d");F&&F.clearRect(0,0,w.width,w.height)},g=()=>{const w=r.current;if(!w)return;const F=w.getContext("2d");F&&(v(),d?e.forEach((E,C)=>{T(F,E,C<=j)}):j<e.length&&T(F,e[j],!0))},T=(w,F,E)=>{if(!(F.points.length<2)){w.strokeStyle=E?"#FFD700":"#E5E7EB",w.lineWidth=E?3:2,w.lineCap="round",w.lineJoin="round",E?w.setLineDash([10,5]):w.setLineDash([5,5]),w.beginPath(),w.moveTo(F.points[0].x,F.points[0].y);for(let C=1;C<F.points.length;C++)w.lineTo(F.points[C].x,F.points[C].y);w.stroke(),w.setLineDash([])}},_=()=>{const w=r.current;if(!w)return;const F=w.getContext("2d");F&&(v(),d?e.forEach((E,C)=>{C<j?T(F,E,!0):C===j?P(F,E,i):T(F,E,!1)}):j<e.length&&P(F,e[j],i))},P=(w,F,E)=>{if(F.points.length<2)return;const C=F.points.length,Y=Math.floor(C*E);if(w.strokeStyle="#FFD700",w.lineWidth=3,w.lineCap="round",w.lineJoin="round",w.setLineDash([10,5]),w.beginPath(),Y>0){w.moveTo(F.points[0].x,F.points[0].y);for(let V=1;V<Y;V++)w.lineTo(F.points[V].x,F.points[V].y);if(Y<C){const V=F.points[Y-1],te=F.points[Y],de=C*E-Y,be=V.x+(te.x-V.x)*de,ie=V.y+(te.y-V.y)*de;w.lineTo(be,ie)}}w.stroke(),w.setLineDash([]),E>.1&&Y>0&&W(w,F,E)},W=(w,F,E)=>{const C=F.points.length,Y=Math.floor(C*E);let V,te,de,be;if(Y<C){const $=F.points[Y-1],G=F.points[Y],X=C*E-Y;V=$.x+(G.x-$.x)*X,te=$.y+(G.y-$.y)*X,de=G.x-$.x,be=G.y-$.y}else{V=F.points[C-1].x,te=F.points[C-1].y,de=0,be=0;const $=Math.min(5,C-1);for(let G=1;G<=$;G++){const X=F.points[C-G],H=F.points[C-G-1];de+=X.x-H.x,be+=X.y-H.y}de/=$,be/=$}const ie=Math.sqrt(de*de+be*be);if(ie<.1)return;de/=ie,be/=ie;const S=Math.atan2(be,de),N=15;w.fillStyle="#FFD700",w.strokeStyle="#FFD700",w.lineWidth=2,w.beginPath(),w.moveTo(V,te),w.lineTo(V-N*Math.cos(S-Math.PI/6),te-N*Math.sin(S-Math.PI/6)),w.lineTo(V-N*Math.cos(S+Math.PI/6),te-N*Math.sin(S+Math.PI/6)),w.closePath(),w.fill(),w.stroke()},Z=()=>{a&&(l(w=>{const F=w+.01*c;return F>=1?d&&j<e.length-1?(I(E=>E+1),0):(o(!1),1):F}),b.current=requestAnimationFrame(Z))},le=()=>{a?(o(!1),b.current&&cancelAnimationFrame(b.current)):o(!0)},ee=()=>{o(!1),l(0),I(0),b.current&&cancelAnimationFrame(b.current)},pe=()=>{if(n)n(e);else{const w=JSON.stringify({guidePaths:e,canvasSize:t,exportedAt:new Date().toISOString()},null,2),F=new Blob([w],{type:"application/json"}),E=URL.createObjectURL(F),C=document.createElement("a");C.href=E,C.download=`guide_paths_${Date.now()}.json`,C.click(),URL.revokeObjectURL(E),K.success("引导线数据已导出")}};return u.useEffect(()=>(a&&(b.current=requestAnimationFrame(Z)),()=>{b.current&&cancelAnimationFrame(b.current)}),[a,c,j,d]),u.useEffect(()=>{a?_():g()},[e,i,j,d,a]),e.length===0?s.jsx(ce,{children:s.jsx("div",{className:"text-center py-8",children:s.jsx(Me,{type:"secondary",children:"暂无引导线数据"})})}):s.jsx("div",{className:"guide-preview",children:s.jsx(ae.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ce,{children:[s.jsxs("div",{className:"flex justify-between items-center mb-4",children:[s.jsx(ul,{level:4,children:"引导线预览"}),s.jsxs(me,{children:[s.jsx(J,{icon:a?s.jsx(vn,{}):s.jsx(De,{}),onClick:le,type:"primary",children:a?"暂停":"播放"}),s.jsx(J,{icon:s.jsx(Qe,{}),onClick:ee,children:"重置"}),s.jsx(J,{icon:s.jsx(ks,{}),onClick:pe,children:"导出"})]})]}),s.jsx("div",{className:"mb-4 p-4 bg-gray-50 rounded-lg",children:s.jsxs(me,{direction:"vertical",className:"w-full",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(Me,{children:"动画速度:"}),s.jsx("div",{className:"w-48",children:s.jsx(xn,{min:.1,max:3,step:.1,value:c,onChange:h,disabled:a})})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(Me,{children:"显示所有路径:"}),s.jsx(Ar,{checked:d,onChange:f,disabled:a})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(Me,{children:"路径信息:"}),s.jsxs(Me,{type:"secondary",children:["共 ",e.length," 条路径，当前第 ",j+1," 条"]})]})]})}),s.jsx("div",{className:"flex justify-center",children:s.jsx("canvas",{ref:r,width:t.width,height:t.height,className:"border-2 border-gray-300 rounded-lg bg-white",style:{maxWidth:"100%",maxHeight:"600px"}})}),s.jsx("div",{className:"mt-4 text-center",children:s.jsxs(me,{children:[s.jsxs(Me,{type:"secondary",children:["画布尺寸: ",t.width," × ",t.height]}),s.jsxs(Me,{type:"secondary",children:["路径数量: ",e.length]}),s.jsxs(Me,{type:"secondary",children:["总点数: ",e.reduce((w,F)=>w+F.points.length,0)]})]})})]})})})},ml=()=>{const[e,t]=u.useState("recorder"),[n,r]=u.useState([]),[a,o]=u.useState({width:800,height:600}),[i,l]=u.useState(),c=(h,d,f)=>{r(h),o(d),l(f),t("preview")};return s.jsx("div",{className:"trace-recorder-page min-h-screen",style:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)"},children:s.jsx("div",{className:"container mx-auto px-4 py-6",children:s.jsx(ae.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsx(ce,{className:"shadow-lg",children:s.jsx(Or,{activeKey:e,onChange:t,size:"large",className:"trace-tabs",items:[{key:"recorder",label:"📝 轨迹记录",children:s.jsx(ll,{})},{key:"manager",label:"📁 轨迹管理",children:s.jsx(dl,{onGenerateGuide:c})},{key:"preview",label:`🎯 引导线预览 ${n.length>0?`(${n.length})`:""}`,children:s.jsx(hl,{guidePaths:n,canvasSize:a,backgroundImage:i})}]})})})})})},{Title:hn,Text:mn}=Te,fl=()=>{const e=Fe(),t=[{id:"L1-1",name:"第一关第一阶段",type:"L1"},{id:"L1-2",name:"第一关第二阶段",type:"L1"},{id:"L1-3",name:"第一关第三阶段",type:"L1"},{id:"L1-4",name:"第一关第四阶段",type:"L1"},{id:"L1-5",name:"第一关第五阶段",type:"L1"},{id:"L2-1",name:"第二关第一阶段 (统一背景)",type:"other"},{id:"L3-1",name:"第三关第一阶段 (统一背景)",type:"other"},{id:"L4-1",name:"第四关第一阶段 (统一背景)",type:"other"}],n=r=>{const[a,o]=r.split("-");e(`/game/${a}/${o}`)};return s.jsx("div",{className:"min-h-screen pt-8",style:{background:"transparent"},children:s.jsxs("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[s.jsxs("div",{className:"text-center mb-8",children:[s.jsx(hn,{level:1,className:"text-white",style:{textShadow:"2px 2px 4px rgba(0,0,0,0.8)"},children:"背景图片测试"}),s.jsx(mn,{className:"text-white text-lg",style:{textShadow:"1px 1px 2px rgba(0,0,0,0.8)"},children:"点击下面的按钮测试不同关卡的背景图片切换"})]}),s.jsx("div",{className:"text-center",children:s.jsxs(me,{direction:"vertical",size:"large",children:[s.jsx(me,{wrap:!0,size:"middle",children:t.map(r=>s.jsx(J,{type:r.type==="L1"?"primary":"default",size:"large",onClick:()=>n(r.id),style:{minWidth:"200px",height:"50px",fontSize:"16px",backgroundColor:r.type==="L1"?"rgba(139, 92, 246, 0.9)":"rgba(34, 197, 94, 0.9)",borderColor:r.type==="L1"?"rgba(139, 92, 246, 0.9)":"rgba(34, 197, 94, 0.9)",color:"white",backdropFilter:"blur(10px)"},children:r.name},r.id))}),s.jsx(J,{type:"default",size:"large",onClick:()=>e("/"),style:{minWidth:"200px",height:"50px",fontSize:"16px",backgroundColor:"rgba(255, 255, 255, 0.9)",backdropFilter:"blur(10px)"},children:"返回首页"})]})}),s.jsx("div",{className:"mt-12 text-center",children:s.jsxs("div",{className:"inline-block p-6 rounded-lg",style:{backgroundColor:"rgba(255, 255, 255, 0.9)",backdropFilter:"blur(10px)",maxWidth:"600px"},children:[s.jsx(hn,{level:3,children:"测试说明"}),s.jsxs(mn,{children:[s.jsx("strong",{children:"紫色按钮 (L1关卡)"}),"：使用各自目录下的background.jpg文件作为背景图片。",s.jsx("br",{}),"例如：L1-2关卡显示 /L1-2/background.jpg 作为背景。",s.jsx("br",{}),s.jsx("strong",{children:"绿色按钮 (其他关卡)"}),"：统一使用 /background.jpg 作为背景图片。",s.jsx("br",{}),s.jsx("strong",{children:"其他页面"}),"：轨迹记录、首页等非游戏页面也使用统一背景 /background.jpg。"]})]})})]})})};class pl extends u.Component{constructor(n){super(n);vt(this,"handleReload",()=>{window.location.reload()});vt(this,"handleGoHome",()=>{window.location.href="/"});this.state={hasError:!1}}static getDerivedStateFromError(n){return{hasError:!0,error:n}}componentDidCatch(n,r){console.error("ErrorBoundary caught an error:",n,r),this.setState({error:n,errorInfo:r})}render(){return this.state.hasError?s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:s.jsx("div",{className:"max-w-2xl mx-auto p-8",children:s.jsx($r,{status:"error",title:"哎呀，出现了一些问题",subTitle:"不用担心，这不是您的错误。我们的技术团队会尽快解决这个问题。",extra:[s.jsx(J,{type:"primary",icon:s.jsx(Qe,{}),onClick:this.handleReload,size:"large",children:"重新加载"},"reload"),s.jsx(J,{icon:s.jsx(wn,{}),onClick:this.handleGoHome,size:"large",children:"返回首页"},"home")],children:s.jsxs("div",{className:"text-center mt-8",children:[s.jsx("p",{className:"text-gray-600 mb-4",children:"如果问题持续存在，请联系我们的客服团队："}),s.jsxs("p",{className:"text-gray-600",children:["📧 <EMAIL>",s.jsx("br",{}),"📞 400-123-4567"]}),s.jsx("div",{className:"mt-6 p-4 bg-orange-50 rounded-lg",children:s.jsx("p",{className:"text-orange-600 font-medium",children:"💝 记忆画笔团队始终为您提供温暖的技术支持"})})]})})})}):this.props.children}}const gl=({visible:e,onClose:t,onEnterFullscreen:n})=>{const r=()=>{n(),t()};return s.jsx(At,{open:e,onCancel:t,footer:null,centered:!0,width:500,closable:!1,maskClosable:!1,className:"fullscreen-prompt-modal",children:s.jsxs("div",{className:"text-center p-6",children:[s.jsxs("div",{className:"mb-6",children:[s.jsx("div",{className:"w-20 h-20 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(ms,{className:"text-3xl text-white"})}),s.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-3",children:"获得最佳体验"}),s.jsxs("p",{className:"text-lg text-gray-600 leading-relaxed",children:["为了获得最佳的绘画体验，建议您使用全屏模式。",s.jsx("br",{}),"全屏模式可以让您专注于创作，减少干扰。"]})]}),s.jsxs(me,{size:"large",className:"w-full justify-center",children:[s.jsx(J,{type:"primary",size:"large",icon:s.jsx(ms,{}),onClick:r,className:"h-12 px-8 text-lg bg-gradient-to-r from-purple-500 to-purple-600 border-0",children:"进入全屏"}),s.jsx(J,{size:"large",icon:s.jsx(Sn,{}),onClick:t,className:"h-12 px-8 text-lg",children:"稍后再说"})]}),s.jsx("div",{className:"mt-6 text-sm text-gray-500",children:s.jsx("p",{children:"您也可以随时通过右上角的全屏按钮切换全屏模式"})})]})})},xl=()=>{const e=Fe(),t=Ut(),[n,r]=u.useState(!1),[a,o]=u.useState(!!document.fullscreenElement);Ue.useEffect(()=>{const c=()=>{o(!!document.fullscreenElement)};return document.addEventListener("fullscreenchange",c),()=>{document.removeEventListener("fullscreenchange",c)}},[]);const i=async()=>{try{document.fullscreenElement?await document.exitFullscreen():await document.documentElement.requestFullscreen()}catch(c){console.error("全屏切换失败:",c)}},l=[{key:"home",icon:s.jsx(wn,{}),label:"首页",onClick:()=>{e("/"),r(!1)}},{key:"game",icon:s.jsx(De,{}),label:"开始绘画",onClick:()=>{e("/game"),r(!1)}},{key:"gallery",icon:s.jsx(Ke,{}),label:"作品画廊",onClick:()=>{e("/gallery"),r(!1)}},{key:"leaderboard",icon:s.jsx(_t,{}),label:"排行榜",onClick:()=>{e("/leaderboard"),r(!1)}},{key:"trace-recorder",icon:s.jsx(Dr,{}),label:"轨迹记录",onClick:()=>{e("/trace-recorder"),r(!1)}},{key:"settings",icon:s.jsx(Es,{}),label:"设置",onClick:()=>{e("/settings"),r(!1)}},{type:"divider"},{key:"fullscreen",icon:a?s.jsx(Fr,{}):s.jsx(ms,{}),label:a?"退出全屏":"进入全屏",onClick:()=>{i(),r(!1)}}];return s.jsxs("div",{className:"fixed top-6 right-6 z-50",children:[s.jsx(Br,{children:n&&s.jsx(ae.div,{initial:{opacity:0,scale:.8,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.8,y:20},transition:{duration:.2},className:"mb-4",children:s.jsx("div",{className:"bg-white rounded-2xl shadow-2xl border border-gray-100 overflow-hidden",children:s.jsx("div",{className:"p-2",children:l.map((c,h)=>{if(c.type==="divider")return s.jsx("div",{className:"h-px bg-gray-200 my-2"},h);const d=c.key==="home"&&t.pathname==="/"||c.key==="game"&&t.pathname.startsWith("/game")||c.key==="gallery"&&t.pathname==="/gallery"||c.key==="leaderboard"&&t.pathname==="/leaderboard"||c.key==="trace-recorder"&&t.pathname==="/trace-recorder"||c.key==="settings"&&t.pathname==="/settings";return s.jsx(J,{type:d?"primary":"text",icon:c.icon,onClick:c.onClick,className:"w-full justify-start mb-1 h-12 text-left",size:"large",children:c.label},c.key)})})})})}),s.jsx(ae.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(J,{type:"primary",shape:"circle",size:"large",icon:n?s.jsx(Sn,{}):s.jsx(Lr,{}),onClick:()=>r(!n),className:"w-14 h-14 bg-gradient-to-br from-purple-500 to-purple-600 border-0 shadow-2xl hover:shadow-3xl",style:{fontSize:"18px"}})})]})},{Content:yl}=Ct,{Title:bl,Paragraph:vl}=Te,jl=()=>{const[e,t]=Ue.useState(!1),n=Fe();co(),Ue.useEffect(()=>{if(!localStorage.getItem("memorybrush-fullscreen-prompt-seen")&&!document.fullscreenElement){const i=setTimeout(()=>{t(!0)},2e3);return()=>clearTimeout(i)}},[]);const r=async()=>{try{await document.documentElement.requestFullscreen(),localStorage.setItem("memorybrush-fullscreen-prompt-seen","true")}catch(o){console.error("进入全屏失败:",o)}},a=()=>{t(!1),localStorage.setItem("memorybrush-fullscreen-prompt-seen","true")};return s.jsxs(pl,{children:[s.jsx(Ct,{className:"min-h-screen",style:{background:"transparent"},children:s.jsx(Ct,{children:s.jsx(Ct,{className:"transition-all duration-300",children:s.jsx(yl,{className:"min-h-screen",children:s.jsx(ae.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:s.jsxs(Ma,{children:[s.jsx(Ae,{path:"/",element:s.jsx(uo,{})}),s.jsx(Ae,{path:"/game",element:s.jsx(ds,{})}),s.jsx(Ae,{path:"/game/:level",element:s.jsx(ds,{})}),s.jsx(Ae,{path:"/game/:level/:stage",element:s.jsx(ds,{})}),s.jsx(Ae,{path:"/profile",element:s.jsx(Xi,{})}),s.jsx(Ae,{path:"/settings",element:s.jsx(Zi,{})}),s.jsx(Ae,{path:"/gallery",element:s.jsx(sl,{})}),s.jsx(Ae,{path:"/leaderboard",element:s.jsx(al,{})}),s.jsx(Ae,{path:"/trace-recorder",element:s.jsx(ml,{})}),s.jsx(Ae,{path:"/background-test",element:s.jsx(fl,{})}),s.jsx(Ae,{path:"*",element:s.jsx("div",{className:"min-h-screen pt-8",style:{background:"transparent"},children:s.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:s.jsx(ae.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ce,{className:"text-center p-12 shadow-soft",children:[s.jsxs("div",{className:"mb-8",children:[s.jsx("div",{className:"w-24 h-24 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx("span",{className:"text-4xl text-white",children:"🔍"})}),s.jsx(bl,{level:1,className:"text-gray-600 mb-4",children:"页面建设中"}),s.jsxs(vl,{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:["您访问的页面正在建设中，敬请期待！",s.jsx("br",{}),"我们正在努力为您提供更好的功能体验。"]})]}),s.jsx(me,{size:"large",children:s.jsx(J,{size:"large",icon:s.jsx(qe,{}),onClick:()=>n("/"),className:"h-12 px-8 text-lg",children:"返回首页"})})]})})})})})]})})})})})}),s.jsx(xl,{}),s.jsx(gl,{visible:e,onClose:a,onEnterFullscreen:r})]})},wl={token:{colorPrimary:"#8b5cf6",colorSuccess:"#10b981",colorWarning:"#f59e0b",colorError:"#ef4444",borderRadius:8,fontSize:16,fontFamily:"Inter, system-ui, sans-serif"},components:{Button:{fontSize:18,paddingContentHorizontal:24,paddingContentVertical:12},Input:{fontSize:16,paddingBlock:12},Card:{borderRadius:12}}};fs.createRoot(document.getElementById("root")).render(s.jsx(Ue.StrictMode,{children:s.jsx(za,{children:s.jsx(Mr,{locale:lo,theme:wl,componentSize:"large",children:s.jsx(jl,{})})})}));
//# sourceMappingURL=index-DwDGAGtD.js.map
