var vr=Object.defineProperty;var wr=(e,t,n)=>t in e?vr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var bt=(e,t,n)=>wr(e,typeof t!="symbol"?t+"":t,n);import{r as h,c as Sr,b as Nr,g as Er,R as Be}from"./vendor-BTWMFwqw.js";import{T as Pe,C as ce,R as Oe,a as Xe,b as Rt,P as gn,c as Es,d as rt,e as xn,f as _t,g as ks,h as kr,i as Cr,j as Pr,k as ot,B as H,l as yn,m as Ke,n as Fe,s as X,S as me,o as bn,p as Tr,q as jn,r as vn,t as at,u as As,I as Os,v as ze,w as Rr,x as Ot,y as $s,z as fs,A as wn,D as _r,E as Cs,F as Sn,M as Lt,G as Lr,H as Ir,J as Ds,K as Ar,L as jt,N as Or,O as $r,Q as Dr,U as Nn,V as ps,W as En,X as Fr,Y as Mr,Z as Ur,_ as kt,$ as zr}from"./antd-SiMkeERs.js";import{m as re,A as Br}from"./animation-BAuEqiwG.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))r(a);new MutationObserver(a=>{for(const o of a)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(a){const o={};return a.integrity&&(o.integrity=a.integrity),a.referrerPolicy&&(o.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?o.credentials="include":a.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(a){if(a.ep)return;a.ep=!0;const o=n(a);fetch(a.href,o)}})();var kn={exports:{}},$t={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qr=h,Hr=Symbol.for("react.element"),Wr=Symbol.for("react.fragment"),Jr=Object.prototype.hasOwnProperty,Gr=qr.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Vr={key:!0,ref:!0,__self:!0,__source:!0};function Cn(e,t,n){var r,a={},o=null,i=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)Jr.call(t,r)&&!Vr.hasOwnProperty(r)&&(a[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)a[r]===void 0&&(a[r]=t[r]);return{$$typeof:Hr,type:e,key:o,ref:i,props:a,_owner:Gr.current}}$t.Fragment=Wr;$t.jsx=Cn;$t.jsxs=Cn;kn.exports=$t;var s=kn.exports,gs={},Fs=Sr;gs.createRoot=Fs.createRoot,gs.hydrateRoot=Fs.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function it(){return it=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},it.apply(this,arguments)}var qe;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(qe||(qe={}));const Ms="popstate";function Yr(e){e===void 0&&(e={});function t(r,a){let{pathname:o,search:i,hash:c}=r.location;return xs("",{pathname:o,search:i,hash:c},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function n(r,a){return typeof a=="string"?a:Tn(a)}return Kr(t,n,null,e)}function xe(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Pn(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Xr(){return Math.random().toString(36).substr(2,8)}function Us(e,t){return{usr:e.state,key:e.key,idx:t}}function xs(e,t,n,r){return n===void 0&&(n=null),it({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Qe(t):t,{state:n,key:t&&t.key||r||Xr()})}function Tn(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function Qe(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function Kr(e,t,n,r){r===void 0&&(r={});let{window:a=document.defaultView,v5Compat:o=!1}=r,i=a.history,c=qe.Pop,l=null,u=d();u==null&&(u=0,i.replaceState(it({},i.state,{idx:u}),""));function d(){return(i.state||{idx:null}).idx}function f(){c=qe.Pop;let g=d(),P=g==null?null:g-u;u=g,l&&l({action:c,location:v.location,delta:P})}function w(g,P){c=qe.Push;let R=xs(v.location,g,P);u=d()+1;let C=Us(R,u),q=v.createHref(R);try{i.pushState(C,"",q)}catch(Q){if(Q instanceof DOMException&&Q.name==="DataCloneError")throw Q;a.location.assign(q)}o&&l&&l({action:c,location:v.location,delta:1})}function I(g,P){c=qe.Replace;let R=xs(v.location,g,P);u=d();let C=Us(R,u),q=v.createHref(R);i.replaceState(C,"",q),o&&l&&l({action:c,location:v.location,delta:0})}function j(g){let P=a.location.origin!=="null"?a.location.origin:a.location.href,R=typeof g=="string"?g:Tn(g);return R=R.replace(/ $/,"%20"),xe(P,"No window.location.(origin|href) available to create URL for href: "+R),new URL(R,P)}let v={get action(){return c},get location(){return e(a,i)},listen(g){if(l)throw new Error("A history only accepts one active listener");return a.addEventListener(Ms,f),l=g,()=>{a.removeEventListener(Ms,f),l=null}},createHref(g){return t(a,g)},createURL:j,encodeLocation(g){let P=j(g);return{pathname:P.pathname,search:P.search,hash:P.hash}},push:w,replace:I,go(g){return i.go(g)}};return v}var zs;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(zs||(zs={}));function Qr(e,t,n){return n===void 0&&(n="/"),Zr(e,t,n)}function Zr(e,t,n,r){let a=typeof t=="string"?Qe(t):t,o=Ln(a.pathname||"/",n);if(o==null)return null;let i=Rn(e);ea(i);let c=null;for(let l=0;c==null&&l<i.length;++l){let u=ha(o);c=ca(i[l],u)}return c}function Rn(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let a=(o,i,c)=>{let l={relativePath:c===void 0?o.path||"":c,caseSensitive:o.caseSensitive===!0,childrenIndex:i,route:o};l.relativePath.startsWith("/")&&(xe(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=We([r,l.relativePath]),d=n.concat(l);o.children&&o.children.length>0&&(xe(o.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),Rn(o.children,t,d,u)),!(o.path==null&&!o.index)&&t.push({path:u,score:ia(u,o.index),routesMeta:d})};return e.forEach((o,i)=>{var c;if(o.path===""||!((c=o.path)!=null&&c.includes("?")))a(o,i);else for(let l of _n(o.path))a(o,i,l)}),t}function _n(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,a=n.endsWith("?"),o=n.replace(/\?$/,"");if(r.length===0)return a?[o,""]:[o];let i=_n(r.join("/")),c=[];return c.push(...i.map(l=>l===""?o:[o,l].join("/"))),a&&c.push(...i),c.map(l=>e.startsWith("/")&&l===""?"/":l)}function ea(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:la(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const ta=/^:[\w-]+$/,sa=3,na=2,ra=1,aa=10,oa=-2,Bs=e=>e==="*";function ia(e,t){let n=e.split("/"),r=n.length;return n.some(Bs)&&(r+=oa),t&&(r+=na),n.filter(a=>!Bs(a)).reduce((a,o)=>a+(ta.test(o)?sa:o===""?ra:aa),r)}function la(e,t){return e.length===t.length&&e.slice(0,-1).every((r,a)=>r===t[a])?e[e.length-1]-t[t.length-1]:0}function ca(e,t,n){let{routesMeta:r}=e,a={},o="/",i=[];for(let c=0;c<r.length;++c){let l=r[c],u=c===r.length-1,d=o==="/"?t:t.slice(o.length)||"/",f=da({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},d),w=l.route;if(!f)return null;Object.assign(a,f.params),i.push({params:a,pathname:We([o,f.pathname]),pathnameBase:ya(We([o,f.pathnameBase])),route:w}),f.pathnameBase!=="/"&&(o=We([o,f.pathnameBase]))}return i}function da(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=ua(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let o=a[0],i=o.replace(/(.)\/+$/,"$1"),c=a.slice(1);return{params:r.reduce((u,d,f)=>{let{paramName:w,isOptional:I}=d;if(w==="*"){let v=c[f]||"";i=o.slice(0,o.length-v.length).replace(/(.)\/+$/,"$1")}const j=c[f];return I&&!j?u[w]=void 0:u[w]=(j||"").replace(/%2F/g,"/"),u},{}),pathname:o,pathnameBase:i,pattern:e}}function ua(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Pn(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,c,l)=>(r.push({paramName:c,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function ha(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Pn(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Ln(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function ma(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:a=""}=typeof e=="string"?Qe(e):e;return{pathname:n?n.startsWith("/")?n:fa(n,t):t,search:ba(r),hash:ja(a)}}function fa(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?n.length>1&&n.pop():a!=="."&&n.push(a)}),n.length>1?n.join("/"):"/"}function is(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function pa(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function ga(e,t){let n=pa(e);return t?n.map((r,a)=>a===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function xa(e,t,n,r){r===void 0&&(r=!1);let a;typeof e=="string"?a=Qe(e):(a=it({},e),xe(!a.pathname||!a.pathname.includes("?"),is("?","pathname","search",a)),xe(!a.pathname||!a.pathname.includes("#"),is("#","pathname","hash",a)),xe(!a.search||!a.search.includes("#"),is("#","search","hash",a)));let o=e===""||a.pathname==="",i=o?"/":a.pathname,c;if(i==null)c=n;else{let f=t.length-1;if(!r&&i.startsWith("..")){let w=i.split("/");for(;w[0]==="..";)w.shift(),f-=1;a.pathname=w.join("/")}c=f>=0?t[f]:"/"}let l=ma(a,c),u=i&&i!=="/"&&i.endsWith("/"),d=(o||i===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(u||d)&&(l.pathname+="/"),l}const We=e=>e.join("/").replace(/\/\/+/g,"/"),ya=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),ba=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,ja=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function va(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const In=["post","put","patch","delete"];new Set(In);const wa=["get",...In];new Set(wa);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function lt(){return lt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},lt.apply(this,arguments)}const Ps=h.createContext(null),Sa=h.createContext(null),Dt=h.createContext(null),Ft=h.createContext(null),Ve=h.createContext({outlet:null,matches:[],isDataRoute:!1}),An=h.createContext(null);function Mt(){return h.useContext(Ft)!=null}function Ut(){return Mt()||xe(!1),h.useContext(Ft).location}function On(e){h.useContext(Dt).static||h.useLayoutEffect(e)}function Me(){let{isDataRoute:e}=h.useContext(Ve);return e?Da():Na()}function Na(){Mt()||xe(!1);let e=h.useContext(Ps),{basename:t,future:n,navigator:r}=h.useContext(Dt),{matches:a}=h.useContext(Ve),{pathname:o}=Ut(),i=JSON.stringify(ga(a,n.v7_relativeSplatPath)),c=h.useRef(!1);return On(()=>{c.current=!0}),h.useCallback(function(u,d){if(d===void 0&&(d={}),!c.current)return;if(typeof u=="number"){r.go(u);return}let f=xa(u,JSON.parse(i),o,d.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:We([t,f.pathname])),(d.replace?r.replace:r.push)(f,d.state,d)},[t,r,i,o,e])}function Ea(){let{matches:e}=h.useContext(Ve),t=e[e.length-1];return t?t.params:{}}function ka(e,t){return Ca(e,t)}function Ca(e,t,n,r){Mt()||xe(!1);let{navigator:a}=h.useContext(Dt),{matches:o}=h.useContext(Ve),i=o[o.length-1],c=i?i.params:{};i&&i.pathname;let l=i?i.pathnameBase:"/";i&&i.route;let u=Ut(),d;if(t){var f;let g=typeof t=="string"?Qe(t):t;l==="/"||(f=g.pathname)!=null&&f.startsWith(l)||xe(!1),d=g}else d=u;let w=d.pathname||"/",I=w;if(l!=="/"){let g=l.replace(/^\//,"").split("/");I="/"+w.replace(/^\//,"").split("/").slice(g.length).join("/")}let j=Qr(e,{pathname:I}),v=La(j&&j.map(g=>Object.assign({},g,{params:Object.assign({},c,g.params),pathname:We([l,a.encodeLocation?a.encodeLocation(g.pathname).pathname:g.pathname]),pathnameBase:g.pathnameBase==="/"?l:We([l,a.encodeLocation?a.encodeLocation(g.pathnameBase).pathname:g.pathnameBase])})),o,n,r);return t&&v?h.createElement(Ft.Provider,{value:{location:lt({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:qe.Pop}},v):v}function Pa(){let e=$a(),t=va(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return h.createElement(h.Fragment,null,h.createElement("h2",null,"Unexpected Application Error!"),h.createElement("h3",{style:{fontStyle:"italic"}},t),n?h.createElement("pre",{style:a},n):null,null)}const Ta=h.createElement(Pa,null);class Ra extends h.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?h.createElement(Ve.Provider,{value:this.props.routeContext},h.createElement(An.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function _a(e){let{routeContext:t,match:n,children:r}=e,a=h.useContext(Ps);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),h.createElement(Ve.Provider,{value:t},r)}function La(e,t,n,r){var a;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var o;if(!n)return null;if(n.errors)e=n.matches;else if((o=r)!=null&&o.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let i=e,c=(a=n)==null?void 0:a.errors;if(c!=null){let d=i.findIndex(f=>f.route.id&&(c==null?void 0:c[f.route.id])!==void 0);d>=0||xe(!1),i=i.slice(0,Math.min(i.length,d+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let d=0;d<i.length;d++){let f=i[d];if((f.route.HydrateFallback||f.route.hydrateFallbackElement)&&(u=d),f.route.id){let{loaderData:w,errors:I}=n,j=f.route.loader&&w[f.route.id]===void 0&&(!I||I[f.route.id]===void 0);if(f.route.lazy||j){l=!0,u>=0?i=i.slice(0,u+1):i=[i[0]];break}}}return i.reduceRight((d,f,w)=>{let I,j=!1,v=null,g=null;n&&(I=c&&f.route.id?c[f.route.id]:void 0,v=f.route.errorElement||Ta,l&&(u<0&&w===0?(Fa("route-fallback"),j=!0,g=null):u===w&&(j=!0,g=f.route.hydrateFallbackElement||null)));let P=t.concat(i.slice(0,w+1)),R=()=>{let C;return I?C=v:j?C=g:f.route.Component?C=h.createElement(f.route.Component,null):f.route.element?C=f.route.element:C=d,h.createElement(_a,{match:f,routeContext:{outlet:d,matches:P,isDataRoute:n!=null},children:C})};return n&&(f.route.ErrorBoundary||f.route.errorElement||w===0)?h.createElement(Ra,{location:n.location,revalidation:n.revalidation,component:v,error:I,children:R(),routeContext:{outlet:null,matches:P,isDataRoute:!0}}):R()},null)}var $n=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}($n||{}),Dn=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Dn||{});function Ia(e){let t=h.useContext(Ps);return t||xe(!1),t}function Aa(e){let t=h.useContext(Sa);return t||xe(!1),t}function Oa(e){let t=h.useContext(Ve);return t||xe(!1),t}function Fn(e){let t=Oa(),n=t.matches[t.matches.length-1];return n.route.id||xe(!1),n.route.id}function $a(){var e;let t=h.useContext(An),n=Aa(),r=Fn();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Da(){let{router:e}=Ia($n.UseNavigateStable),t=Fn(Dn.UseNavigateStable),n=h.useRef(!1);return On(()=>{n.current=!0}),h.useCallback(function(a,o){o===void 0&&(o={}),n.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,lt({fromRouteId:t},o)))},[e,t])}const qs={};function Fa(e,t,n){qs[e]||(qs[e]=!0)}function Ma(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function Le(e){xe(!1)}function Ua(e){let{basename:t="/",children:n=null,location:r,navigationType:a=qe.Pop,navigator:o,static:i=!1,future:c}=e;Mt()&&xe(!1);let l=t.replace(/^\/*/,"/"),u=h.useMemo(()=>({basename:l,navigator:o,static:i,future:lt({v7_relativeSplatPath:!1},c)}),[l,c,o,i]);typeof r=="string"&&(r=Qe(r));let{pathname:d="/",search:f="",hash:w="",state:I=null,key:j="default"}=r,v=h.useMemo(()=>{let g=Ln(d,l);return g==null?null:{location:{pathname:g,search:f,hash:w,state:I,key:j},navigationType:a}},[l,d,f,w,I,j,a]);return v==null?null:h.createElement(Dt.Provider,{value:u},h.createElement(Ft.Provider,{children:n,value:v}))}function za(e){let{children:t,location:n}=e;return ka(ys(t),n)}new Promise(()=>{});function ys(e,t){t===void 0&&(t=[]);let n=[];return h.Children.forEach(e,(r,a)=>{if(!h.isValidElement(r))return;let o=[...t,a];if(r.type===h.Fragment){n.push.apply(n,ys(r.props.children,o));return}r.type!==Le&&xe(!1),!r.props.index||!r.props.children||xe(!1);let i={id:r.props.id||o.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(i.children=ys(r.props.children,o)),n.push(i)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const Ba="6";try{window.__reactRouterVersion=Ba}catch{}const qa="startTransition",Hs=Nr[qa];function Ha(e){let{basename:t,children:n,future:r,window:a}=e,o=h.useRef();o.current==null&&(o.current=Yr({window:a,v5Compat:!0}));let i=o.current,[c,l]=h.useState({action:i.action,location:i.location}),{v7_startTransition:u}=r||{},d=h.useCallback(f=>{u&&Hs?Hs(()=>l(f)):l(f)},[l,u]);return h.useLayoutEffect(()=>i.listen(d),[i,d]),h.useEffect(()=>Ma(r),[r]),h.createElement(Ua,{basename:t,children:n,location:c.location,navigationType:c.action,navigator:i,future:r})}var Ws;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Ws||(Ws={}));var Js;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Js||(Js={}));var zt={},Mn={exports:{}};(function(e){function t(n){return n&&n.__esModule?n:{default:n}}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})(Mn);var Bt=Mn.exports,qt={};Object.defineProperty(qt,"__esModule",{value:!0});qt.default=void 0;var Wa={items_per_page:"条/页",jump_to:"跳至",jump_to_confirm:"确定",page:"页",prev_page:"上一页",next_page:"下一页",prev_5:"向前 5 页",next_5:"向后 5 页",prev_3:"向前 3 页",next_3:"向后 3 页",page_size:"页码"};qt.default=Wa;var Ht={},dt={},Wt={},Un={exports:{}},zn={exports:{}},Bn={exports:{}},qn={exports:{}};(function(e){function t(n){"@babel/helpers - typeof";return e.exports=t=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(r){return typeof r}:function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})(qn);var Hn=qn.exports,Wn={exports:{}};(function(e){var t=Hn.default;function n(r,a){if(t(r)!="object"||!r)return r;var o=r[Symbol.toPrimitive];if(o!==void 0){var i=o.call(r,a||"default");if(t(i)!="object")return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return(a==="string"?String:Number)(r)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports})(Wn);var Ja=Wn.exports;(function(e){var t=Hn.default,n=Ja;function r(a){var o=n(a,"string");return t(o)=="symbol"?o:o+""}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports})(Bn);var Ga=Bn.exports;(function(e){var t=Ga;function n(r,a,o){return(a=t(a))in r?Object.defineProperty(r,a,{value:o,enumerable:!0,configurable:!0,writable:!0}):r[a]=o,r}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports})(zn);var Va=zn.exports;(function(e){var t=Va;function n(a,o){var i=Object.keys(a);if(Object.getOwnPropertySymbols){var c=Object.getOwnPropertySymbols(a);o&&(c=c.filter(function(l){return Object.getOwnPropertyDescriptor(a,l).enumerable})),i.push.apply(i,c)}return i}function r(a){for(var o=1;o<arguments.length;o++){var i=arguments[o]!=null?arguments[o]:{};o%2?n(Object(i),!0).forEach(function(c){t(a,c,i[c])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(i)):n(Object(i)).forEach(function(c){Object.defineProperty(a,c,Object.getOwnPropertyDescriptor(i,c))})}return a}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports})(Un);var Ya=Un.exports,Jt={};Object.defineProperty(Jt,"__esModule",{value:!0});Jt.commonLocale=void 0;Jt.commonLocale={yearFormat:"YYYY",dayFormat:"D",cellMeridiemFormat:"A",monthBeforeYear:!0};var Xa=Bt.default;Object.defineProperty(Wt,"__esModule",{value:!0});Wt.default=void 0;var Gs=Xa(Ya),Ka=Jt,Qa=(0,Gs.default)((0,Gs.default)({},Ka.commonLocale),{},{locale:"zh_CN",today:"今天",now:"此刻",backToToday:"返回今天",ok:"确定",timeSelect:"选择时间",dateSelect:"选择日期",weekSelect:"选择周",clear:"清除",week:"周",month:"月",year:"年",previousMonth:"上个月 (翻页上键)",nextMonth:"下个月 (翻页下键)",monthSelect:"选择月份",yearSelect:"选择年份",decadeSelect:"选择年代",previousYear:"上一年 (Control键加左方向键)",nextYear:"下一年 (Control键加右方向键)",previousDecade:"上一年代",nextDecade:"下一年代",previousCentury:"上一世纪",nextCentury:"下一世纪",yearFormat:"YYYY年",cellDateFormat:"D",monthBeforeYear:!1});Wt.default=Qa;var ut={};Object.defineProperty(ut,"__esModule",{value:!0});ut.default=void 0;const Za={placeholder:"请选择时间",rangePlaceholder:["开始时间","结束时间"]};ut.default=Za;var Jn=Bt.default;Object.defineProperty(dt,"__esModule",{value:!0});dt.default=void 0;var eo=Jn(Wt),to=Jn(ut);const Gn={lang:Object.assign({placeholder:"请选择日期",yearPlaceholder:"请选择年份",quarterPlaceholder:"请选择季度",monthPlaceholder:"请选择月份",weekPlaceholder:"请选择周",rangePlaceholder:["开始日期","结束日期"],rangeYearPlaceholder:["开始年份","结束年份"],rangeMonthPlaceholder:["开始月份","结束月份"],rangeQuarterPlaceholder:["开始季度","结束季度"],rangeWeekPlaceholder:["开始周","结束周"]},eo.default),timePickerLocale:Object.assign({},to.default)};Gn.lang.ok="确定";dt.default=Gn;var so=Bt.default;Object.defineProperty(Ht,"__esModule",{value:!0});Ht.default=void 0;var no=so(dt);Ht.default=no.default;var Gt=Bt.default;Object.defineProperty(zt,"__esModule",{value:!0});zt.default=void 0;var ro=Gt(qt),ao=Gt(Ht),oo=Gt(dt),io=Gt(ut);const Te="${label}不是一个有效的${type}",lo={locale:"zh-cn",Pagination:ro.default,DatePicker:oo.default,TimePicker:io.default,Calendar:ao.default,global:{placeholder:"请选择",close:"关闭"},Table:{filterTitle:"筛选",filterConfirm:"确定",filterReset:"重置",filterEmptyText:"无筛选项",filterCheckAll:"全选",filterSearchPlaceholder:"在筛选项中搜索",emptyText:"暂无数据",selectAll:"全选当页",selectInvert:"反选当页",selectNone:"清空所有",selectionAll:"全选所有",sortTitle:"排序",expand:"展开行",collapse:"关闭行",triggerDesc:"点击降序",triggerAsc:"点击升序",cancelSort:"取消排序"},Modal:{okText:"确定",cancelText:"取消",justOkText:"知道了"},Tour:{Next:"下一步",Previous:"上一步",Finish:"结束导览"},Popconfirm:{cancelText:"取消",okText:"确定"},Transfer:{titles:["",""],searchPlaceholder:"请输入搜索内容",itemUnit:"项",itemsUnit:"项",remove:"删除",selectCurrent:"全选当页",removeCurrent:"删除当页",selectAll:"全选所有",deselectAll:"取消全选",removeAll:"删除全部",selectInvert:"反选当页"},Upload:{uploading:"文件上传中",removeFile:"删除文件",uploadError:"上传错误",previewFile:"预览文件",downloadFile:"下载文件"},Empty:{description:"暂无数据"},Icon:{icon:"图标"},Text:{edit:"编辑",copy:"复制",copied:"复制成功",expand:"展开",collapse:"收起"},Form:{optional:"（可选）",defaultValidateMessages:{default:"字段验证错误${label}",required:"请输入${label}",enum:"${label}必须是其中一个[${enum}]",whitespace:"${label}不能为空字符",date:{format:"${label}日期格式无效",parse:"${label}不能转换为日期",invalid:"${label}是一个无效日期"},types:{string:Te,method:Te,array:Te,object:Te,number:Te,date:Te,boolean:Te,integer:Te,float:Te,regexp:Te,email:Te,url:Te,hex:Te},string:{len:"${label}须为${len}个字符",min:"${label}最少${min}个字符",max:"${label}最多${max}个字符",range:"${label}须在${min}-${max}字符之间"},number:{len:"${label}必须等于${len}",min:"${label}最小值为${min}",max:"${label}最大值为${max}",range:"${label}须在${min}-${max}之间"},array:{len:"须为${len}个${label}",min:"最少${min}个${label}",max:"最多${max}个${label}",range:"${label}数量须在${min}-${max}之间"},pattern:{mismatch:"${label}与模式不匹配${pattern}"}}},Image:{preview:"预览"},QRCode:{expired:"二维码过期",refresh:"点击刷新",scanned:"已扫描"},ColorPicker:{presetEmpty:"暂无",transparent:"无色",singleColor:"单色",gradientColor:"渐变色"}};zt.default=lo;var co=zt;const uo=Er(co),ho=()=>{const e=Ut();h.useEffect(()=>{const t=e.pathname.split("/").filter(o=>o.length>0);let n="";if(t[0]==="game"&&t[1]==="level-1"&&t.length===2){document.body.style.backgroundImage="",document.documentElement.style.backgroundImage="";return}if(t[0]==="game"&&t.length>=3){const o=t[1],i=t[2];if(i.startsWith("L")&&i.includes("-"))n=i;else if(o.startsWith("level-")&&i.startsWith("stage-")){const c=o.replace("level-",""),l=i.replace("stage-","");n=`L${c}-${l}`}else o.startsWith("L")&&!o.includes("-")?n=`${o}-${i}`:o.startsWith("level-")&&i.startsWith("L")&&(n=i)}const r=o=>{document.body.style.backgroundImage=`url('${o}')`,document.documentElement.style.backgroundImage=`url('${o}')`};return n?(async o=>{if(o.startsWith("L1-")){const i=["jpg","jpeg","png"];for(const c of i)try{const l=`/${o}/background.${c}`,u=new Image;await new Promise((d,f)=>{u.onload=d,u.onerror=f,u.src=l}),r(l),console.log("✅ 成功加载L1关卡背景:",l);return}catch{continue}r("/background.jpg"),console.log("⚠️ L1关卡背景不存在，使用统一背景: /background.jpg")}else r("/background.jpg"),console.log("✅ 使用统一背景:","/background.jpg")})(n):(r("/background.jpg"),console.log("✅ 非游戏页面使用统一背景:","/background.jpg")),()=>{}},[e.pathname])},{Title:vt,Text:je}=Pe,mo=()=>{const e=Me(),t={currentLevel:2,completedStages:5,overallProgress:42,consecutiveDays:5};return s.jsx("div",{className:"min-h-screen",style:{background:"transparent"},children:s.jsxs("div",{className:"max-w-6xl mx-auto px-4 pt-8 pb-12",children:[s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"mb-16",children:s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto",children:[s.jsx(re.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(ce,{className:"bg-gradient-to-br from-orange-400 to-pink-500 border-0 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer h-48",onClick:()=>e("/game"),children:s.jsxs("div",{className:"text-center text-white h-full flex flex-col justify-center",children:[s.jsx(Oe,{className:"text-6xl mb-4"}),s.jsx(vt,{level:2,className:"text-white mb-2",children:"开始绘画"}),s.jsx(je,{className:"text-white text-xl opacity-90",children:"让创意自由流淌"})]})})}),s.jsx(re.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(ce,{className:"bg-white border-2 border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer h-48",onClick:()=>e("/gallery"),children:s.jsxs("div",{className:"text-center h-full flex flex-col justify-center",children:[s.jsx(Xe,{className:"text-6xl text-purple-500 mb-4"}),s.jsx(vt,{level:2,className:"text-gray-700 mb-2",children:"作品画廊"}),s.jsx(je,{className:"text-gray-600 text-xl",children:"欣赏美好作品"})]})})})]})}),s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},className:"mb-16",children:s.jsx(ce,{className:"bg-white shadow-soft border-0 max-w-4xl mx-auto",children:s.jsxs("div",{className:"text-center p-8",children:[s.jsxs(vt,{level:2,className:"text-gray-700 mb-8",children:[s.jsx(Rt,{className:"mr-3 text-yellow-500"}),"您的学习进度"]}),s.jsxs("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-8 mb-8",children:[s.jsxs("div",{className:"flex justify-between items-center mb-6",children:[s.jsx(je,{className:"text-2xl text-gray-700",children:"艺术之旅进度"}),s.jsxs(je,{className:"text-4xl font-bold text-purple-600",children:[t.overallProgress,"%"]})]}),s.jsx(gn,{percent:t.overallProgress,strokeColor:{"0%":"#3b82f6","50%":"#8b5cf6","100%":"#ec4899"},strokeWidth:20,className:"mb-4"}),s.jsx(je,{className:"text-xl text-gray-600",children:"每一步都是成长，每一画都是进步 ✨"})]}),s.jsxs(Es,{gutter:24,children:[s.jsx(rt,{span:8,children:s.jsxs("div",{className:"text-center p-6 bg-gradient-to-br from-orange-100 to-orange-200 rounded-xl",children:[s.jsx("div",{className:"text-5xl mb-3",children:"🎨"}),s.jsx(je,{className:"text-4xl font-bold text-orange-600 block",children:t.currentLevel}),s.jsx(je,{className:"text-xl text-gray-600",children:"当前级别"})]})}),s.jsx(rt,{span:8,children:s.jsxs("div",{className:"text-center p-6 bg-gradient-to-br from-green-100 to-green-200 rounded-xl",children:[s.jsx("div",{className:"text-5xl mb-3",children:"⭐"}),s.jsx(je,{className:"text-4xl font-bold text-green-600 block",children:t.completedStages}),s.jsx(je,{className:"text-xl text-gray-600",children:"完成阶段"})]})}),s.jsx(rt,{span:8,children:s.jsxs("div",{className:"text-center p-6 bg-gradient-to-br from-pink-100 to-pink-200 rounded-xl",children:[s.jsx("div",{className:"text-5xl mb-3",children:"🔥"}),s.jsx(je,{className:"text-4xl font-bold text-pink-600 block",children:t.consecutiveDays}),s.jsx(je,{className:"text-xl text-gray-600",children:"连续天数"})]})})]})]})})}),s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:1},className:"mb-16",children:s.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto",children:[s.jsx(re.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsxs(ce,{className:"text-center cursor-pointer bg-gradient-to-br from-blue-50 to-blue-100 border-0 shadow-soft hover:shadow-lg transition-all duration-300 h-32",onClick:()=>e("/profile"),children:[s.jsx(xn,{className:"text-4xl text-blue-500 mb-2"}),s.jsx(je,{className:"text-lg font-medium text-gray-700",children:"个人档案"})]})}),s.jsx(re.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsxs(ce,{className:"text-center cursor-pointer bg-gradient-to-br from-yellow-50 to-yellow-100 border-0 shadow-soft hover:shadow-lg transition-all duration-300 h-32",onClick:()=>e("/leaderboard"),children:[s.jsx(Rt,{className:"text-4xl text-yellow-500 mb-2"}),s.jsx(je,{className:"text-lg font-medium text-gray-700",children:"排行榜"})]})}),s.jsx(re.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsxs(ce,{className:"text-center cursor-pointer bg-gradient-to-br from-purple-50 to-purple-100 border-0 shadow-soft hover:shadow-lg transition-all duration-300 h-32",onClick:()=>e("/achievements"),children:[s.jsx(_t,{className:"text-4xl text-purple-500 mb-2"}),s.jsx(je,{className:"text-lg font-medium text-gray-700",children:"成就"})]})}),s.jsx(re.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsxs(ce,{className:"text-center cursor-pointer bg-gradient-to-br from-green-50 to-green-100 border-0 shadow-soft hover:shadow-lg transition-all duration-300 h-32",onClick:()=>e("/settings"),children:[s.jsx(ks,{className:"text-4xl text-green-500 mb-2"}),s.jsx(je,{className:"text-lg font-medium text-gray-700",children:"设置"})]})})]})}),s.jsx(re.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:1,delay:1.2},className:"text-center mt-16 py-12",children:s.jsx("div",{className:"max-w-3xl mx-auto",children:s.jsxs("div",{className:"bg-gradient-to-r from-orange-100 via-pink-100 to-purple-100 rounded-2xl p-8 shadow-soft",children:[s.jsx("div",{className:"flex justify-center mb-6",children:s.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-orange-400 via-pink-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg",children:s.jsx(kr,{className:"text-2xl text-white"})})}),s.jsx(vt,{level:2,className:"text-3xl mb-4",children:s.jsx("span",{className:"bg-gradient-to-r from-orange-500 via-pink-500 to-purple-600 bg-clip-text text-transparent",children:"让艺术点亮记忆，让创造温暖心灵"})}),s.jsx(je,{className:"text-xl text-gray-700 mb-6",children:"每一笔都是希望，每一画都是奇迹"}),s.jsx(je,{className:"text-lg text-gray-600",children:"奇迹创造者团队 · 用心陪伴您的艺术之旅 ✨"})]})})})]})})},Vt=[{id:"level-1",title:"线条启蒙",description:"从基础的线条开始，培养手部协调能力",icon:"📈",difficulty:1,stages:[{id:"L1-1",title:"自由画线",description:"随意画线，创作属于您的艺术作品",type:"free-draw"},{id:"L1-2",title:"匀速直线",description:"跟随引导线条，提高绘画精准度",type:"image-trace"},{id:"L1-3",title:"匀速线条组合",description:"从真实图片中抽取线条进行描画练习",type:"image-trace"},{id:"L1-4",title:"曲线消除",description:"绘制曲线和复杂图形",type:"image-trace"},{id:"L1-5",title:"直线图形",description:"绘制三角形、正方形、矩形等直线图形",type:"shape-straight"}]},{id:"level-2",title:"立体空间",description:"从二维图形到三维立体，提升空间想象力",icon:"📦",difficulty:2,stages:[{id:"L2-1",title:"立体图形",description:"绘制锥形、立方体、圆柱体等三维图形",type:"coming-soon",comingSoon:!0},{id:"L2-2",title:"色彩填充",description:"为几何图形填色，学习色系和色谱搭配",type:"coming-soon",comingSoon:!0},{id:"L2-3",title:"质感画笔",description:"复杂曲线描边，选择不同质感的画笔填色",type:"coming-soon",comingSoon:!0},{id:"L2-4",title:"阴影效果",description:"学习光影关系，添加阴影效果",type:"coming-soon",comingSoon:!0}]},{id:"level-3",title:"画面构图",description:"通过引导线条，完成完整的艺术画面",icon:"🖼️",difficulty:3,stages:[{id:"L3-1",title:"抽象艺术",description:"用抽象线条和色块创作现代艺术作品",type:"coming-soon",comingSoon:!0},{id:"L3-2",title:"几何静物",description:"绘制几何形状组成的静物画",type:"coming-soon",comingSoon:!0},{id:"L3-3",title:"风景艺术",description:"创作简单的风景画作品",type:"coming-soon",comingSoon:!0},{id:"L3-4",title:"肖像艺术",description:"学习人物肖像的基本绘制",type:"coming-soon",comingSoon:!0}]},{id:"level-4",title:"智能创作",description:"上传照片，AI辅助创作个性化艺术作品",icon:"🤖",difficulty:4,stages:[{id:"L4-1",title:"照片描边",description:"上传照片，提取轮廓进行描边练习",type:"coming-soon",comingSoon:!0},{id:"L4-2",title:"风格渲染",description:"选择不同艺术风格，AI辅助渲染作品",type:"coming-soon",comingSoon:!0},{id:"L4-3",title:"AI协作",description:"与AI协作，创作独特的个人艺术作品",type:"coming-soon",comingSoon:!0},{id:"L4-4",title:"创意模式",description:"自由创作模式，发挥无限想象力",type:"coming-soon",comingSoon:!0}]}],fo=()=>Vt.flatMap(e=>e.stages.map(t=>t.id)),po=()=>Vt.map(e=>e.id),{Title:Vs,Text:Ys}=Pe,go=({onSelectLevel:e,unlockedLevels:t=["level-1"],onResetProgress:n})=>{const r=Vt.map(i=>({...i,icon:i.id==="level-1"?s.jsx(Cr,{className:"text-4xl"}):i.id==="level-2"?s.jsx(Pr,{className:"text-4xl"}):i.id==="level-3"?s.jsx(Xe,{className:"text-4xl"}):i.id==="level-4"?s.jsx(_t,{className:"text-4xl"}):s.jsx(Oe,{className:"text-4xl"})})),a=i=>{switch(i){case 1:return"text-green-600 bg-green-100";case 2:return"text-blue-600 bg-blue-100";case 3:return"text-purple-600 bg-purple-100";case 4:return"text-orange-600 bg-orange-100";default:return"text-gray-600 bg-gray-100"}},o=i=>{switch(i){case 1:return"入门";case 2:return"初级";case 3:return"中级";case 4:return"高级";default:return"未知"}};return s.jsxs("div",{className:"max-w-6xl mx-auto px-4 py-8",children:[s.jsxs(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-center mb-8",children:[s.jsx(Vs,{level:1,className:"text-purple-600 mb-4",children:"选择游戏级别"}),s.jsx(Ys,{className:"text-xl text-gray-600",children:"从简单的线条开始，逐步提升您的绘画技能"})]}),s.jsx(Es,{gutter:[24,24],children:r.map((i,c)=>{const l=t.includes(i.id);return s.jsx(rt,{xs:24,lg:12,children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:c*.1},children:s.jsxs(ce,{className:`h-full shadow-lg transition-all duration-300 ${l?"hover:shadow-xl cursor-pointer":"opacity-60 cursor-not-allowed"}`,bodyStyle:{padding:"24px"},children:[s.jsxs("div",{className:"text-center mb-6",children:[s.jsxs("div",{className:`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-white relative ${l?"bg-gradient-to-br from-purple-400 to-purple-600":"bg-gradient-to-br from-gray-400 to-gray-500"}`,children:[!l&&s.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:s.jsx(ot,{className:"text-2xl"})}),l&&i.icon]}),s.jsx(Vs,{level:3,className:`mb-2 ${l?"":"text-gray-500"}`,children:i.title}),s.jsxs("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium mb-3 ${l?a(i.difficulty):"bg-gray-100 text-gray-500"}`,children:[s.jsx(_t,{className:"mr-1"}),o(i.difficulty)]}),s.jsx(Ys,{className:`block ${l?"text-gray-600":"text-gray-500"}`,children:l?i.description:"完成前面的级别来解锁"})]}),s.jsx("div",{className:"mt-6",children:s.jsx(re.div,{whileHover:l?{scale:1.02}:{},whileTap:l?{scale:.98}:{},children:s.jsx(H,{type:l?"primary":"default",size:"large",icon:l?s.jsx(Oe,{}):s.jsx(ot,{}),onClick:()=>l&&e(i.id),disabled:!l,className:"w-full h-12 text-lg",children:l?"进入级别":"级别锁定"})})})]})})},i.id)})}),n&&s.jsx("div",{className:"text-center mt-8",children:s.jsx(yn,{title:"重置游戏进度",description:"确定要重置所有游戏进度吗？这将清除所有解锁的级别。",onConfirm:n,okText:"确定",cancelText:"取消",children:s.jsx(H,{icon:s.jsx(Ke,{}),size:"small",className:"text-gray-500 hover:text-gray-700",children:"重置进度"})})})]})},Ts={get allLevels(){return po()},get allStages(){return fo()}},xo=()=>Ts.allLevels,yo=()=>Ts.allStages,Vn=e=>Ts.allStages.includes(e),{Title:ls,Text:Xs}=Pe,bo=({level:e,onSelectStage:t,onBack:n,unlockedStages:r=[]})=>{if(e.id==="level-1")return s.jsx(jo,{level:e,onSelectStage:t,onBack:n,unlockedStages:r});const a=c=>{switch(c){case 1:return"text-green-600 bg-green-100";case 2:return"text-blue-600 bg-blue-100";case 3:return"text-purple-600 bg-purple-100";case 4:return"text-orange-600 bg-orange-100";default:return"text-gray-600 bg-gray-100"}},o=c=>{switch(c){case 1:return"入门";case 2:return"初级";case 3:return"中级";case 4:return"高级";default:return"未知"}},i=(c,l)=>Vn(c);return s.jsx("div",{className:"max-w-6xl mx-auto px-4 py-8",children:s.jsxs(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[s.jsx(ce,{className:"mb-6 shadow-sm",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg flex items-center justify-center text-white text-2xl",children:(typeof e.icon=="string",e.icon)}),s.jsxs("div",{children:[s.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[s.jsx(ls,{level:2,className:"mb-0 text-purple-600",children:e.title}),s.jsxs("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${a(e.difficulty)}`,children:[s.jsx(_t,{className:"mr-1"}),o(e.difficulty)]})]}),s.jsx(Xs,{className:"text-lg text-gray-600",children:e.description})]})]}),s.jsx(H,{icon:s.jsx(Fe,{}),onClick:n,size:"large",className:"h-12 px-6",children:"返回级别选择"})]})}),s.jsx(Es,{gutter:[24,24],children:e.stages.map((c,l)=>{const u=i(c.id),d=!c.comingSoon&&u;return s.jsx(rt,{xs:24,sm:12,lg:8,children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:l*.1},whileHover:d?{scale:1.02}:{},whileTap:d?{scale:.98}:{},children:s.jsx(ce,{className:`h-full shadow-lg transition-all duration-300 ${d?"hover:shadow-xl cursor-pointer":"opacity-60 cursor-not-allowed"}`,bodyStyle:{padding:"24px"},onClick:()=>d&&t(c.id),children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-white ${d?"bg-gradient-to-br from-blue-400 to-blue-600":"bg-gradient-to-br from-gray-400 to-gray-500"}`,children:d?s.jsx(Oe,{className:"text-2xl"}):s.jsx(ot,{className:"text-2xl"})}),s.jsxs(ls,{level:4,className:`mb-3 ${d?"":"text-gray-500"}`,children:[c.title,c.comingSoon&&s.jsx("div",{className:"mt-2",children:s.jsx("span",{className:"text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded-full",children:"即将推出"})})]}),s.jsx(Xs,{className:`block mb-6 ${d?"text-gray-600":"text-gray-500"}`,children:c.description}),s.jsx(H,{type:d?"primary":"default",size:"large",icon:d?s.jsx(Oe,{}):s.jsx(ot,{}),disabled:!d,className:"w-full h-12",children:d?"开始练习":c.comingSoon?"即将推出":"完成前面练习解锁"})]})})})},c.id)})}),s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"mt-8",children:s.jsx(ce,{className:"bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200",children:s.jsxs("div",{className:"text-center",children:[s.jsx(ls,{level:4,className:"text-blue-600 mb-3",children:"💡 学习建议"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-700",children:[s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-blue-400 rounded-full"}),s.jsx("span",{children:"按顺序完成练习效果更佳"})]}),s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-purple-400 rounded-full"}),s.jsx("span",{children:"每次练习时间不宜过长"})]}),s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-pink-400 rounded-full"}),s.jsx("span",{children:"重复练习有助于提高技能"})]})]})]})})})]})})},jo=({level:e,onSelectStage:t,onBack:n,unlockedStages:r=[]})=>{const a=window.location.search.includes("debug=true"),o=(l,u)=>Vn(l),i=[{id:"L1-1",x:"15%",y:"25%",number:1},{id:"L1-2",x:"30%",y:"40%",number:2},{id:"L1-3",x:"50%",y:"30%",number:3},{id:"L1-4",x:"70%",y:"45%",number:4},{id:"L1-5",x:"85%",y:"35%",number:5}],c=l=>{e.stages.findIndex(d=>d.id===l),o(l)&&t(l)};return s.jsx("div",{className:"min-h-screen relative",children:s.jsxs("div",{className:"absolute inset-0 bg-no-repeat",style:{backgroundImage:"url(/background-l1.jpg)",backgroundSize:"auto 100vh",backgroundPosition:"center center",backgroundColor:"#f0f0f0"},children:[s.jsx("div",{className:"absolute top-8 left-8 z-10",children:s.jsx(H,{icon:s.jsx(Fe,{}),onClick:n,size:"large",className:"h-12 px-6 bg-white/90 backdrop-blur-sm hover:bg-white",children:"返回级别选择"})}),i.map((l,u)=>{const d=e.stages.find(w=>w.id===l.id),f=o(l.id);return d?s.jsxs(re.div,{className:"absolute z-20",style:{left:l.x,top:l.y,transform:"translate(-50%, -50%)"},initial:{opacity:0,scale:0},animate:{opacity:1,scale:1},transition:{duration:.6,delay:u*.1},whileHover:f?{scale:1.1}:{},whileTap:f?{scale:.95}:{},children:[s.jsx("div",{className:`
                                    w-16 h-16 rounded-full flex items-center justify-center text-white text-xl font-bold
                                    cursor-pointer transition-all duration-300 shadow-lg
                                    ${f?"bg-gradient-to-br from-blue-400 to-blue-600 hover:shadow-xl":"bg-gradient-to-br from-gray-400 to-gray-500 cursor-not-allowed opacity-60"}
                                    ${a?"ring-2 ring-red-500 ring-opacity-50":""}
                                `,onClick:()=>c(l.id),title:f?`${d.title} - 点击开始`:`${d.title} - 需要完成前面的关卡`,children:f?l.number:s.jsx(ot,{className:"text-lg"})}),a&&s.jsxs("div",{className:"absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs bg-red-500 text-white px-1 rounded",children:[l.x,", ",l.y]}),s.jsx("div",{className:"absolute top-full mt-2 left-1/2 transform -translate-x-1/2 whitespace-nowrap",children:s.jsx("div",{className:"bg-black/70 text-white px-2 py-1 rounded text-sm",children:d.title})})]},l.id):null})]})})},Yn=({width:e=800,height:t=600,onSave:n,disabled:r=!1,backgroundImage:a,showReference:o=!1,hideCanvas:i=!1,onClearRef:c,isCompleted:l=!1,onPathsChange:u})=>{const d=h.useRef(null),[f,w]=h.useState(!1),[I,j]=h.useState(5),[v,g]=h.useState("#000000"),[P,R]=h.useState([]),[C,q]=h.useState([]),[Q,ie]=h.useState(!1),[ee,fe]=h.useState(!1),S=l,D=h.useCallback(y=>{const $=d.current;if(!$)return{x:0,y:0};const W=$.getBoundingClientRect(),oe=$.width/W.width,Z=$.height/W.height;let B,le;if("touches"in y){const ye=y.touches[0]||y.changedTouches[0];B=ye.clientX,le=ye.clientY}else B=y.clientX,le=y.clientY;const ne=(B-W.left)*oe,Se=(le-W.top)*Z;return{x:ne,y:Se}},[]),N=h.useCallback(y=>{if(r||S)return;y.preventDefault(),w(!0);const $=D(y);q([$])},[r,S,D]),k=h.useCallback(y=>{if(!f||r||S)return;y.preventDefault();const $=D(y);q(W=>[...W,$])},[f,r,S,D]),V=h.useCallback(()=>{if(!(!f||r||S)){if(w(!1),C.length>1){const y={points:C,color:v,size:I};R($=>{const W=[...$,y];return ie(!0),fe(!1),u==null||u(W),W})}q([])}},[f,r,S,C,v,I]),G=h.useCallback((y,$,W,oe)=>{if(!($.length<2)){y.strokeStyle=W,y.lineWidth=oe,y.lineCap="round",y.lineJoin="round",y.beginPath(),y.moveTo($[0].x,$[0].y);for(let Z=1;Z<$.length;Z++)y.lineTo($[Z].x,$[Z].y);y.stroke()}},[]),te=h.useCallback(()=>{const y=d.current;if(!y)return;const $=y.getContext("2d",{alpha:!0});if($)if(y.style.backgroundColor="rgba(0,0,0,0)",y.style.background="none",$.clearRect(0,0,y.width,y.height),$.globalCompositeOperation="source-over",a&&o){const W=new Image;W.onload=()=>{$.globalAlpha=.3,$.drawImage(W,0,0,y.width,y.height),$.globalAlpha=1,P.forEach(oe=>{G($,oe.points,oe.color,oe.size)}),C.length>1&&G($,C,v,I)},W.src=a}else P.forEach(W=>{G($,W.points,W.color,W.size)}),C.length>1&&G($,C,v,I)},[P,C,v,I,a,o,G]),de=h.useCallback(()=>{const y=d.current;if(y){const $=y.getContext("2d",{alpha:!0});$&&$.clearRect(0,0,y.width,y.height)}R([]),q([]),ie(!1),fe(!1),u==null||u([])},[u]),K=h.useCallback(()=>{de()},[de]),J=h.useCallback(()=>{const y=d.current;if(!y)return;const $=y.toDataURL("image/png");n==null||n($),fe(!0),X.success("画作已保存！")},[n]);return h.useEffect(()=>{const y=d.current;if(y){const $=y.getContext("2d",{alpha:!0});$&&($.globalCompositeOperation="source-over",y.style.backgroundColor="rgba(0,0,0,0)",y.style.background="none",$.clearRect(0,0,y.width,y.height),y.setAttribute("style","background-color: rgba(0,0,0,0) !important; background: none !important; touch-action: none; border-radius: 8px; display: block;"),y.style.setProperty("background-color","rgba(0,0,0,0)","important"),y.style.setProperty("background","none","important"),console.log("Canvas initialized with alpha channel"))}},[]),h.useEffect(()=>{te()},[te]),h.useEffect(()=>{const y=d.current;if(y){y.width=e,y.height=t,y.style.width=`${e}px`,y.style.height=`${t}px`;const $=Z=>{if(r||S)return;Z.preventDefault(),Z.stopPropagation();const B=Z.touches[0],le=y.getBoundingClientRect(),ne=y.width/le.width,Se=y.height/le.height,ye={x:(B.clientX-le.left)*ne,y:(B.clientY-le.top)*Se};w(!0),q([ye])},W=Z=>{if(!f||r||S)return;Z.preventDefault(),Z.stopPropagation();const B=Z.touches[0],le=y.getBoundingClientRect(),ne=y.width/le.width,Se=y.height/le.height,ye={x:(B.clientX-le.left)*ne,y:(B.clientY-le.top)*Se};q($e=>[...$e,ye])},oe=Z=>{if(Z.preventDefault(),Z.stopPropagation(),!(!f||r||S)){if(w(!1),C.length>1){const B={points:C,color:v,size:I};R(le=>{const ne=[...le,B];return ie(!0),fe(!1),u==null||u(ne),ne})}q([])}};return y.addEventListener("touchstart",$,{passive:!1}),y.addEventListener("touchmove",W,{passive:!1}),y.addEventListener("touchend",oe,{passive:!1}),te(),()=>{y.removeEventListener("touchstart",$),y.removeEventListener("touchmove",W),y.removeEventListener("touchend",oe)}}},[e,t,te,r,S,f,C,v,I,u]),h.useEffect(()=>{c&&c(de)},[c,de]),h.useEffect(()=>{if(!i&&P.length===0){const y=d.current;if(y){const $=y.getContext("2d",{alpha:!0});$&&$.clearRect(0,0,y.width,y.height)}}},[i,P]),s.jsxs("div",{className:"flex flex-col items-center gap-4",style:{backgroundColor:"transparent"},children:[s.jsxs("div",{className:"flex flex-wrap items-center gap-4 p-4 bg-white rounded-lg shadow-sm border",children:[s.jsxs(me,{children:[s.jsx("span",{className:"text-sm font-medium",children:"画笔大小:"}),s.jsx(bn,{min:1,max:20,value:I,onChange:j,style:{width:100},disabled:S}),s.jsxs("span",{className:"text-sm text-gray-500",children:[I,"px"]})]}),s.jsxs(me,{children:[s.jsx("span",{className:"text-sm font-medium",children:"颜色:"}),s.jsx(Tr,{value:v,onChange:y=>g(y.toHexString()),showText:!0,disabled:S})]}),s.jsxs(me,{children:[s.jsx(H,{icon:s.jsx(jn,{}),onClick:K,disabled:!Q||ee||S,title:"清空",children:"清空"}),s.jsx(H,{icon:s.jsx(vn,{}),onClick:J,type:"primary",disabled:!Q||ee||S,title:"保存",children:"保存"})]})]}),!i&&s.jsx("div",{style:{backgroundColor:"rgba(0,0,0,0)",background:"none",position:"relative",borderRadius:"8px",border:"2px solid #e5e7eb",overflow:"hidden"},children:s.jsx("canvas",{ref:d,width:e,height:t,className:S?"cursor-not-allowed opacity-60":"cursor-crosshair",onMouseDown:N,onMouseMove:k,onMouseUp:V,onMouseLeave:V,style:{touchAction:"none",backgroundColor:"rgba(0,0,0,0)",background:"none",borderRadius:"8px",display:"block",opacity:"1",position:"relative",margin:0,padding:0,border:"none",outline:"none"}})})]})};function Xn(e,t){return function(){return e.apply(t,arguments)}}const{toString:vo}=Object.prototype,{getPrototypeOf:Rs}=Object,{iterator:Yt,toStringTag:Kn}=Symbol,Xt=(e=>t=>{const n=vo.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),Ie=e=>(e=e.toLowerCase(),t=>Xt(t)===e),Kt=e=>t=>typeof t===e,{isArray:Ze}=Array,ct=Kt("undefined");function ht(e){return e!==null&&!ct(e)&&e.constructor!==null&&!ct(e.constructor)&&ke(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Qn=Ie("ArrayBuffer");function wo(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Qn(e.buffer),t}const So=Kt("string"),ke=Kt("function"),Zn=Kt("number"),mt=e=>e!==null&&typeof e=="object",No=e=>e===!0||e===!1,Ct=e=>{if(Xt(e)!=="object")return!1;const t=Rs(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Kn in e)&&!(Yt in e)},Eo=e=>{if(!mt(e)||ht(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},ko=Ie("Date"),Co=Ie("File"),Po=Ie("Blob"),To=Ie("FileList"),Ro=e=>mt(e)&&ke(e.pipe),_o=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||ke(e.append)&&((t=Xt(e))==="formdata"||t==="object"&&ke(e.toString)&&e.toString()==="[object FormData]"))},Lo=Ie("URLSearchParams"),[Io,Ao,Oo,$o]=["ReadableStream","Request","Response","Headers"].map(Ie),Do=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function ft(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,a;if(typeof e!="object"&&(e=[e]),Ze(e))for(r=0,a=e.length;r<a;r++)t.call(null,e[r],r,e);else{if(ht(e))return;const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let c;for(r=0;r<i;r++)c=o[r],t.call(null,e[c],c,e)}}function er(e,t){if(ht(e))return null;t=t.toLowerCase();const n=Object.keys(e);let r=n.length,a;for(;r-- >0;)if(a=n[r],t===a.toLowerCase())return a;return null}const He=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,tr=e=>!ct(e)&&e!==He;function bs(){const{caseless:e}=tr(this)&&this||{},t={},n=(r,a)=>{const o=e&&er(t,a)||a;Ct(t[o])&&Ct(r)?t[o]=bs(t[o],r):Ct(r)?t[o]=bs({},r):Ze(r)?t[o]=r.slice():t[o]=r};for(let r=0,a=arguments.length;r<a;r++)arguments[r]&&ft(arguments[r],n);return t}const Fo=(e,t,n,{allOwnKeys:r}={})=>(ft(t,(a,o)=>{n&&ke(a)?e[o]=Xn(a,n):e[o]=a},{allOwnKeys:r}),e),Mo=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Uo=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},zo=(e,t,n,r)=>{let a,o,i;const c={};if(t=t||{},e==null)return t;do{for(a=Object.getOwnPropertyNames(e),o=a.length;o-- >0;)i=a[o],(!r||r(i,e,t))&&!c[i]&&(t[i]=e[i],c[i]=!0);e=n!==!1&&Rs(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Bo=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},qo=e=>{if(!e)return null;if(Ze(e))return e;let t=e.length;if(!Zn(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Ho=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Rs(Uint8Array)),Wo=(e,t)=>{const r=(e&&e[Yt]).call(e);let a;for(;(a=r.next())&&!a.done;){const o=a.value;t.call(e,o[0],o[1])}},Jo=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},Go=Ie("HTMLFormElement"),Vo=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,a){return r.toUpperCase()+a}),Ks=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Yo=Ie("RegExp"),sr=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};ft(n,(a,o)=>{let i;(i=t(a,o,e))!==!1&&(r[o]=i||a)}),Object.defineProperties(e,r)},Xo=e=>{sr(e,(t,n)=>{if(ke(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(ke(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Ko=(e,t)=>{const n={},r=a=>{a.forEach(o=>{n[o]=!0})};return Ze(e)?r(e):r(String(e).split(t)),n},Qo=()=>{},Zo=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function ei(e){return!!(e&&ke(e.append)&&e[Kn]==="FormData"&&e[Yt])}const ti=e=>{const t=new Array(10),n=(r,a)=>{if(mt(r)){if(t.indexOf(r)>=0)return;if(ht(r))return r;if(!("toJSON"in r)){t[a]=r;const o=Ze(r)?[]:{};return ft(r,(i,c)=>{const l=n(i,a+1);!ct(l)&&(o[c]=l)}),t[a]=void 0,o}}return r};return n(e,0)},si=Ie("AsyncFunction"),ni=e=>e&&(mt(e)||ke(e))&&ke(e.then)&&ke(e.catch),nr=((e,t)=>e?setImmediate:t?((n,r)=>(He.addEventListener("message",({source:a,data:o})=>{a===He&&o===n&&r.length&&r.shift()()},!1),a=>{r.push(a),He.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",ke(He.postMessage)),ri=typeof queueMicrotask<"u"?queueMicrotask.bind(He):typeof process<"u"&&process.nextTick||nr,ai=e=>e!=null&&ke(e[Yt]),p={isArray:Ze,isArrayBuffer:Qn,isBuffer:ht,isFormData:_o,isArrayBufferView:wo,isString:So,isNumber:Zn,isBoolean:No,isObject:mt,isPlainObject:Ct,isEmptyObject:Eo,isReadableStream:Io,isRequest:Ao,isResponse:Oo,isHeaders:$o,isUndefined:ct,isDate:ko,isFile:Co,isBlob:Po,isRegExp:Yo,isFunction:ke,isStream:Ro,isURLSearchParams:Lo,isTypedArray:Ho,isFileList:To,forEach:ft,merge:bs,extend:Fo,trim:Do,stripBOM:Mo,inherits:Uo,toFlatObject:zo,kindOf:Xt,kindOfTest:Ie,endsWith:Bo,toArray:qo,forEachEntry:Wo,matchAll:Jo,isHTMLForm:Go,hasOwnProperty:Ks,hasOwnProp:Ks,reduceDescriptors:sr,freezeMethods:Xo,toObjectSet:Ko,toCamelCase:Vo,noop:Qo,toFiniteNumber:Zo,findKey:er,global:He,isContextDefined:tr,isSpecCompliantForm:ei,toJSONObject:ti,isAsyncFn:si,isThenable:ni,setImmediate:nr,asap:ri,isIterable:ai};function Y(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a,this.status=a.status?a.status:null)}p.inherits(Y,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:p.toJSONObject(this.config),code:this.code,status:this.status}}});const rr=Y.prototype,ar={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{ar[e]={value:e}});Object.defineProperties(Y,ar);Object.defineProperty(rr,"isAxiosError",{value:!0});Y.from=(e,t,n,r,a,o)=>{const i=Object.create(rr);return p.toFlatObject(e,i,function(l){return l!==Error.prototype},c=>c!=="isAxiosError"),Y.call(i,e.message,t,n,r,a),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const oi=null;function js(e){return p.isPlainObject(e)||p.isArray(e)}function or(e){return p.endsWith(e,"[]")?e.slice(0,-2):e}function Qs(e,t,n){return e?e.concat(t).map(function(a,o){return a=or(a),!n&&o?"["+a+"]":a}).join(n?".":""):t}function ii(e){return p.isArray(e)&&!e.some(js)}const li=p.toFlatObject(p,{},null,function(t){return/^is[A-Z]/.test(t)});function Qt(e,t,n){if(!p.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=p.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(v,g){return!p.isUndefined(g[v])});const r=n.metaTokens,a=n.visitor||d,o=n.dots,i=n.indexes,l=(n.Blob||typeof Blob<"u"&&Blob)&&p.isSpecCompliantForm(t);if(!p.isFunction(a))throw new TypeError("visitor must be a function");function u(j){if(j===null)return"";if(p.isDate(j))return j.toISOString();if(p.isBoolean(j))return j.toString();if(!l&&p.isBlob(j))throw new Y("Blob is not supported. Use a Buffer instead.");return p.isArrayBuffer(j)||p.isTypedArray(j)?l&&typeof Blob=="function"?new Blob([j]):Buffer.from(j):j}function d(j,v,g){let P=j;if(j&&!g&&typeof j=="object"){if(p.endsWith(v,"{}"))v=r?v:v.slice(0,-2),j=JSON.stringify(j);else if(p.isArray(j)&&ii(j)||(p.isFileList(j)||p.endsWith(v,"[]"))&&(P=p.toArray(j)))return v=or(v),P.forEach(function(C,q){!(p.isUndefined(C)||C===null)&&t.append(i===!0?Qs([v],q,o):i===null?v:v+"[]",u(C))}),!1}return js(j)?!0:(t.append(Qs(g,v,o),u(j)),!1)}const f=[],w=Object.assign(li,{defaultVisitor:d,convertValue:u,isVisitable:js});function I(j,v){if(!p.isUndefined(j)){if(f.indexOf(j)!==-1)throw Error("Circular reference detected in "+v.join("."));f.push(j),p.forEach(j,function(P,R){(!(p.isUndefined(P)||P===null)&&a.call(t,P,p.isString(R)?R.trim():R,v,w))===!0&&I(P,v?v.concat(R):[R])}),f.pop()}}if(!p.isObject(e))throw new TypeError("data must be an object");return I(e),t}function Zs(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function _s(e,t){this._pairs=[],e&&Qt(e,this,t)}const ir=_s.prototype;ir.append=function(t,n){this._pairs.push([t,n])};ir.toString=function(t){const n=t?function(r){return t.call(this,r,Zs)}:Zs;return this._pairs.map(function(a){return n(a[0])+"="+n(a[1])},"").join("&")};function ci(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function lr(e,t,n){if(!t)return e;const r=n&&n.encode||ci;p.isFunction(n)&&(n={serialize:n});const a=n&&n.serialize;let o;if(a?o=a(t,n):o=p.isURLSearchParams(t)?t.toString():new _s(t,n).toString(r),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class en{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){p.forEach(this.handlers,function(r){r!==null&&t(r)})}}const cr={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},di=typeof URLSearchParams<"u"?URLSearchParams:_s,ui=typeof FormData<"u"?FormData:null,hi=typeof Blob<"u"?Blob:null,mi={isBrowser:!0,classes:{URLSearchParams:di,FormData:ui,Blob:hi},protocols:["http","https","file","blob","url","data"]},Ls=typeof window<"u"&&typeof document<"u",vs=typeof navigator=="object"&&navigator||void 0,fi=Ls&&(!vs||["ReactNative","NativeScript","NS"].indexOf(vs.product)<0),pi=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",gi=Ls&&window.location.href||"http://localhost",xi=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Ls,hasStandardBrowserEnv:fi,hasStandardBrowserWebWorkerEnv:pi,navigator:vs,origin:gi},Symbol.toStringTag,{value:"Module"})),we={...xi,...mi};function yi(e,t){return Qt(e,new we.classes.URLSearchParams,{visitor:function(n,r,a,o){return we.isNode&&p.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)},...t})}function bi(e){return p.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function ji(e){const t={},n=Object.keys(e);let r;const a=n.length;let o;for(r=0;r<a;r++)o=n[r],t[o]=e[o];return t}function dr(e){function t(n,r,a,o){let i=n[o++];if(i==="__proto__")return!0;const c=Number.isFinite(+i),l=o>=n.length;return i=!i&&p.isArray(a)?a.length:i,l?(p.hasOwnProp(a,i)?a[i]=[a[i],r]:a[i]=r,!c):((!a[i]||!p.isObject(a[i]))&&(a[i]=[]),t(n,r,a[i],o)&&p.isArray(a[i])&&(a[i]=ji(a[i])),!c)}if(p.isFormData(e)&&p.isFunction(e.entries)){const n={};return p.forEachEntry(e,(r,a)=>{t(bi(r),a,n,0)}),n}return null}function vi(e,t,n){if(p.isString(e))try{return(t||JSON.parse)(e),p.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const pt={transitional:cr,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",a=r.indexOf("application/json")>-1,o=p.isObject(t);if(o&&p.isHTMLForm(t)&&(t=new FormData(t)),p.isFormData(t))return a?JSON.stringify(dr(t)):t;if(p.isArrayBuffer(t)||p.isBuffer(t)||p.isStream(t)||p.isFile(t)||p.isBlob(t)||p.isReadableStream(t))return t;if(p.isArrayBufferView(t))return t.buffer;if(p.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let c;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return yi(t,this.formSerializer).toString();if((c=p.isFileList(t))||r.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return Qt(c?{"files[]":t}:t,l&&new l,this.formSerializer)}}return o||a?(n.setContentType("application/json",!1),vi(t)):t}],transformResponse:[function(t){const n=this.transitional||pt.transitional,r=n&&n.forcedJSONParsing,a=this.responseType==="json";if(p.isResponse(t)||p.isReadableStream(t))return t;if(t&&p.isString(t)&&(r&&!this.responseType||a)){const i=!(n&&n.silentJSONParsing)&&a;try{return JSON.parse(t)}catch(c){if(i)throw c.name==="SyntaxError"?Y.from(c,Y.ERR_BAD_RESPONSE,this,null,this.response):c}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:we.classes.FormData,Blob:we.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};p.forEach(["delete","get","head","post","put","patch"],e=>{pt.headers[e]={}});const wi=p.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Si=e=>{const t={};let n,r,a;return e&&e.split(`
`).forEach(function(i){a=i.indexOf(":"),n=i.substring(0,a).trim().toLowerCase(),r=i.substring(a+1).trim(),!(!n||t[n]&&wi[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},tn=Symbol("internals");function st(e){return e&&String(e).trim().toLowerCase()}function Pt(e){return e===!1||e==null?e:p.isArray(e)?e.map(Pt):String(e)}function Ni(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const Ei=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function cs(e,t,n,r,a){if(p.isFunction(r))return r.call(this,t,n);if(a&&(t=n),!!p.isString(t)){if(p.isString(r))return t.indexOf(r)!==-1;if(p.isRegExp(r))return r.test(t)}}function ki(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function Ci(e,t){const n=p.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(a,o,i){return this[r].call(this,t,a,o,i)},configurable:!0})})}let Ce=class{constructor(t){t&&this.set(t)}set(t,n,r){const a=this;function o(c,l,u){const d=st(l);if(!d)throw new Error("header name must be a non-empty string");const f=p.findKey(a,d);(!f||a[f]===void 0||u===!0||u===void 0&&a[f]!==!1)&&(a[f||l]=Pt(c))}const i=(c,l)=>p.forEach(c,(u,d)=>o(u,d,l));if(p.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(p.isString(t)&&(t=t.trim())&&!Ei(t))i(Si(t),n);else if(p.isObject(t)&&p.isIterable(t)){let c={},l,u;for(const d of t){if(!p.isArray(d))throw TypeError("Object iterator must return a key-value pair");c[u=d[0]]=(l=c[u])?p.isArray(l)?[...l,d[1]]:[l,d[1]]:d[1]}i(c,n)}else t!=null&&o(n,t,r);return this}get(t,n){if(t=st(t),t){const r=p.findKey(this,t);if(r){const a=this[r];if(!n)return a;if(n===!0)return Ni(a);if(p.isFunction(n))return n.call(this,a,r);if(p.isRegExp(n))return n.exec(a);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=st(t),t){const r=p.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||cs(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let a=!1;function o(i){if(i=st(i),i){const c=p.findKey(r,i);c&&(!n||cs(r,r[c],c,n))&&(delete r[c],a=!0)}}return p.isArray(t)?t.forEach(o):o(t),a}clear(t){const n=Object.keys(this);let r=n.length,a=!1;for(;r--;){const o=n[r];(!t||cs(this,this[o],o,t,!0))&&(delete this[o],a=!0)}return a}normalize(t){const n=this,r={};return p.forEach(this,(a,o)=>{const i=p.findKey(r,o);if(i){n[i]=Pt(a),delete n[o];return}const c=t?ki(o):String(o).trim();c!==o&&delete n[o],n[c]=Pt(a),r[c]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return p.forEach(this,(r,a)=>{r!=null&&r!==!1&&(n[a]=t&&p.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(a=>r.set(a)),r}static accessor(t){const r=(this[tn]=this[tn]={accessors:{}}).accessors,a=this.prototype;function o(i){const c=st(i);r[c]||(Ci(a,i),r[c]=!0)}return p.isArray(t)?t.forEach(o):o(t),this}};Ce.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);p.reduceDescriptors(Ce.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});p.freezeMethods(Ce);function ds(e,t){const n=this||pt,r=t||n,a=Ce.from(r.headers);let o=r.data;return p.forEach(e,function(c){o=c.call(n,o,a.normalize(),t?t.status:void 0)}),a.normalize(),o}function ur(e){return!!(e&&e.__CANCEL__)}function et(e,t,n){Y.call(this,e??"canceled",Y.ERR_CANCELED,t,n),this.name="CanceledError"}p.inherits(et,Y,{__CANCEL__:!0});function hr(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new Y("Request failed with status code "+n.status,[Y.ERR_BAD_REQUEST,Y.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Pi(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Ti(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a=0,o=0,i;return t=t!==void 0?t:1e3,function(l){const u=Date.now(),d=r[o];i||(i=u),n[a]=l,r[a]=u;let f=o,w=0;for(;f!==a;)w+=n[f++],f=f%e;if(a=(a+1)%e,a===o&&(o=(o+1)%e),u-i<t)return;const I=d&&u-d;return I?Math.round(w*1e3/I):void 0}}function Ri(e,t){let n=0,r=1e3/t,a,o;const i=(u,d=Date.now())=>{n=d,a=null,o&&(clearTimeout(o),o=null),e(...u)};return[(...u)=>{const d=Date.now(),f=d-n;f>=r?i(u,d):(a=u,o||(o=setTimeout(()=>{o=null,i(a)},r-f)))},()=>a&&i(a)]}const It=(e,t,n=3)=>{let r=0;const a=Ti(50,250);return Ri(o=>{const i=o.loaded,c=o.lengthComputable?o.total:void 0,l=i-r,u=a(l),d=i<=c;r=i;const f={loaded:i,total:c,progress:c?i/c:void 0,bytes:l,rate:u||void 0,estimated:u&&c&&d?(c-i)/u:void 0,event:o,lengthComputable:c!=null,[t?"download":"upload"]:!0};e(f)},n)},sn=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},nn=e=>(...t)=>p.asap(()=>e(...t)),_i=we.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,we.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(we.origin),we.navigator&&/(msie|trident)/i.test(we.navigator.userAgent)):()=>!0,Li=we.hasStandardBrowserEnv?{write(e,t,n,r,a,o){const i=[e+"="+encodeURIComponent(t)];p.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),p.isString(r)&&i.push("path="+r),p.isString(a)&&i.push("domain="+a),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Ii(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Ai(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function mr(e,t,n){let r=!Ii(t);return e&&(r||n==!1)?Ai(e,t):t}const rn=e=>e instanceof Ce?{...e}:e;function Ge(e,t){t=t||{};const n={};function r(u,d,f,w){return p.isPlainObject(u)&&p.isPlainObject(d)?p.merge.call({caseless:w},u,d):p.isPlainObject(d)?p.merge({},d):p.isArray(d)?d.slice():d}function a(u,d,f,w){if(p.isUndefined(d)){if(!p.isUndefined(u))return r(void 0,u,f,w)}else return r(u,d,f,w)}function o(u,d){if(!p.isUndefined(d))return r(void 0,d)}function i(u,d){if(p.isUndefined(d)){if(!p.isUndefined(u))return r(void 0,u)}else return r(void 0,d)}function c(u,d,f){if(f in t)return r(u,d);if(f in e)return r(void 0,u)}const l={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:c,headers:(u,d,f)=>a(rn(u),rn(d),f,!0)};return p.forEach(Object.keys({...e,...t}),function(d){const f=l[d]||a,w=f(e[d],t[d],d);p.isUndefined(w)&&f!==c||(n[d]=w)}),n}const fr=e=>{const t=Ge({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:a,xsrfCookieName:o,headers:i,auth:c}=t;t.headers=i=Ce.from(i),t.url=lr(mr(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&i.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let l;if(p.isFormData(n)){if(we.hasStandardBrowserEnv||we.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((l=i.getContentType())!==!1){const[u,...d]=l?l.split(";").map(f=>f.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...d].join("; "))}}if(we.hasStandardBrowserEnv&&(r&&p.isFunction(r)&&(r=r(t)),r||r!==!1&&_i(t.url))){const u=a&&o&&Li.read(o);u&&i.set(a,u)}return t},Oi=typeof XMLHttpRequest<"u",$i=Oi&&function(e){return new Promise(function(n,r){const a=fr(e);let o=a.data;const i=Ce.from(a.headers).normalize();let{responseType:c,onUploadProgress:l,onDownloadProgress:u}=a,d,f,w,I,j;function v(){I&&I(),j&&j(),a.cancelToken&&a.cancelToken.unsubscribe(d),a.signal&&a.signal.removeEventListener("abort",d)}let g=new XMLHttpRequest;g.open(a.method.toUpperCase(),a.url,!0),g.timeout=a.timeout;function P(){if(!g)return;const C=Ce.from("getAllResponseHeaders"in g&&g.getAllResponseHeaders()),Q={data:!c||c==="text"||c==="json"?g.responseText:g.response,status:g.status,statusText:g.statusText,headers:C,config:e,request:g};hr(function(ee){n(ee),v()},function(ee){r(ee),v()},Q),g=null}"onloadend"in g?g.onloadend=P:g.onreadystatechange=function(){!g||g.readyState!==4||g.status===0&&!(g.responseURL&&g.responseURL.indexOf("file:")===0)||setTimeout(P)},g.onabort=function(){g&&(r(new Y("Request aborted",Y.ECONNABORTED,e,g)),g=null)},g.onerror=function(){r(new Y("Network Error",Y.ERR_NETWORK,e,g)),g=null},g.ontimeout=function(){let q=a.timeout?"timeout of "+a.timeout+"ms exceeded":"timeout exceeded";const Q=a.transitional||cr;a.timeoutErrorMessage&&(q=a.timeoutErrorMessage),r(new Y(q,Q.clarifyTimeoutError?Y.ETIMEDOUT:Y.ECONNABORTED,e,g)),g=null},o===void 0&&i.setContentType(null),"setRequestHeader"in g&&p.forEach(i.toJSON(),function(q,Q){g.setRequestHeader(Q,q)}),p.isUndefined(a.withCredentials)||(g.withCredentials=!!a.withCredentials),c&&c!=="json"&&(g.responseType=a.responseType),u&&([w,j]=It(u,!0),g.addEventListener("progress",w)),l&&g.upload&&([f,I]=It(l),g.upload.addEventListener("progress",f),g.upload.addEventListener("loadend",I)),(a.cancelToken||a.signal)&&(d=C=>{g&&(r(!C||C.type?new et(null,e,g):C),g.abort(),g=null)},a.cancelToken&&a.cancelToken.subscribe(d),a.signal&&(a.signal.aborted?d():a.signal.addEventListener("abort",d)));const R=Pi(a.url);if(R&&we.protocols.indexOf(R)===-1){r(new Y("Unsupported protocol "+R+":",Y.ERR_BAD_REQUEST,e));return}g.send(o||null)})},Di=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,a;const o=function(u){if(!a){a=!0,c();const d=u instanceof Error?u:this.reason;r.abort(d instanceof Y?d:new et(d instanceof Error?d.message:d))}};let i=t&&setTimeout(()=>{i=null,o(new Y(`timeout ${t} of ms exceeded`,Y.ETIMEDOUT))},t);const c=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(o):u.removeEventListener("abort",o)}),e=null)};e.forEach(u=>u.addEventListener("abort",o));const{signal:l}=r;return l.unsubscribe=()=>p.asap(c),l}},Fi=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,a;for(;r<n;)a=r+t,yield e.slice(r,a),r=a},Mi=async function*(e,t){for await(const n of Ui(e))yield*Fi(n,t)},Ui=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},an=(e,t,n,r)=>{const a=Mi(e,t);let o=0,i,c=l=>{i||(i=!0,r&&r(l))};return new ReadableStream({async pull(l){try{const{done:u,value:d}=await a.next();if(u){c(),l.close();return}let f=d.byteLength;if(n){let w=o+=f;n(w)}l.enqueue(new Uint8Array(d))}catch(u){throw c(u),u}},cancel(l){return c(l),a.return()}},{highWaterMark:2})},Zt=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",pr=Zt&&typeof ReadableStream=="function",zi=Zt&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),gr=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Bi=pr&&gr(()=>{let e=!1;const t=new Request(we.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),on=64*1024,ws=pr&&gr(()=>p.isReadableStream(new Response("").body)),At={stream:ws&&(e=>e.body)};Zt&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!At[t]&&(At[t]=p.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new Y(`Response type '${t}' is not supported`,Y.ERR_NOT_SUPPORT,r)})})})(new Response);const qi=async e=>{if(e==null)return 0;if(p.isBlob(e))return e.size;if(p.isSpecCompliantForm(e))return(await new Request(we.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(p.isArrayBufferView(e)||p.isArrayBuffer(e))return e.byteLength;if(p.isURLSearchParams(e)&&(e=e+""),p.isString(e))return(await zi(e)).byteLength},Hi=async(e,t)=>{const n=p.toFiniteNumber(e.getContentLength());return n??qi(t)},Wi=Zt&&(async e=>{let{url:t,method:n,data:r,signal:a,cancelToken:o,timeout:i,onDownloadProgress:c,onUploadProgress:l,responseType:u,headers:d,withCredentials:f="same-origin",fetchOptions:w}=fr(e);u=u?(u+"").toLowerCase():"text";let I=Di([a,o&&o.toAbortSignal()],i),j;const v=I&&I.unsubscribe&&(()=>{I.unsubscribe()});let g;try{if(l&&Bi&&n!=="get"&&n!=="head"&&(g=await Hi(d,r))!==0){let Q=new Request(t,{method:"POST",body:r,duplex:"half"}),ie;if(p.isFormData(r)&&(ie=Q.headers.get("content-type"))&&d.setContentType(ie),Q.body){const[ee,fe]=sn(g,It(nn(l)));r=an(Q.body,on,ee,fe)}}p.isString(f)||(f=f?"include":"omit");const P="credentials"in Request.prototype;j=new Request(t,{...w,signal:I,method:n.toUpperCase(),headers:d.normalize().toJSON(),body:r,duplex:"half",credentials:P?f:void 0});let R=await fetch(j,w);const C=ws&&(u==="stream"||u==="response");if(ws&&(c||C&&v)){const Q={};["status","statusText","headers"].forEach(S=>{Q[S]=R[S]});const ie=p.toFiniteNumber(R.headers.get("content-length")),[ee,fe]=c&&sn(ie,It(nn(c),!0))||[];R=new Response(an(R.body,on,ee,()=>{fe&&fe(),v&&v()}),Q)}u=u||"text";let q=await At[p.findKey(At,u)||"text"](R,e);return!C&&v&&v(),await new Promise((Q,ie)=>{hr(Q,ie,{data:q,headers:Ce.from(R.headers),status:R.status,statusText:R.statusText,config:e,request:j})})}catch(P){throw v&&v(),P&&P.name==="TypeError"&&/Load failed|fetch/i.test(P.message)?Object.assign(new Y("Network Error",Y.ERR_NETWORK,e,j),{cause:P.cause||P}):Y.from(P,P&&P.code,e,j)}}),Ss={http:oi,xhr:$i,fetch:Wi};p.forEach(Ss,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const ln=e=>`- ${e}`,Ji=e=>p.isFunction(e)||e===null||e===!1,xr={getAdapter:e=>{e=p.isArray(e)?e:[e];const{length:t}=e;let n,r;const a={};for(let o=0;o<t;o++){n=e[o];let i;if(r=n,!Ji(n)&&(r=Ss[(i=String(n)).toLowerCase()],r===void 0))throw new Y(`Unknown adapter '${i}'`);if(r)break;a[i||"#"+o]=r}if(!r){const o=Object.entries(a).map(([c,l])=>`adapter ${c} `+(l===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(ln).join(`
`):" "+ln(o[0]):"as no adapter specified";throw new Y("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:Ss};function us(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new et(null,e)}function cn(e){return us(e),e.headers=Ce.from(e.headers),e.data=ds.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),xr.getAdapter(e.adapter||pt.adapter)(e).then(function(r){return us(e),r.data=ds.call(e,e.transformResponse,r),r.headers=Ce.from(r.headers),r},function(r){return ur(r)||(us(e),r&&r.response&&(r.response.data=ds.call(e,e.transformResponse,r.response),r.response.headers=Ce.from(r.response.headers))),Promise.reject(r)})}const yr="1.11.0",es={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{es[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const dn={};es.transitional=function(t,n,r){function a(o,i){return"[Axios v"+yr+"] Transitional option '"+o+"'"+i+(r?". "+r:"")}return(o,i,c)=>{if(t===!1)throw new Y(a(i," has been removed"+(n?" in "+n:"")),Y.ERR_DEPRECATED);return n&&!dn[i]&&(dn[i]=!0,console.warn(a(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,c):!0}};es.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function Gi(e,t,n){if(typeof e!="object")throw new Y("options must be an object",Y.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const o=r[a],i=t[o];if(i){const c=e[o],l=c===void 0||i(c,o,e);if(l!==!0)throw new Y("option "+o+" must be "+l,Y.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new Y("Unknown option "+o,Y.ERR_BAD_OPTION)}}const Tt={assertOptions:Gi,validators:es},Ae=Tt.validators;let Je=class{constructor(t){this.defaults=t||{},this.interceptors={request:new en,response:new en}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let a={};Error.captureStackTrace?Error.captureStackTrace(a):a=new Error;const o=a.stack?a.stack.replace(/^.+\n/,""):"";try{r.stack?o&&!String(r.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+o):r.stack=o}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Ge(this.defaults,n);const{transitional:r,paramsSerializer:a,headers:o}=n;r!==void 0&&Tt.assertOptions(r,{silentJSONParsing:Ae.transitional(Ae.boolean),forcedJSONParsing:Ae.transitional(Ae.boolean),clarifyTimeoutError:Ae.transitional(Ae.boolean)},!1),a!=null&&(p.isFunction(a)?n.paramsSerializer={serialize:a}:Tt.assertOptions(a,{encode:Ae.function,serialize:Ae.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Tt.assertOptions(n,{baseUrl:Ae.spelling("baseURL"),withXsrfToken:Ae.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&p.merge(o.common,o[n.method]);o&&p.forEach(["delete","get","head","post","put","patch","common"],j=>{delete o[j]}),n.headers=Ce.concat(i,o);const c=[];let l=!0;this.interceptors.request.forEach(function(v){typeof v.runWhen=="function"&&v.runWhen(n)===!1||(l=l&&v.synchronous,c.unshift(v.fulfilled,v.rejected))});const u=[];this.interceptors.response.forEach(function(v){u.push(v.fulfilled,v.rejected)});let d,f=0,w;if(!l){const j=[cn.bind(this),void 0];for(j.unshift(...c),j.push(...u),w=j.length,d=Promise.resolve(n);f<w;)d=d.then(j[f++],j[f++]);return d}w=c.length;let I=n;for(f=0;f<w;){const j=c[f++],v=c[f++];try{I=j(I)}catch(g){v.call(this,g);break}}try{d=cn.call(this,I)}catch(j){return Promise.reject(j)}for(f=0,w=u.length;f<w;)d=d.then(u[f++],u[f++]);return d}getUri(t){t=Ge(this.defaults,t);const n=mr(t.baseURL,t.url,t.allowAbsoluteUrls);return lr(n,t.params,t.paramsSerializer)}};p.forEach(["delete","get","head","options"],function(t){Je.prototype[t]=function(n,r){return this.request(Ge(r||{},{method:t,url:n,data:(r||{}).data}))}});p.forEach(["post","put","patch"],function(t){function n(r){return function(o,i,c){return this.request(Ge(c||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}Je.prototype[t]=n(),Je.prototype[t+"Form"]=n(!0)});let Vi=class br{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(a=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](a);r._listeners=null}),this.promise.then=a=>{let o;const i=new Promise(c=>{r.subscribe(c),o=c}).then(a);return i.cancel=function(){r.unsubscribe(o)},i},t(function(o,i,c){r.reason||(r.reason=new et(o,i,c),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new br(function(a){t=a}),cancel:t}}};function Yi(e){return function(n){return e.apply(null,n)}}function Xi(e){return p.isObject(e)&&e.isAxiosError===!0}const Ns={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Ns).forEach(([e,t])=>{Ns[t]=e});function jr(e){const t=new Je(e),n=Xn(Je.prototype.request,t);return p.extend(n,Je.prototype,t,{allOwnKeys:!0}),p.extend(n,t,null,{allOwnKeys:!0}),n.create=function(a){return jr(Ge(e,a))},n}const pe=jr(pt);pe.Axios=Je;pe.CanceledError=et;pe.CancelToken=Vi;pe.isCancel=ur;pe.VERSION=yr;pe.toFormData=Qt;pe.AxiosError=Y;pe.Cancel=pe.CanceledError;pe.all=function(t){return Promise.all(t)};pe.spread=Yi;pe.isAxiosError=Xi;pe.mergeConfig=Ge;pe.AxiosHeaders=Ce;pe.formToJSON=e=>dr(p.isHTMLForm(e)?new FormData(e):e);pe.getAdapter=xr.getAdapter;pe.HttpStatusCode=Ns;pe.default=pe;const{Axios:$l,AxiosError:Dl,CanceledError:Fl,isCancel:Ml,CancelToken:Ul,VERSION:zl,all:Bl,Cancel:ql,isAxiosError:Hl,spread:Wl,toFormData:Jl,AxiosHeaders:Gl,HttpStatusCode:Vl,formToJSON:Yl,getAdapter:Xl,mergeConfig:Kl}=pe,Ki="http://localhost:8000/api/v1",Ee=pe.create({baseURL:Ki,timeout:3e4,headers:{"Content-Type":"application/json"}});Ee.interceptors.request.use(e=>{var t;return console.log("API Request:",(t=e.method)==null?void 0:t.toUpperCase(),e.url),e},e=>(console.error("API Request Error:",e),Promise.reject(e)));Ee.interceptors.response.use(e=>(console.log("API Response:",e.status,e.config.url),e),e=>{var t,n,r,a,o;return console.error("API Response Error:",{status:(t=e.response)==null?void 0:t.status,statusText:(n=e.response)==null?void 0:n.statusText,data:(r=e.response)==null?void 0:r.data,url:(a=e.config)==null?void 0:a.url,method:(o=e.config)==null?void 0:o.method,message:e.message}),Promise.reject(e)});class Qi{async analyzeLines(t){try{console.log("发送线条分析请求:",{canvas_data_length:t.canvas_data.length,paths_count:t.paths.length,paths_sample:t.paths.slice(0,2)});const n=await Ee.post("/games/analyze-lines",t);return console.log("线条分析响应:",n.data),n.data}catch(n){throw console.error("线条分析失败:",n),console.error("请求数据:",t),new Error("线条分析失败，请稍后重试")}}async generateAIImage(t){try{console.log("发送AI生图请求");const n=await Ee.post("/games/generate-ai-image",t);return console.log("AI生图响应:",n.data),n.data}catch(n){return console.error("AI生图失败:",n),{status:"failed",error:"AI生图失败，请稍后重试"}}}async getGameLevels(){try{return(await Ee.get("/games/levels")).data}catch(t){throw console.error("获取游戏级别失败:",t),new Error("获取游戏级别失败")}}async getGameLevel(t){try{return(await Ee.get(`/games/levels/${t}`)).data}catch(n){throw console.error("获取级别详情失败:",n),new Error("获取级别详情失败")}}async getFamousArtworks(){try{return(await Ee.get("/games/artworks")).data}catch(t){throw console.error("获取名画列表失败:",t),new Error("获取名画列表失败")}}async getArtworkDetails(t){try{return(await Ee.get(`/games/artworks/${t}`)).data.data}catch(n){throw console.error("获取名画详情失败:",n),new Error("获取名画详情失败")}}async startGameSession(t,n=1,r){try{return(await Ee.post("/games/sessions/start",{level:t,stage:n,user_id:r})).data}catch(a){throw console.error("开始游戏会话失败:",a),new Error("开始游戏会话失败")}}async submitDrawing(t,n){try{return(await Ee.post(`/games/sessions/${t}/submit`,n)).data}catch(r){throw console.error("提交作品失败:",r),new Error("提交作品失败")}}}const un=new Qi,{Title:wt,Text:ve}=Pe,Zi=({onBack:e,onComplete:t})=>{const[n,r]=h.useState(!1),[a,o]=h.useState(""),[i,c]=h.useState(null),[l,u]=h.useState(null),[d,f]=h.useState(!1),[w,I]=h.useState([]),[j,v]=h.useState(0),g=async C=>{var q;o(C),f(!0);try{const Q=await un.analyzeLines({canvas_data:C,paths:w});if(u(Q),r(!0),f(!1),X.success("风格分析完成！AI正在为您创作艺术作品..."),((q=Q.ai_generated_image)==null?void 0:q.status)==="generating")try{const ie=await un.generateAIImage({canvas_data:C,paths:w});u(ee=>ee&&{...ee,ai_generated_image:ie}),ie.status==="success"?X.success("AI艺术作品生成完成！"):X.warning("AI生图暂时不可用，但风格分析已完成")}catch(ie){console.error("AI生图失败:",ie),u(ee=>ee&&{...ee,ai_generated_image:{status:"failed",error:"AI生图服务暂时不可用"}}),X.warning("AI生图暂时不可用，但风格分析已完成")}}catch(Q){console.error("线条分析失败:",Q),X.error("分析失败，但作品已保存"),r(!0),f(!1)}},P=()=>{a?t():X.warning("请先保存您的作品")},R=()=>{r(!1),o(""),u(null),I([]),v(C=>C+1),i&&i()};return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-orange-50 via-pink-50 to-purple-50",children:s.jsx("div",{className:"max-w-6xl mx-auto px-4 py-8",children:s.jsxs(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[!n&&s.jsx(ce,{className:"mb-6 shadow-sm",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx(wt,{level:2,className:"mb-2 text-purple-600",children:"自由绘画"}),s.jsx(ve,{className:"text-lg text-gray-600",children:"发挥您的想象力，创作属于您的艺术作品。没有对错，只有表达。让您的创意自由流淌吧！"})]}),s.jsx(H,{icon:s.jsx(Fe,{}),onClick:e,size:"large",className:"h-12 px-6",children:"返回"})]})}),n&&s.jsx(re.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5},className:"mb-6",children:s.jsx(ce,{className:"bg-gradient-to-r from-green-50 to-blue-50 border-green-200",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(at,{className:"text-2xl text-white"})}),s.jsx(wt,{level:3,className:"text-green-600 mb-3",children:"🎉 创作完成！"}),s.jsx(ve,{className:"text-lg text-gray-700 block mb-6",children:"太棒了！您已经完成了一幅美丽的作品。每一笔都是您创意的体现！"}),d&&s.jsxs("div",{className:"mb-6",children:[s.jsx(As,{size:"large"}),s.jsx(ve,{className:"block mt-2 text-gray-600",children:"正在分析您的线条风格..."})]}),l&&s.jsxs("div",{className:"mb-6 p-4 bg-white rounded-lg border",children:[s.jsxs(wt,{level:4,className:"text-blue-600 mb-3",children:[s.jsx(Xe,{className:"mr-2"}),"您的作品风格分析"]}),s.jsx("div",{className:"w-full",children:s.jsxs("div",{className:"grid grid-cols-4 gap-4",children:[s.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded",children:[s.jsx(ve,{className:"block text-sm text-gray-600 mb-1",children:"复杂度"}),s.jsxs(ve,{className:"font-medium text-lg",children:[(l.line_features.complexity*100).toFixed(1),"%"]})]}),s.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded",children:[s.jsx(ve,{className:"block text-sm text-gray-600 mb-1",children:"节奏感"}),s.jsxs(ve,{className:"font-medium text-lg",children:[(l.line_features.rhythm*100).toFixed(1),"%"]})]}),s.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded",children:[s.jsx(ve,{className:"block text-sm text-gray-600 mb-1",children:"平滑度"}),s.jsxs(ve,{className:"font-medium text-lg",children:[(l.line_features.smoothness*100).toFixed(1),"%"]})]}),s.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded",children:[s.jsx(ve,{className:"block text-sm text-gray-600 mb-1",children:"曲线特征"}),s.jsx(ve,{className:"font-medium text-lg",children:l.line_features.dominant_curves?"是":"否"})]})]})}),l.ai_generated_image&&s.jsxs("div",{className:"mt-6 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200",children:[s.jsx(wt,{level:4,className:"text-purple-600 mb-3 text-center",children:"🎨 AI为您创作的艺术作品"}),l.ai_generated_image.status==="success"&&l.ai_generated_image.image_url&&s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsxs("div",{className:"text-center",children:[s.jsx(ve,{className:"block text-lg font-medium text-gray-700 mb-3",children:"📝 您的原创作品"}),s.jsx(Os,{src:a,alt:"用户原创作品",className:"rounded-lg shadow-lg max-w-full h-auto border-2 border-gray-200",style:{maxHeight:"300px"},preview:{mask:s.jsx("div",{className:"text-white",children:"🔍 查看原图"})}})]}),s.jsxs("div",{className:"text-center",children:[s.jsx(ve,{className:"block text-lg font-medium text-purple-600 mb-3",children:"🤖 AI 创作版本"}),s.jsx(Os,{src:l.ai_generated_image.image_url,alt:"AI生成的艺术作品",className:"rounded-lg shadow-lg max-w-full h-auto border-2 border-purple-200",style:{maxHeight:"300px"},preview:{mask:s.jsx("div",{className:"text-white",children:"🔍 查看AI作品"})}})]})]}),l.ai_generated_image.status==="failed"&&s.jsxs("div",{className:"text-center text-gray-500",children:[s.jsx(ve,{children:"😔 AI创作暂时不可用"}),l.ai_generated_image.error&&s.jsx(ve,{className:"text-xs block mt-1",children:l.ai_generated_image.error})]}),l.ai_generated_image.status==="generating"&&s.jsx("div",{className:"text-center py-8",children:s.jsxs("div",{className:"relative",children:[s.jsx(As,{size:"large"}),s.jsxs("div",{className:"mt-4",children:[s.jsx(ve,{className:"block text-lg font-medium text-purple-600",children:"🎨 AI正在为您创作艺术作品"}),s.jsx(ve,{className:"block mt-2 text-gray-600",children:"请稍候，这可能需要10-30秒..."}),s.jsxs("div",{className:"mt-3 flex justify-center items-center space-x-2",children:[s.jsx("div",{className:"w-2 h-2 bg-purple-400 rounded-full animate-bounce"}),s.jsx("div",{className:"w-2 h-2 bg-pink-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),s.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]})]})})]})]}),s.jsxs(me,{size:"large",children:[s.jsx(H,{type:"primary",size:"large",icon:s.jsx(at,{}),onClick:P,className:"h-12 px-8 bg-green-500 hover:bg-green-600",children:"完成练习"}),s.jsx(H,{size:"large",icon:s.jsx(Ke,{}),onClick:R,className:"h-12 px-8",children:"重新创作"})]})]})})}),!n&&s.jsx("div",{className:"transparent-canvas-card",children:s.jsx(Yn,{width:768,height:1024,onSave:g,hideCanvas:!1,onClearRef:c,isCompleted:!1,onPathsChange:I},j)})]})})})},{Title:St,Text:Nt}=Pe,el=[{id:"triangle",name:"三角形",description:"绘制一个三角形",instruction:"画三条直线，让它们相互连接形成三角形",difficulty:1,type:"straight"},{id:"square",name:"正方形",description:"绘制一个正方形",instruction:"画四条相等的直线，形成四个直角",difficulty:1,type:"straight"},{id:"rectangle",name:"长方形",description:"绘制一个长方形",instruction:"画四条直线，对边相等且平行",difficulty:1,type:"straight"},{id:"pentagon",name:"五边形",description:"绘制一个五边形",instruction:"画五条直线，形成一个封闭的五边形",difficulty:2,type:"straight"},{id:"hexagon",name:"六边形",description:"绘制一个六边形",instruction:"画六条直线，形成一个封闭的六边形",difficulty:2,type:"straight"},{id:"circle",name:"圆形",description:"绘制一个圆形",instruction:"画一条连续的曲线，让起点和终点相接",difficulty:1,type:"curved"},{id:"oval",name:"椭圆形",description:"绘制一个椭圆形",instruction:"画一个拉长的圆形，像鸡蛋的形状",difficulty:2,type:"curved"},{id:"heart",name:"心形",description:"绘制一个心形",instruction:"画两个圆弧在顶部，底部汇聚成一个点",difficulty:3,type:"curved"},{id:"star",name:"星形",description:"绘制一个五角星",instruction:"画五个尖角，每个尖角之间用直线或曲线连接",difficulty:3,type:"curved"}],hn=({onBack:e,onComplete:t,shapeType:n})=>{const[r,a]=h.useState(0),[o,i]=h.useState(!1),[c,l]=h.useState(0),[u,d]=h.useState(!1),f=el.filter(q=>q.type===n),w=f[r],I=q=>{const ie=(4-w.difficulty)*10,ee=Math.floor(Math.random()*20),fe=Math.min(100,60+ie+ee);l(fe),i(!0),X.success(`太棒了！您的${w.name}得分：${fe}分`)},j=()=>{t(c)},v=()=>{r<f.length-1?(a(q=>q+1),i(!1),l(0),d(!1)):j()},g=()=>{i(!1),l(0)},P=()=>{d(!u)},R=q=>{switch(q){case 1:return"green";case 2:return"blue";case 3:return"purple";default:return"gray"}},C=q=>{switch(q){case 1:return"简单";case 2:return"中等";case 3:return"困难";default:return"未知"}};return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-green-50 via-blue-50 to-purple-50",children:s.jsx("div",{className:"max-w-6xl mx-auto px-4 py-8",children:s.jsxs(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[s.jsx(ce,{className:"mb-6 shadow-sm",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsxs(St,{level:2,className:"mb-2 text-purple-600",children:[n==="straight"?"直线图形":"曲线图形","绘制"]}),s.jsx(Nt,{className:"text-lg text-gray-600",children:n==="straight"?"学习绘制各种直线构成的几何图形":"练习绘制优美的曲线图形"})]}),s.jsx(H,{icon:s.jsx(Fe,{}),onClick:e,size:"large",className:"h-12 px-6",children:"返回"})]})}),s.jsx(ce,{className:"mb-6 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[s.jsxs(St,{level:4,className:"text-blue-600 mb-0",children:[w.name," (",r+1,"/",f.length,")"]}),s.jsx(ze,{color:R(w.difficulty),children:C(w.difficulty)})]}),s.jsx(Nt,{className:"text-gray-700 block mb-2",children:w.description}),u&&s.jsx(re.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},transition:{duration:.3},children:s.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-2",children:s.jsxs(Nt,{className:"text-yellow-800",children:["💡 提示：",w.instruction]})})})]}),s.jsxs(H,{icon:s.jsx(Rr,{}),onClick:P,type:u?"primary":"default",children:[u?"隐藏":"显示","提示"]})]})}),s.jsx(ce,{className:"mb-6 bg-gradient-to-r from-green-50 to-blue-50 border-green-200",children:s.jsxs("div",{className:"text-center",children:[s.jsx(St,{level:4,className:"text-green-600 mb-3",children:"🎯 绘画要点"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-700",children:[s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-green-400 rounded-full"}),s.jsx("span",{children:n==="straight"?"保持线条笔直":"保持曲线流畅"})]}),s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-blue-400 rounded-full"}),s.jsx("span",{children:"注意图形的对称性"})]}),s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx("span",{className:"w-2 h-2 bg-purple-400 rounded-full"}),s.jsx("span",{children:"确保线条闭合"})]})]})]})}),s.jsx("div",{className:"transparent-canvas-card",children:s.jsx(Yn,{width:800,height:600,onSave:I})}),o&&s.jsx(re.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5},className:"mt-6",children:s.jsx(ce,{className:"bg-gradient-to-r from-green-50 to-blue-50 border-green-200",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(at,{className:"text-2xl text-white"})}),s.jsxs(St,{level:3,className:"text-green-600 mb-3",children:["🎉 ",w.name,"绘制完成！"]}),s.jsxs("div",{className:"mb-4",children:[s.jsx(Nt,{className:"text-lg text-gray-700 block mb-2",children:"绘制评分"}),s.jsx(gn,{percent:c,strokeColor:{"0%":"#108ee9","100%":"#87d068"},className:"max-w-md mx-auto"})]}),s.jsxs(me,{size:"large",children:[r<f.length-1?s.jsx(H,{type:"primary",size:"large",icon:s.jsx(at,{}),onClick:v,className:"h-12 px-8 bg-blue-500 hover:bg-blue-600",children:"下一个图形"}):s.jsx(H,{type:"primary",size:"large",icon:s.jsx(at,{}),onClick:j,className:"h-12 px-8 bg-green-500 hover:bg-green-600",children:"完成练习"}),s.jsx(H,{size:"large",icon:s.jsx(Ke,{}),onClick:g,className:"h-12 px-8",children:"重新绘制"})]})]})})})]})})})},Ye={accuracyThreshold:.7,tolerance:20,animationSpeed:.6,staticTraceDisplayTime:3e3},nt={minCanvasSize:350,maxCanvasSize:700,sizeRatios:{small:.45,medium:.5,large:.55}},mn=({guidePaths:e,originalCanvasSize:t,levelStage:n="L1-3",onPathComplete:r})=>{const a=n==="L1-4",o=h.useRef(null),[i,c]=h.useState(0),[l,u]=h.useState(!1),[d,f]=h.useState([]),[w,I]=h.useState([]),[j,v]=h.useState({}),[g,P]=h.useState(0),[R,C]=h.useState("entering"),[q,Q]=h.useState(.1),[ie,ee]=h.useState(1),[fe,S]=h.useState(0),[D,N]=h.useState(1),[k,V]=h.useState(""),[G,te]=h.useState(!1),[de,K]=h.useState(null),[J,y]=h.useState({width:800,height:600}),[$,W]=h.useState(""),[oe,Z]=h.useState(""),[B,le]=h.useState(null),[ne,Se]=h.useState("guide-animation"),[ye,$e]=h.useState(null);h.useEffect(()=>{const m=new Image;m.onload=()=>{K(m)},m.src=`/${n}/arrow.png`},[n]),h.useEffect(()=>{W(""),Z("");const m=E=>new Promise((T,L)=>{const U=new Image;U.onload=()=>T(U),U.onerror=()=>L(new Error(`Failed to load ${E}`)),U.src=E});(async()=>{const E=["png","jpg","jpeg"];let T=null,L="";for(const O of E)try{const _=`/${n}/bg.${O}`;T=await m(_),L=_;break}catch{}if(T&&L){W(L);const _=(()=>{const ae=window.innerHeight;let he;return ae<=600?he=ae*nt.sizeRatios.small:ae<=900?he=ae*nt.sizeRatios.medium:he=ae*nt.sizeRatios.large,he=Math.max(nt.minCanvasSize,he),he=Math.min(nt.maxCanvasSize,he),he})(),A=T.width/T.height;let F,se;A>=1?(F=_,se=_/A,se>_&&(se=_,F=_*A)):(se=_,F=_*A,F>_&&(F=_,se=_/A)),y({width:Math.round(F),height:Math.round(se)})}else console.error("Failed to load canvas background image in any format"),y({width:400,height:300});const U=["jpg","jpeg","png"];for(const O of U)try{const _=`/${n}/full.${O}`;await m(_),Z(_);break}catch{}})()},[n]);const ts=m=>m,[De,ss]=h.useState([]);h.useEffect(()=>{if(e.length>0){const m=ts(e);ss(m);let b=0;for(;b<m.length;){const E=m[b];if(!E||E.points.length>=2)break;I(T=>[...T,E.id]),r(E.id,[]),b++}b!==i&&c(b)}},[e]);const ue=De[i];h.useEffect(()=>{De.length>0&&ue&&(B&&(clearTimeout(B),le(null)),a&&ye&&(clearTimeout(ye),$e(null)),f([]),P(0),a&&Se("guide-animation"))},[De,i,ue]),h.useEffect(()=>{if(R==="entering"){const m=Date.now(),b=3e3,E=()=>{const T=Date.now()-m,L=Math.min(T/b,1),O=(A=>A<.5?4*A*A*A:1-Math.pow(-2*A+2,3)/2)(L),_=.1+(1-.1)*O;Q(_),L<1?requestAnimationFrame(E):C("instruction")};requestAnimationFrame(E)}},[R]),h.useEffect(()=>{if(R==="instruction"){const m="请根据黄色引导线，描绘名画。您需要紧跟画笔，并且紧跟黄线描绘";te(!0),V("");let b=0;const E=100,T=()=>{b<m.length?(V(m.slice(0,b+1)),b++,setTimeout(T,E)):setTimeout(()=>{te(!1),C("active")},2e3)};setTimeout(T,500)}},[R]),h.useEffect(()=>{if(R==="completing"){const m=Date.now(),b=2e3,E=()=>{const T=Date.now()-m,L=Math.min(T/b,1);ee(1-L),S(L),L<1?requestAnimationFrame(E):C("showing")};requestAnimationFrame(E)}},[R]),h.useEffect(()=>{if(R==="showing"){if(a)return;{const m=setTimeout(()=>{C("exiting")},3e3);return()=>clearTimeout(m)}}},[R,a]),h.useEffect(()=>{if(R==="exiting"){if(a)return;const m=Date.now(),b=3e3,E=()=>{const T=Date.now()-m,L=Math.min(T/b,1),O=(F=>F<.5?2*F*F:-1+(4-2*F)*F)(L);N(1-O*.85);const _=.9;if(L>_){const F=(L-_)/(1-_);S(1-F)}else S(1);const A=.3;if(L>A)ee(0);else{const F=L/A;ee(1-F)}L<1&&requestAnimationFrame(E)};requestAnimationFrame(E)}},[R,a]),h.useEffect(()=>{const m=o.current;if(!m)return;const b=U=>{var O;(O=U.target)!=null&&O.closest("canvas")&&U.preventDefault()},E=U=>{var he;const O=((he=document.querySelector("[data-guide-stage]"))==null?void 0:he.getAttribute("data-guide-stage"))||ne;if(U.preventDefault(),U.stopPropagation(),a&&(O==="guide-animation"||O==="static-trace"))return;B&&(clearTimeout(B),le(null)),u(!0);const _=U.touches[0],A=m.getBoundingClientRect(),F=m.width/A.width,se=m.height/A.height,ae={x:(_.clientX-A.left)*F,y:(_.clientY-A.top)*se};f([ae])},T=U=>{var ge;U.preventDefault(),U.stopPropagation();const O=((ge=document.querySelector("[data-guide-stage]"))==null?void 0:ge.getAttribute("data-guide-stage"))||ne;if(a&&(O==="guide-animation"||O==="static-trace")||!l)return;const _=U.touches[0],A=m.getBoundingClientRect(),F=m.width/A.width,se=m.height/A.height,ae={x:(_.clientX-A.left)*F,y:(_.clientY-A.top)*se},he=[...d,ae];f(he)},L=U=>{U.preventDefault(),U.stopPropagation(),x()};return m.addEventListener("touchstart",E,{passive:!1}),m.addEventListener("touchmove",T,{passive:!1}),m.addEventListener("touchend",L,{passive:!1}),document.addEventListener("touchmove",b,{passive:!1}),document.addEventListener("wheel",b,{passive:!1}),()=>{m.removeEventListener("touchstart",E),m.removeEventListener("touchmove",T),m.removeEventListener("touchend",L),document.removeEventListener("touchmove",b),document.removeEventListener("wheel",b)}},[l,d,B,ne,a]),h.useEffect(()=>()=>{B&&clearTimeout(B),a&&ye&&clearTimeout(ye)},[B,ye,a]),h.useEffect(()=>{if(!ue||R!=="active")return;let m,b=!0;if(a)ne==="guide-animation"&&(()=>{if(!b||ne!=="guide-animation")return;const T=ue.points.length,L=3e3,U=Math.max(0,(T-10)*150),O=(L+U)/Ye.animationSpeed,_=Date.now(),A=()=>{if(!b||ne!=="guide-animation")return;const se=Date.now()-_;let ae=Math.min(se/O,1);const ge=(be=>be<.5?2*be*be:-1+(4-2*be)*be)(ae);P(ge),ae<1?m=requestAnimationFrame(A):b&&ue&&!w.includes(ue.id)&&(Se("static-trace"),P(1))};m=requestAnimationFrame(A)})();else{const E=()=>{if(!b)return;const T=ue.points.length,L=3e3,U=Math.max(0,(T-10)*150),O=(L+U)/Ye.animationSpeed,_=Date.now(),A=()=>{if(!b)return;const se=Date.now()-_;let ae=Math.min(se/O,1);const ge=(be=>be<.5?2*be*be:-1+(4-2*be)*be)(ae);P(ge),ae<1?m=requestAnimationFrame(A):b&&ue&&!w.includes(ue.id)&&setTimeout(()=>{b&&(P(0),E())},500)};m=requestAnimationFrame(A)};E()}return()=>{b=!1,m&&cancelAnimationFrame(m)}},[i,ue,R,ne,a]),h.useEffect(()=>{if(!(!a||!ue||R!=="active")&&ne==="static-trace"){const m=setTimeout(()=>{w.includes(ue.id)||(Se("waiting-user"),P(0))},Ye.staticTraceDisplayTime);return $e(m),()=>{clearTimeout(m)}}},[ne,ue,a,R,w]),h.useEffect(()=>{const m=o.current;m&&(m.width=J.width,m.height=J.height,m.style.width=`${J.width}px`,m.style.height=`${J.height}px`)},[J]),h.useEffect(()=>{const m=o.current;if(!m)return;const b=m.getContext("2d");if(!b)return;b.clearRect(0,0,m.width,m.height),E();function E(){!ue||!b||(w.forEach(T=>{const L=j[T];L&&L.length>0&&yt(b,L)}),ue&&(a?ne==="guide-animation"?xt(b,ue):ne==="static-trace"&&ns(b,ue):xt(b,ue)),d.length>0&&yt(b,d))}},[ue,d,w,j,g,J,a?ne:null]);const gt=(m,b,E,T,L,U,O=15)=>{const _=T-b,A=L-E;if(!de){const ae=Math.atan2(A,_);m.fillStyle=U,m.strokeStyle=U,m.lineWidth=2,m.beginPath(),m.moveTo(T,L),m.lineTo(T-O*Math.cos(ae-Math.PI/5),L-O*Math.sin(ae-Math.PI/5)),m.lineTo(T-O*Math.cos(ae+Math.PI/5),L-O*Math.sin(ae+Math.PI/5)),m.closePath(),m.fill(),m.stroke();return}const F=Math.atan2(A,_),se=O*2;m.save(),m.translate(T,L),m.rotate(F),m.drawImage(de,-se/2,-se/2,se,se),m.restore()},xt=(m,b)=>{if(b.points.length<2)return;const E=(t==null?void 0:t.width)||800,T=(t==null?void 0:t.height)||600,L=J.width/E,U=J.height/T,O=b.points.map(F=>({x:F.x*L,y:F.y*U}));m.strokeStyle="#FFD700",m.lineWidth=8,m.lineCap="round",m.lineJoin="round",m.setLineDash([]);const _=O.length,A=Math.floor(_*g);if(m.beginPath(),A>0){m.moveTo(O[0].x,O[0].y);for(let F=1;F<A;F++)m.lineTo(O[F].x,O[F].y);if(A<_){const F=O[A-1],se=O[A],ae=_*g-A,he=F.x+(se.x-F.x)*ae,ge=F.y+(se.y-F.y)*ae;m.lineTo(he,ge)}}if(m.stroke(),m.setLineDash([]),g>.1&&A>0){let F,se,ae,he;if(A<_){const ge=O[A-1],be=O[A],Is=_*g-A;F=ge.x+(be.x-ge.x)*Is,se=ge.y+(be.y-ge.y)*Is,ae=ge.x,he=ge.y}else F=O[_-1].x,se=O[_-1].y,ae=O[Math.max(0,_-2)].x,he=O[Math.max(0,_-2)].y;gt(m,ae,he,F,se,"#FFD700",20)}},ns=(m,b)=>{if(b.points.length<2)return;const E=(t==null?void 0:t.width)||800,T=(t==null?void 0:t.height)||600,L=J.width/E,U=J.height/T,O=b.points.map(_=>({x:_.x*L,y:_.y*U}));m.strokeStyle="#888888",m.lineWidth=4,m.lineCap="round",m.lineJoin="round",m.setLineDash([10,5]),m.beginPath(),m.moveTo(O[0].x,O[0].y);for(let _=1;_<O.length;_++)m.lineTo(O[_].x,O[_].y);m.stroke(),m.setLineDash([])},yt=(m,b)=>{b.length<2||(m.strokeStyle="#000000",m.lineWidth=4,m.lineCap="round",m.lineJoin="round",rs(m,b))},rs=(m,b)=>{if(!(b.length<2)){if(m.beginPath(),m.moveTo(b[0].x,b[0].y),b.length===2)m.lineTo(b[1].x,b[1].y);else{for(let T=1;T<b.length-1;T++){const L=b[T],U=b[T+1],O=(L.x+U.x)/2,_=(L.y+U.y)/2;m.quadraticCurveTo(L.x,L.y,O,_)}const E=b[b.length-1];m.lineTo(E.x,E.y)}m.stroke()}},tt=m=>{const b=o.current;if(!b)return{x:0,y:0};const E=b.getBoundingClientRect(),T=b.width/E.width,L=b.height/E.height,U=(m.clientX-E.left)*T,O=(m.clientY-E.top)*L;return{x:U,y:O}},as=m=>{if(m.preventDefault(),m.stopPropagation(),a&&(ne==="guide-animation"||ne==="static-trace"))return;B&&(clearTimeout(B),le(null)),u(!0);const b=tt(m);f([b])},os=m=>{if(m.preventDefault(),m.stopPropagation(),a&&(ne==="guide-animation"||ne==="static-trace")||!l)return;const b=tt(m),E=[...d,b];f(E)},x=m=>{if(m&&(m.preventDefault(),m.stopPropagation()),!l||!ue)return;if(u(!1),M(d,ue.points)>Ye.accuracyThreshold)if(B&&(clearTimeout(B),le(null)),r(ue.id,d),I(E=>[...E,ue.id]),v(E=>({...E,[ue.id]:d})),i<De.length-1){let E=i+1;for(;E<De.length;){const T=De[E];if(!T||T.points.length>=2)break;I(L=>[...L,T.id]),r(T.id,[]),E++}E<De.length?(c(E),f([]),P(0)):setTimeout(()=>{C("completing")},500)}else setTimeout(()=>{C("completing")},500);else f([]),a&&(Se("guide-animation"),P(0),ye&&(clearTimeout(ye),$e(null)))},z=m=>{if(m.length<2)return m;const b=(t==null?void 0:t.width)||800,E=(t==null?void 0:t.height)||600,T=J.width/b,L=J.height/E,U=m.map(A=>({x:A.x*T,y:A.y*L})),O=[],_=5;for(let A=0;A<U.length-1;A++){const F=U[A],se=U[A+1],ae=Math.sqrt(Math.pow(se.x-F.x,2)+Math.pow(se.y-F.y,2)),he=Math.ceil(ae/_);for(let ge=0;ge<=he;ge++){const be=ge/he;O.push({x:F.x+(se.x-F.x)*be,y:F.y+(se.y-F.y)*be})}}return O},M=(m,b)=>{if(m.length===0||b.length===0||m.length<2)return 0;const E=z(b);let T=0,L=0;for(const F of m){let se=1/0;for(const ae of E){const he=Math.sqrt(Math.pow(F.x-ae.x,2)+Math.pow(F.y-ae.y,2));se=Math.min(se,he)}se<=Ye.tolerance&&(T+=se,L++)}if(L===0)return 0;const U=T/L,O=L/m.length;return Math.max(0,1-U/Ye.tolerance)*O};return s.jsx("div",{className:"trace-practice min-h-screen","data-guide-stage":ne,style:{background:"transparent"},children:s.jsx("div",{className:"flex items-center justify-center min-h-screen",children:s.jsxs("div",{className:"relative",style:{transform:"translateY(-120px) translateX(10px)"},children:[G&&s.jsx("div",{className:"absolute -top-20 left-1/2 transform -translate-x-1/2 z-20",children:s.jsx("div",{className:"bg-black bg-opacity-75 text-white px-6 py-3 rounded-lg shadow-lg",children:s.jsxs("div",{className:"text-lg font-medium text-center whitespace-nowrap",children:[k,s.jsx("span",{className:"animate-pulse",children:"|"})]})})}),s.jsxs("div",{className:"relative flex items-center justify-center",children:[$&&s.jsx("div",{style:{width:`${J.width}px`,height:`${J.height}px`,transform:`scale(${q})`,opacity:ie,transition:"none"},children:s.jsx("img",{src:$,alt:"背景图片",className:"object-contain w-full h-full"})}),s.jsx("div",{className:"absolute inset-0 flex items-center justify-center",style:{transform:`scale(${q})`,opacity:ie,transition:"none"},children:s.jsx("canvas",{ref:o,width:J.width,height:J.height,className:"bg-transparent",style:{touchAction:"none",userSelect:"none",width:`${J.width}px`,height:`${J.height}px`,position:"relative",margin:0,padding:0,border:"none",outline:"none",display:"block"},onMouseDown:R==="active"?as:void 0,onMouseMove:R==="active"?os:void 0,onMouseUp:R==="active"?x:void 0,onMouseLeave:R==="active"?()=>u(!1):void 0})}),(R==="completing"||R==="showing"||R==="exiting")&&oe&&s.jsx("div",{className:"absolute inset-0 flex flex-col items-center justify-center",style:{opacity:fe,transform:`scale(${D})`,transition:"none"},children:s.jsx("img",{src:oe,alt:"完整图片",className:"object-contain",style:{width:`${J.width}px`,height:`${J.height}px`},onError:m=>{console.error("奖励图片加载失败"),m.currentTarget.style.display="none"}})})]})]})})})},hs=()=>{const e=Me(),{level:t,stage:n}=Ea(),[r,a]=h.useState("level-select"),[o,i]=h.useState(""),[c,l]=h.useState(""),[u,d]=h.useState("free-draw"),[f,w]=h.useState(()=>xo()),[I,j]=h.useState(()=>yo()),[v,g]=h.useState(null),[P,R]=h.useState(!1),C=Be.useCallback(async(K,J)=>{var y,$,W;R(!0);try{if(K==="trace"&&J)try{console.log(`尝试加载轨迹文件: /${J}/trace.json`);const B=await fetch(`/${J}/trace.json`);if(!B.ok)throw new Error(`HTTP ${B.status}: 无法加载 ${J}/trace.json`);const le=B.headers.get("content-type");if(!le||!le.includes("application/json"))throw new Error(`文件不是JSON格式: ${le}`);const ne=await B.json();console.log("成功加载轨迹数据:",ne);const Se=((y=ne.strokes)==null?void 0:y.map((ye,$e)=>({id:`path_${$e}`,points:ye.points||[]})))||[];console.log(`转换后的引导路径数量: ${Se.length}`),g({sessionId:`trace_${Date.now()}`,guidePaths:Se,guideImage:ne.backgroundImage||"",artworkName:"trace",canvasSize:ne.canvasSize||{width:800,height:600},levelStage:J}),R(!1),console.log("轨迹练习数据设置成功");return}catch(B){if(console.warn(`从文件加载失败: ${(B==null?void 0:B.message)||B}`),console.warn(`文件路径: /${J}/trace.json`),($=B==null?void 0:B.message)!=null&&$.includes("404")||(W=B==null?void 0:B.message)!=null&&W.includes("HTTP 404")){X.warning(`${J} 目录下没有 trace.json 文件，该关卡暂不支持图像描线功能`),R(!1);return}console.warn("尝试使用后端API作为备选方案")}if(K==="trace")throw new Error("trace类型轨迹文件加载失败，且没有后端支持");const oe=await fetch("/api/v1/games/trace/start",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({artwork_name:K,difficulty:"medium"})});if(!oe.ok)throw new Error("启动描线练习失败");const Z=await oe.json();if(Z.success)g({sessionId:Z.session_id,guidePaths:Z.guide_paths,guideImage:Z.guide_image,artworkName:Z.artwork_name,canvasSize:Z.canvas_size,levelStage:J});else throw new Error(Z.error||"启动描线练习失败")}catch(oe){console.error("启动描线练习失败:",oe),X.error("启动描线练习失败，请重试")}finally{R(!1)}},[]),q=Be.useCallback(K=>{C("trace",K)},[C]);Be.useEffect(()=>{if(t&&n){let K=n;!n.includes("-")&&t&&(K=`${t}-${n}`),i(t),l(K),a("playing");const J=Q.find($=>$.id===t),y=J==null?void 0:J.stages.find($=>$.id===K);y&&(d(y.type),y.type==="image-trace"&&q(K))}else t?(i(t),a("stage-select")):a("level-select")},[t,n,q]);const Q=Vt,ie=K=>{i(K),a("stage-select"),e(`/game/${K}`)},ee=Be.useCallback(K=>{l(K),a("playing"),e(`/game/${o}/${K}`)},[o,e]),fe=()=>{a("level-select"),i(""),l(""),e("/game")},S=()=>{a("stage-select"),l(""),e(`/game/${o}`)},D=()=>{w(["level-1"]),j(["L1-1"]),localStorage.removeItem("memorybrush-unlocked-levels"),localStorage.removeItem("memorybrush-unlocked-stages"),X.success("游戏进度已重置")},N=async(K,J)=>{if(v)try{const y=await fetch("/api/v1/games/trace/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({session_id:v.sessionId,user_paths:J,completed_path_id:K})});if(!y.ok)throw new Error("提交描线进度失败");(await y.json()).success&&console.log("路径完成:",K)}catch(y){console.error("提交描线进度失败:",y)}},k=()=>{X.success("恭喜！描线练习完成！"),S()},V=()=>{X.success("恭喜完成练习！"),setTimeout(()=>{S()},2e3)},G=K=>{X.success(`恭喜完成！得分：${K}分`),setTimeout(()=>{S()},2e3)},te=()=>{switch(u){case"free-draw":return s.jsx(Zi,{onBack:S,onComplete:V});case"trace":return P?s.jsx("div",{className:"flex items-center justify-center min-h-screen",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"}),s.jsx("p",{className:"mt-4 text-lg",children:"正在准备描线练习..."})]})}):v?s.jsxs("div",{className:"container mx-auto px-4 py-8",children:[s.jsx("div",{className:"mb-4",children:s.jsx("button",{onClick:S,className:"px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600",children:"← 返回阶段选择"})}),s.jsx(mn,{guidePaths:v.guidePaths,guideImage:v.guideImage,originalCanvasSize:v.canvasSize,levelStage:v.levelStage,onPathComplete:N,onAllComplete:k})]}):s.jsx("div",{className:"flex items-center justify-center min-h-screen",children:s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-lg",children:"描线练习数据加载失败"}),s.jsx("button",{onClick:S,className:"mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"返回阶段选择"})]})});case"image-trace":return P?s.jsx("div",{className:"flex items-center justify-center min-h-screen",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"}),s.jsx("p",{className:"mt-4 text-lg",children:"正在从图片抽取线条..."})]})}):v?s.jsxs("div",{className:"container mx-auto px-4 py-8",children:[s.jsx("div",{className:"mb-4",children:s.jsx("button",{onClick:S,className:"px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600",children:"← 返回阶段选择"})}),s.jsx(mn,{guidePaths:v.guidePaths,guideImage:v.guideImage,originalCanvasSize:v.canvasSize,levelStage:v.levelStage,onPathComplete:N,onAllComplete:k})]}):s.jsx("div",{className:"flex items-center justify-center min-h-screen",children:s.jsxs("div",{className:"text-center",children:[s.jsx("p",{className:"text-lg",children:"图像线条抽取失败"}),s.jsx("button",{onClick:S,className:"mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"返回阶段选择"})]})});case"shape-straight":return s.jsx(hn,{onBack:S,onComplete:G,shapeType:"straight"});case"shape-curved":return s.jsx(hn,{onBack:S,onComplete:G,shapeType:"curved"});default:return null}},de=()=>Q.find(K=>K.id===o);return s.jsxs("div",{className:"min-h-screen pt-8",style:{background:"transparent"},children:[r==="level-select"&&s.jsx(go,{onSelectLevel:ie,unlockedLevels:f,onResetProgress:D}),r==="stage-select"&&de()&&s.jsx(bo,{level:de(),onSelectStage:ee,onBack:fe,unlockedStages:I}),r==="playing"&&te()]})},{Title:tl,Paragraph:sl}=Pe,nl=()=>{const e=Me();return s.jsx("div",{className:"min-h-screen",style:{background:"transparent"},children:s.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ce,{className:"text-center p-12 shadow-soft",children:[s.jsxs("div",{className:"mb-8",children:[s.jsx("div",{className:"w-24 h-24 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx(xn,{className:"text-4xl text-white"})}),s.jsx(tl,{level:1,className:"text-blue-600 mb-4",children:"个人资料"}),s.jsxs(sl,{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:["个人资料功能正在开发中，敬请期待！",s.jsx("br",{}),"我们正在为您打造个性化的用户体验。"]})]}),s.jsx(me,{size:"large",children:s.jsx(H,{size:"large",icon:s.jsx(Fe,{}),onClick:()=>e("/"),className:"h-12 px-8 text-lg",children:"返回首页"})})]})})})})},{Title:rl,Paragraph:al}=Pe,ol=()=>{const e=Me();return s.jsx("div",{className:"min-h-screen",style:{background:"transparent"},children:s.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ce,{className:"text-center p-12 shadow-soft",children:[s.jsxs("div",{className:"mb-8",children:[s.jsx("div",{className:"w-24 h-24 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx(ks,{className:"text-4xl text-white"})}),s.jsx(rl,{level:1,className:"text-green-600 mb-4",children:"设置"}),s.jsxs(al,{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:["设置功能正在开发中，敬请期待！",s.jsx("br",{}),"我们正在为您准备贴心的个性化设置选项。"]})]}),s.jsx(me,{size:"large",children:s.jsx(H,{size:"large",icon:s.jsx(Fe,{}),onClick:()=>e("/"),className:"h-12 px-8 text-lg",children:"返回首页"})})]})})})})},{Title:il,Paragraph:ll}=Pe,cl=()=>{const e=Me();return s.jsx("div",{className:"min-h-screen",style:{background:"transparent"},children:s.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ce,{className:"text-center p-12 shadow-soft",children:[s.jsxs("div",{className:"mb-8",children:[s.jsx("div",{className:"w-24 h-24 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx(Xe,{className:"text-4xl text-white"})}),s.jsx(il,{level:1,className:"text-purple-600 mb-4",children:"作品画廊"}),s.jsxs(ll,{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:["作品画廊功能正在开发中，敬请期待！",s.jsx("br",{}),"我们正在为您打造精美的作品展示空间。"]})]}),s.jsx(me,{size:"large",children:s.jsx(H,{size:"large",icon:s.jsx(Fe,{}),onClick:()=>e("/"),className:"h-12 px-8 text-lg",children:"返回首页"})})]})})})})},{Title:dl,Paragraph:ul}=Pe,hl=()=>{const e=Me();return s.jsx("div",{className:"min-h-screen",style:{background:"transparent"},children:s.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ce,{className:"text-center p-12 shadow-soft",children:[s.jsxs("div",{className:"mb-8",children:[s.jsx("div",{className:"w-24 h-24 bg-gradient-to-br from-yellow-400 to-orange-600 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx(Rt,{className:"text-4xl text-white"})}),s.jsx(dl,{level:1,className:"text-orange-600 mb-4",children:"排行榜"}),s.jsxs(ul,{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:["排行榜功能正在开发中，敬请期待！",s.jsx("br",{}),"我们正在为您打造激励性的成就展示系统。"]})]}),s.jsx(me,{size:"large",children:s.jsx(H,{size:"large",icon:s.jsx(Fe,{}),onClick:()=>e("/"),className:"h-12 px-8 text-lg",children:"返回首页"})})]})})})})};class ml{constructor(){bt(this,"baseUrl","/traces")}async createSession(t){try{return(await Ee.post(`${this.baseUrl}/sessions`,t)).data}catch(n){throw console.error("创建轨迹会话失败:",n),n}}async getSessions(t=50,n=0){try{return(await Ee.get(`${this.baseUrl}/sessions`,{params:{limit:t,offset:n}})).data}catch(r){throw console.error("获取轨迹会话列表失败:",r),r}}async getSession(t){try{return(await Ee.get(`${this.baseUrl}/sessions/${t}`)).data}catch(n){throw console.error("获取轨迹会话失败:",n),n}}async deleteSession(t){try{return(await Ee.delete(`${this.baseUrl}/sessions/${t}`)).data}catch(n){throw console.error("删除轨迹会话失败:",n),n}}async generateGuidePaths(t,n={}){try{const r={session_id:t,simplification_level:n.simplification_level||"medium",min_stroke_length:n.min_stroke_length||20,merge_distance:n.merge_distance||50};return(await Ee.post(`${this.baseUrl}/sessions/${t}/generate-guide`,r)).data}catch(r){throw console.error("生成引导线失败:",r),r}}exportSessionAsJson(t){const n=JSON.stringify(t,null,2),r=new Blob([n],{type:"application/json"}),a=URL.createObjectURL(r),o=document.createElement("a");o.href=a,o.download=`trace_${t.name}_${Date.now()}.json`,o.click(),URL.revokeObjectURL(a)}async importSessionFromJson(t){return new Promise((n,r)=>{const a=new FileReader;a.onload=o=>{var i;try{const c=(i=o.target)==null?void 0:i.result,l=JSON.parse(c);if(!this.validateTraceSession(l))throw new Error("无效的轨迹数据格式");n(l)}catch(c){r(new Error("解析JSON文件失败: "+c.message))}},a.onerror=()=>{r(new Error("读取文件失败"))},a.readAsText(t)})}validateTraceSession(t){if(!t||typeof t!="object")return!1;const n=["id","name","strokes","canvasSize","createdAt","duration"];for(const r of n)if(!(r in t))return!1;return!(!Array.isArray(t.strokes)||!t.canvasSize||typeof t.canvasSize!="object"||!("width"in t.canvasSize)||!("height"in t.canvasSize))}saveToLocalStorage(t){try{const n=this.getFromLocalStorage(),r=n.findIndex(a=>a.id===t.id);r>=0?n[r]=t:n.push(t),localStorage.setItem("traceSessions",JSON.stringify(n))}catch(n){throw console.error("保存到本地存储失败:",n),n}}getFromLocalStorage(){try{const t=localStorage.getItem("traceSessions");return t?JSON.parse(t):[]}catch(t){return console.error("从本地存储读取失败:",t),[]}}deleteFromLocalStorage(t){try{const r=this.getFromLocalStorage().filter(a=>a.id!==t);localStorage.setItem("traceSessions",JSON.stringify(r))}catch(n){throw console.error("从本地存储删除失败:",n),n}}clearLocalStorage(){try{localStorage.removeItem("traceSessions")}catch(t){throw console.error("清空本地存储失败:",t),t}}calculateSessionStats(t){const n=t.strokes.length;let r=0,a=0;return t.strokes.forEach(o=>{r+=o.points.length;for(let i=1;i<o.points.length;i++){const c=o.points[i].x-o.points[i-1].x,l=o.points[i].y-o.points[i-1].y;a+=Math.sqrt(c*c+l*l)}}),{totalStrokes:n,totalPoints:r,totalLength:a,averageStrokeLength:n>0?a/n:0,duration:t.duration}}}const Ne=new ml,{Title:fl,Text:Re}=Pe,{Option:Et}=Ot,pl=()=>{const e=h.useRef(null),[t,n]=h.useState(!1),[r,a]=h.useState(!1),[o,i]=h.useState(!1),[c,l]=h.useState(null),[u,d]=h.useState([]),[f,w]=h.useState(""),[I,j]=h.useState(""),[v,g]=h.useState("#000000"),[P,R]=h.useState(3),[C,q]=h.useState({width:800,height:600}),[Q,ie]=h.useState(null),[ee,fe]=h.useState(null),[S,D]=h.useState(0),[N,k]=h.useState(null),[V,G]=h.useState(!1),[te,de]=h.useState(!1),[K,J]=h.useState(null),[y,$]=h.useState(null),W=h.useRef(null),oe=h.useCallback(x=>{u.forEach(z=>{if(!(z.points.length<2)){x.strokeStyle="#000000",x.lineWidth=3,x.lineCap="round",x.lineJoin="round",x.beginPath(),x.moveTo(z.points[0].x,z.points[0].y);for(let M=1;M<z.points.length;M++)x.lineTo(z.points[M].x,z.points[M].y);x.stroke()}})},[u]),Z=h.useCallback((x,z)=>{if(!(z.points.length<2)){x.strokeStyle=v,x.lineWidth=P,x.lineCap="round",x.lineJoin="round",x.beginPath(),x.moveTo(z.points[0].x,z.points[0].y);for(let M=1;M<z.points.length;M++)x.lineTo(z.points[M].x,z.points[M].y);x.stroke()}},[v,P]),B=h.useCallback(()=>{const x=e.current;if(!x)return;const z=x.getContext("2d");z&&(z.clearRect(0,0,x.width,x.height),W.current&&z.drawImage(W.current,0,0,x.width,x.height),oe(z),c&&c.points.length>0&&Z(z,c))},[u,oe,c,Z]);h.useEffect(()=>{if(y){const x=new Image;x.onload=()=>{W.current=x,B()},x.src=y}else W.current=null,B()},[y,B]),h.useEffect(()=>{B()},[c,B]),h.useEffect(()=>{const x=e.current;if(!x)return;const z=x.getContext("2d");if(!z)return;x.width=C.width,x.height=C.height,x.style.width=`${C.width}px`,x.style.height=`${C.height}px`,z.lineCap="round",z.lineJoin="round";const M=E=>{if(!t||r)return;E.preventDefault(),E.stopPropagation();const T=E.touches[0],L=x.getBoundingClientRect(),U=x.width/L.width,O=x.height/L.height,_={x:(T.clientX-L.left)*U,y:(T.clientY-L.top)*O};i(!0);const F={id:`stroke_${Date.now()}`,points:[_]};l(F)},m=E=>{if(!o||!c||!t||r)return;E.preventDefault(),E.stopPropagation();const T=E.touches[0],L=x.getBoundingClientRect(),U=x.width/L.width,O=x.height/L.height,_={x:(T.clientX-L.left)*U,y:(T.clientY-L.top)*O},A={...c,points:[...c.points,_]};l(A)},b=E=>{E.preventDefault(),E.stopPropagation(),!(!o||!c||!t||r)&&(i(!1),d(T=>[...T,c]),l(null))};return x.addEventListener("touchstart",M,{passive:!1}),x.addEventListener("touchmove",m,{passive:!1}),x.addEventListener("touchend",b,{passive:!1}),B(),()=>{x.removeEventListener("touchstart",M),x.removeEventListener("touchmove",m),x.removeEventListener("touchend",b)}},[C,B,t,r,o,c]);const le=h.useCallback(()=>{const x=e.current;if(!x)return;const z=x.getContext("2d");z&&(z.clearRect(0,0,x.width,x.height),W.current&&z.drawImage(W.current,0,0,x.width,x.height))},[]),ne=x=>{const z=e.current;if(!z)return{x:0,y:0};const M=z.getBoundingClientRect(),m=z.width/M.width,b=z.height/M.height,E=(x.clientX-M.left)*m,T=(x.clientY-M.top)*b;return{x:E,y:T}},Se=x=>{if(!t||r)return;x.preventDefault(),i(!0);const z=ne(x),m={id:`stroke_${Date.now()}`,points:[z]};l(m)},ye=x=>{if(!o||!c||!t||r)return;x.preventDefault();const z=ne(x),M={...c,points:[...c.points,z]};l(M)},$e=()=>{!o||!c||(i(!1),d(x=>[...x,c]),l(null))},ts=()=>{if(!f.trim()){X.error("请输入会话名称");return}n(!0),a(!1),fe(Date.now()),D(0),d([]),le(),X.success("开始录制轨迹")},De=()=>{t&&(r?(N&&D(x=>x+(Date.now()-N)),k(null),a(!1),X.info("恢复录制")):(k(Date.now()),a(!0),i(!1),l(null),X.info("暂停录制")))},ss=()=>{n(!1),a(!1),i(!1),l(null),N&&(D(x=>x+(Date.now()-N)),k(null)),X.success("录制已停止")},ue=()=>{d([]),le(),X.info("画布已清空")},gt=(x,z=3)=>x.filter(M=>{if(M.points.length<2)return!1;let m=0;for(let b=1;b<M.points.length;b++){const E=M.points[b].x-M.points[b-1].x,T=M.points[b].y-M.points[b-1].y;m+=Math.sqrt(E*E+T*T)}return m>=z}).map(M=>{if(M.points.length<10)return M;const m=xt(M.points),b=ns(m);return{...M,points:b}}),xt=(x,z=2)=>{if(x.length<=3)return x;const M=[];M.push(x[0]);for(let m=1;m<x.length-1;m++){const b=x[m-1],E=x[m],T=x[m+1],L=(b.x+E.x*2+T.x)/4,U=(b.y+E.y*2+T.y)/4;M.push({x:L,y:U})}return M.push(x[x.length-1]),M},ns=(x,z=.5)=>{if(x.length<=3)return x;const M=[x[0]];for(let m=1;m<x.length-1;m++){const b=M[M.length-1],E=x[m];Math.sqrt(Math.pow(E.x-b.x,2)+Math.pow(E.y-b.y,2))>z&&M.push(E)}return M.push(x[x.length-1]),M},yt=async()=>{if(u.length===0){X.error("没有轨迹数据可保存");return}de(!0);try{const x=ee?Date.now()-ee:0,z=gt(u);console.log(`轨迹优化完成: 原始笔画数 ${u.length}, 优化后笔画数 ${z.length}`);const M={name:f,description:I,strokes:z,canvasSize:C,duration:x-S};try{const m=await Ne.createSession(M);if(m.success&&m.data)X.success("轨迹数据已保存到服务器"),console.log("保存成功，会话ID:",m.data.id);else throw new Error(m.message)}catch(m){console.warn("API保存失败，使用本地存储:",m);const b={id:`session_${Date.now()}`,name:f,description:I,strokes:u,canvasSize:C,createdAt:new Date().toISOString(),duration:x-S};Ne.saveToLocalStorage(b),X.success("轨迹数据已保存到本地")}w(""),j(""),d([]),le()}catch(x){console.error("保存失败:",x),X.error("保存失败，请重试")}finally{de(!1)}},rs=x=>{if(!["image/jpeg","image/jpg","image/png","image/gif","image/webp"].includes(x.type))return X.error("请上传图片文件 (JPG, PNG, GIF, WebP)"),!1;const M=10*1024*1024;if(x.size>M)return X.error("图片文件大小不能超过 10MB"),!1;const m=new FileReader;return m.onload=b=>{var L;const E=(L=b.target)==null?void 0:L.result;$(E);const T=new Image;T.onload=()=>{const U=Math.min(window.innerWidth*.8,1200),O=Math.min(window.innerHeight*.7,800);let _=T.width,A=T.height;_>U&&(A=A*U/_,_=U),A>O&&(_=_*O/A,A=O);const F={width:Math.round(_),height:Math.round(A)};q(F),X.success(`背景图已加载，画布尺寸调整为 ${Math.round(_)} × ${Math.round(A)}`)},T.src=E},m.readAsDataURL(x),!1},tt=()=>{$(null),q({width:800,height:600}),X.info("背景图已移除")},as=()=>{d([]),tt(),le(),X.info("画布和背景图已清空")},os=()=>{if(u.length===0){X.error("没有轨迹数据可导出");return}const x=ee?Date.now()-ee:0,z=gt(u);console.log(`导出轨迹优化完成: 原始笔画数 ${u.length}, 优化后笔画数 ${z.length}`);const M={id:`session_${Date.now()}`,name:f||"未命名会话",description:I,strokes:z,canvasSize:C,createdAt:new Date().toISOString(),duration:x-S};Ne.exportSessionAsJson(M),X.success("轨迹数据已导出")};return s.jsxs("div",{className:"trace-recorder min-h-screen p-3 bg-gray-50",children:[s.jsx("div",{className:"max-w-7xl mx-auto",children:s.jsx(re.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},children:s.jsxs("div",{className:"grid grid-cols-1 xl:grid-cols-4 gap-4",children:[s.jsx("div",{className:"xl:col-span-1",children:s.jsx(ce,{size:"small",className:"mb-3",children:s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx(Re,{strong:!0,className:"block mb-2",children:"会话信息"}),s.jsxs(me,{direction:"vertical",className:"w-full",size:"small",children:[s.jsx($s,{placeholder:"会话名称",value:f,onChange:x=>w(x.target.value),disabled:t,size:"small"}),s.jsx($s.TextArea,{placeholder:"会话描述（可选）",value:I,onChange:x=>j(x.target.value),disabled:t,rows:2,size:"small"})]})]}),s.jsxs("div",{children:[s.jsx(Re,{strong:!0,className:"block mb-2",children:"背景图"}),s.jsxs(me,{direction:"vertical",className:"w-full",size:"small",children:[s.jsx(H,{icon:s.jsx(Xe,{}),onClick:()=>{const x=document.createElement("input");x.type="file",x.accept="image/*",x.onchange=z=>{var m;const M=(m=z.target.files)==null?void 0:m[0];M&&rs(M)},x.click()},disabled:t,className:"w-full",size:"small",children:"上传背景图"}),y&&s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(Re,{type:"success",style:{fontSize:"11px"},children:"✓ 已加载"}),s.jsx(H,{icon:s.jsx(fs,{}),size:"small",danger:!0,onClick:tt,disabled:t,children:"移除"})]})]})]}),s.jsxs("div",{children:[s.jsx(Re,{strong:!0,className:"block mb-2",children:"画笔设置"}),s.jsxs(me,{direction:"vertical",className:"w-full",size:"small",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(Re,{style:{fontSize:"12px",width:"30px"},children:"颜色"}),s.jsx("input",{type:"color",value:v,onChange:x=>g(x.target.value),disabled:t&&!r,className:"w-8 h-6 border rounded"})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(Re,{style:{fontSize:"12px",width:"30px"},children:"大小"}),s.jsxs(Ot,{value:P,onChange:R,disabled:t&&!r,className:"flex-1",size:"small",children:[s.jsx(Et,{value:1,children:"细 (1px)"}),s.jsx(Et,{value:3,children:"中 (3px)"}),s.jsx(Et,{value:5,children:"粗 (5px)"}),s.jsx(Et,{value:8,children:"很粗 (8px)"})]})]})]})]}),s.jsxs("div",{children:[s.jsx(fl,{level:4,children:"录制控制"}),s.jsxs(me,{wrap:!0,children:[t?s.jsxs(s.Fragment,{children:[s.jsx(H,{icon:r?s.jsx(Oe,{}):s.jsx(wn,{}),onClick:De,size:"large",children:r?"恢复":"暂停"}),s.jsx(H,{icon:s.jsx(_r,{}),onClick:ss,size:"large",danger:!0,children:"停止"})]}):s.jsx(H,{type:"primary",icon:s.jsx(Oe,{}),onClick:ts,size:"large",children:"开始录制"}),s.jsx(H,{icon:s.jsx(jn,{}),onClick:ue,disabled:t&&!r,children:"清空轨迹"}),y&&s.jsx(H,{icon:s.jsx(fs,{}),onClick:as,disabled:t&&!r,danger:!0,children:"清空全部"})]})]}),s.jsxs("div",{children:[s.jsx(Re,{strong:!0,className:"block mb-2",children:"保存操作"}),s.jsxs(me,{direction:"vertical",className:"w-full",size:"small",children:[s.jsx(H,{type:"primary",icon:s.jsx(vn,{}),onClick:yt,disabled:u.length===0||te,loading:te,className:"w-full",size:"small",children:te?"保存中...":"保存轨迹"}),s.jsx(H,{icon:s.jsx(Cs,{}),onClick:os,disabled:u.length===0,className:"w-full",size:"small",children:"导出JSON"}),s.jsx(H,{icon:s.jsx(Sn,{}),onClick:()=>G(!0),disabled:u.length===0,className:"w-full",size:"small",children:"预览轨迹"})]})]}),t&&s.jsxs("div",{className:"mt-4 p-2 bg-gray-100 rounded",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:`w-2 h-2 rounded-full ${r?"bg-yellow-500":"bg-red-500 animate-pulse"}`}),s.jsx(Re,{style:{fontSize:"11px"},strong:!0,children:r?"录制已暂停":"正在录制..."})]}),s.jsxs(Re,{style:{fontSize:"11px"},type:"secondary",children:["笔画数: ",u.length]})]})]})})}),s.jsx("div",{className:"xl:col-span-3",children:s.jsx(ce,{size:"small",children:s.jsxs("div",{className:"flex flex-col items-center",children:[s.jsx("div",{className:"mb-2 text-center",children:s.jsxs(Re,{type:"secondary",style:{fontSize:"12px"},children:["画布: ",C.width," × ",C.height,y&&" • 已加载背景图"]})}),s.jsx("div",{className:"relative",children:s.jsx("canvas",{ref:e,width:C.width,height:C.height,className:`border-2 border-gray-300 rounded-lg cursor-crosshair shadow-md ${y?"bg-transparent":"bg-white"}`,style:{touchAction:"none",userSelect:"none",maxWidth:"100%",maxHeight:"70vh",position:"relative",margin:0,padding:0,border:"none",outline:"none",display:"block"},onMouseDown:Se,onMouseMove:ye,onMouseUp:$e,onMouseLeave:()=>i(!1)})})]})})})]})})}),s.jsx(Lt,{title:"轨迹预览",open:V,onCancel:()=>G(!1),footer:null,width:900,children:s.jsxs("div",{className:"text-center",children:[s.jsxs(Re,{children:["会话: ",f||"未命名"]}),s.jsx("br",{}),s.jsxs(Re,{type:"secondary",children:["笔画数: ",u.length]}),s.jsx("br",{}),s.jsxs(Re,{type:"secondary",children:["录制时长: ",Math.round((Date.now()-(ee||Date.now())-S)/1e3),"秒"]})]})})]})},{Title:gl,Text:_e}=Pe,{Option:ms}=Ot,xl=({onGenerateGuide:e})=>{const[t,n]=h.useState([]),[r,a]=h.useState(!1),[o,i]=h.useState(null),[c,l]=h.useState(!1),[u,d]=h.useState(!1),[f,w]=h.useState(null),[I,j]=h.useState({simplification_level:"medium",min_stroke_length:20,merge_distance:50}),[v,g]=h.useState(!1),P=async()=>{a(!0);try{let N=[];try{const k=await Ne.getSessions();N=[...k],console.log("从API加载了",k.length,"个会话")}catch(k){console.warn("API加载失败:",k)}try{const k=Ne.getFromLocalStorage();console.log("从本地存储加载了",k.length,"个会话");const V=new Set(N.map(te=>te.id)),G=k.filter(te=>!V.has(te.id));N=[...N,...G]}catch(k){console.warn("本地存储加载失败:",k)}N.sort((k,V)=>new Date(V.createdAt).getTime()-new Date(k.createdAt).getTime()),n(N),console.log("总共加载了",N.length,"个会话")}catch(N){console.error("加载轨迹会话失败:",N),X.error("加载轨迹会话失败")}finally{a(!1)}};h.useEffect(()=>{P()},[]);const R=async N=>{try{try{await Ne.deleteSession(N)}catch(k){console.warn("API删除失败，从本地存储删除:",k),Ne.deleteFromLocalStorage(N)}X.success("会话删除成功"),P()}catch(k){console.error("删除会话失败:",k),X.error("删除会话失败")}},C=N=>{i(N),l(!0)},q=N=>{Ne.exportSessionAsJson(N),X.success("会话数据已导出")},Q=()=>{const N=document.createElement("input");N.type="file",N.accept=".json",N.onchange=async k=>{var G;const V=(G=k.target.files)==null?void 0:G[0];if(V)try{const te=await Ne.importSessionFromJson(V);try{await Ne.createSession({name:te.name,description:te.description,strokes:te.strokes,canvasSize:te.canvasSize,duration:te.duration})}catch(de){console.warn("API保存失败，保存到本地存储:",de),Ne.saveToLocalStorage(te)}X.success("会话导入成功"),P()}catch(te){console.error("导入会话失败:",te),X.error("导入会话失败: "+te.message)}},N.click()},ie=N=>{w(N),d(!0)},ee=async()=>{if(f){g(!0);try{let N=f,k=!1;try{await Ne.getSession(f.id)}catch{console.log("会话不存在于API中，尝试上传..."),k=!0}if(k)try{const G=await Ne.createSession({name:f.name,description:f.description,strokes:f.strokes,canvasSize:f.canvasSize,duration:f.duration});if(G.success&&G.data)N=G.data,console.log("会话上传成功，新ID:",N.id);else throw new Error("上传会话失败")}catch(G){console.error("上传会话失败:",G),X.error("无法上传会话到服务器，请检查网络连接");return}const V=await Ne.generateGuidePaths(N.id,I);if(V.success&&V.guide_paths)X.success("引导线生成成功"),d(!1),e&&V.canvas_size&&e(V.guide_paths,V.canvas_size,void 0),k&&P();else throw new Error(V.message||"生成引导线失败")}catch(N){console.error("生成引导线失败:",N),X.error("生成引导线失败: "+N.message)}finally{g(!1)}}},fe=N=>{const k=Math.floor(N/1e3),V=Math.floor(k/60),G=k%60;return`${V}:${G.toString().padStart(2,"0")}`},S=N=>Ne.calculateSessionStats(N),D=[{title:"会话名称",dataIndex:"name",key:"name",render:(N,k)=>s.jsxs("div",{children:[s.jsx(_e,{strong:!0,children:N}),k.description&&s.jsx("div",{children:s.jsx(_e,{type:"secondary",style:{fontSize:"12px"},children:k.description})})]})},{title:"统计信息",key:"stats",render:(N,k)=>{const V=S(k);return s.jsxs(me,{direction:"vertical",size:"small",children:[s.jsxs(ze,{color:"blue",children:[V.totalStrokes," 笔画"]}),s.jsxs(ze,{color:"green",children:[V.totalPoints," 点"]}),s.jsx(ze,{color:"orange",children:fe(V.duration)})]})}},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",render:N=>new Date(N).toLocaleString()},{title:"操作",key:"actions",render:(N,k)=>s.jsxs(me,{children:[s.jsx(jt,{title:"预览",children:s.jsx(H,{icon:s.jsx(Sn,{}),onClick:()=>C(k),size:"small"})}),s.jsx(jt,{title:"生成引导线",children:s.jsx(H,{icon:s.jsx(Oe,{}),onClick:()=>ie(k),size:"small",type:"primary"})}),s.jsx(jt,{title:"导出",children:s.jsx(H,{icon:s.jsx(Cs,{}),onClick:()=>q(k),size:"small"})}),s.jsx(jt,{title:"删除",children:s.jsx(yn,{title:"确定要删除这个会话吗？",onConfirm:()=>R(k.id),okText:"确定",cancelText:"取消",children:s.jsx(H,{icon:s.jsx(fs,{}),danger:!0,size:"small"})})})]})}];return s.jsxs("div",{className:"trace-manager",children:[s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ce,{children:[s.jsxs("div",{className:"flex justify-between items-center mb-6",children:[s.jsx(gl,{level:3,children:"轨迹管理"}),s.jsxs(me,{children:[s.jsx(H,{icon:s.jsx(Ke,{}),onClick:P,loading:r,children:"刷新"}),s.jsx(H,{icon:s.jsx(Lr,{}),onClick:Q,children:"导入会话"})]})]}),s.jsx(Ir,{columns:D,dataSource:t,rowKey:"id",loading:r,pagination:{pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:N=>`共 ${N} 个会话`}})]})}),s.jsx(Lt,{title:`预览会话: ${o==null?void 0:o.name}`,open:c,onCancel:()=>l(!1),footer:null,width:800,children:o&&s.jsx("div",{children:s.jsxs(me,{direction:"vertical",className:"w-full",children:[s.jsxs("div",{children:[s.jsx(_e,{strong:!0,children:"描述: "}),s.jsx(_e,{children:o.description||"无"})]}),s.jsxs("div",{children:[s.jsx(_e,{strong:!0,children:"画布尺寸: "}),s.jsxs(_e,{children:[o.canvasSize.width," × ",o.canvasSize.height]})]}),s.jsxs("div",{children:[s.jsx(_e,{strong:!0,children:"背景图: "}),s.jsx(_e,{children:"无"})]}),s.jsxs("div",{children:[s.jsx(_e,{strong:!0,children:"统计信息: "}),(()=>{const N=S(o);return s.jsxs("div",{children:[s.jsxs(ze,{color:"blue",children:[N.totalStrokes," 笔画"]}),s.jsxs(ze,{color:"green",children:[N.totalPoints," 点"]}),s.jsx(ze,{color:"orange",children:fe(N.duration)}),s.jsxs(ze,{color:"purple",children:[Math.round(N.totalLength)," 像素长度"]})]})})()]})]})})}),s.jsx(Lt,{title:`生成引导线: ${f==null?void 0:f.name}`,open:u,onCancel:()=>d(!1),onOk:ee,confirmLoading:v,okText:"生成",cancelText:"取消",children:s.jsxs(me,{direction:"vertical",className:"w-full",children:[s.jsxs("div",{children:[s.jsx(_e,{strong:!0,children:"简化级别:"}),s.jsxs(Ot,{value:I.simplification_level,onChange:N=>j(k=>({...k,simplification_level:N})),className:"w-full mt-2",children:[s.jsx(ms,{value:"low",children:"低 (保留更多细节)"}),s.jsx(ms,{value:"medium",children:"中 (平衡)"}),s.jsx(ms,{value:"high",children:"高 (更简化)"})]})]}),s.jsxs("div",{children:[s.jsx(_e,{strong:!0,children:"最小笔画长度 (像素):"}),s.jsx(Ds,{value:I.min_stroke_length,onChange:N=>j(k=>({...k,min_stroke_length:N||20})),min:1,max:200,className:"w-full mt-2"})]}),s.jsxs("div",{children:[s.jsx(_e,{strong:!0,children:"合并距离 (像素):"}),s.jsx(Ds,{value:I.merge_distance,onChange:N=>j(k=>({...k,merge_distance:N||50})),min:1,max:200,className:"w-full mt-2"})]}),s.jsx(Ar,{}),s.jsx(_e,{type:"secondary",children:"这些设置将影响生成的引导线的复杂度和精度。较高的简化级别会产生更简单的引导线， 较大的合并距离会将相近的笔画合并为一条路径。"})]})})]})},{Title:yl,Text:Ue}=Pe,bl=({guidePaths:e,canvasSize:t,onExport:n})=>{const r=h.useRef(null),[a,o]=h.useState(!1),[i,c]=h.useState(0),[l,u]=h.useState(1),[d,f]=h.useState(!0),[w,I]=h.useState(0),j=h.useRef(null),v=()=>{const S=r.current;if(!S)return;const D=S.getContext("2d");D&&D.clearRect(0,0,S.width,S.height)},g=()=>{const S=r.current;if(!S)return;const D=S.getContext("2d");D&&(v(),d?e.forEach((N,k)=>{P(D,N,k<=w)}):w<e.length&&P(D,e[w],!0))},P=(S,D,N)=>{if(!(D.points.length<2)){S.strokeStyle=N?"#FFD700":"#E5E7EB",S.lineWidth=N?3:2,S.lineCap="round",S.lineJoin="round",N?S.setLineDash([10,5]):S.setLineDash([5,5]),S.beginPath(),S.moveTo(D.points[0].x,D.points[0].y);for(let k=1;k<D.points.length;k++)S.lineTo(D.points[k].x,D.points[k].y);S.stroke(),S.setLineDash([])}},R=()=>{const S=r.current;if(!S)return;const D=S.getContext("2d");D&&(v(),d?e.forEach((N,k)=>{k<w?P(D,N,!0):k===w?C(D,N,i):P(D,N,!1)}):w<e.length&&C(D,e[w],i))},C=(S,D,N)=>{if(D.points.length<2)return;const k=D.points.length,V=Math.floor(k*N);if(S.strokeStyle="#FFD700",S.lineWidth=3,S.lineCap="round",S.lineJoin="round",S.setLineDash([10,5]),S.beginPath(),V>0){S.moveTo(D.points[0].x,D.points[0].y);for(let G=1;G<V;G++)S.lineTo(D.points[G].x,D.points[G].y);if(V<k){const G=D.points[V-1],te=D.points[V],de=k*N-V,K=G.x+(te.x-G.x)*de,J=G.y+(te.y-G.y)*de;S.lineTo(K,J)}}S.stroke(),S.setLineDash([]),N>.1&&V>0&&q(S,D,N)},q=(S,D,N)=>{const k=D.points.length,V=Math.floor(k*N);let G,te,de,K;if(V<k){const W=D.points[V-1],oe=D.points[V],Z=k*N-V;G=W.x+(oe.x-W.x)*Z,te=W.y+(oe.y-W.y)*Z,de=oe.x-W.x,K=oe.y-W.y}else{G=D.points[k-1].x,te=D.points[k-1].y,de=0,K=0;const W=Math.min(5,k-1);for(let oe=1;oe<=W;oe++){const Z=D.points[k-oe],B=D.points[k-oe-1];de+=Z.x-B.x,K+=Z.y-B.y}de/=W,K/=W}const J=Math.sqrt(de*de+K*K);if(J<.1)return;de/=J,K/=J;const y=Math.atan2(K,de),$=15;S.fillStyle="#FFD700",S.strokeStyle="#FFD700",S.lineWidth=2,S.beginPath(),S.moveTo(G,te),S.lineTo(G-$*Math.cos(y-Math.PI/6),te-$*Math.sin(y-Math.PI/6)),S.lineTo(G-$*Math.cos(y+Math.PI/6),te-$*Math.sin(y+Math.PI/6)),S.closePath(),S.fill(),S.stroke()},Q=()=>{a&&(c(S=>{const D=S+.01*l;return D>=1?d&&w<e.length-1?(I(N=>N+1),0):(o(!1),1):D}),j.current=requestAnimationFrame(Q))},ie=()=>{a?(o(!1),j.current&&cancelAnimationFrame(j.current)):o(!0)},ee=()=>{o(!1),c(0),I(0),j.current&&cancelAnimationFrame(j.current)},fe=()=>{if(n)n(e);else{const S=JSON.stringify({guidePaths:e,canvasSize:t,exportedAt:new Date().toISOString()},null,2),D=new Blob([S],{type:"application/json"}),N=URL.createObjectURL(D),k=document.createElement("a");k.href=N,k.download=`guide_paths_${Date.now()}.json`,k.click(),URL.revokeObjectURL(N),X.success("引导线数据已导出")}};return h.useEffect(()=>(a&&(j.current=requestAnimationFrame(Q)),()=>{j.current&&cancelAnimationFrame(j.current)}),[a,l,w,d]),h.useEffect(()=>{a?R():g()},[e,i,w,d,a]),e.length===0?s.jsx(ce,{children:s.jsx("div",{className:"text-center py-8",children:s.jsx(Ue,{type:"secondary",children:"暂无引导线数据"})})}):s.jsx("div",{className:"guide-preview",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ce,{children:[s.jsxs("div",{className:"flex justify-between items-center mb-4",children:[s.jsx(yl,{level:4,children:"引导线预览"}),s.jsxs(me,{children:[s.jsx(H,{icon:a?s.jsx(wn,{}):s.jsx(Oe,{}),onClick:ie,type:"primary",children:a?"暂停":"播放"}),s.jsx(H,{icon:s.jsx(Ke,{}),onClick:ee,children:"重置"}),s.jsx(H,{icon:s.jsx(Cs,{}),onClick:fe,children:"导出"})]})]}),s.jsx("div",{className:"mb-4 p-4 bg-gray-50 rounded-lg",children:s.jsxs(me,{direction:"vertical",className:"w-full",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(Ue,{children:"动画速度:"}),s.jsx("div",{className:"w-48",children:s.jsx(bn,{min:.1,max:3,step:.1,value:l,onChange:u,disabled:a})})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(Ue,{children:"显示所有路径:"}),s.jsx(Or,{checked:d,onChange:f,disabled:a})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx(Ue,{children:"路径信息:"}),s.jsxs(Ue,{type:"secondary",children:["共 ",e.length," 条路径，当前第 ",w+1," 条"]})]})]})}),s.jsx("div",{className:"flex justify-center",children:s.jsx("canvas",{ref:r,width:t.width,height:t.height,className:"border-2 border-gray-300 rounded-lg bg-white",style:{maxWidth:"100%",maxHeight:"600px"}})}),s.jsx("div",{className:"mt-4 text-center",children:s.jsxs(me,{children:[s.jsxs(Ue,{type:"secondary",children:["画布尺寸: ",t.width," × ",t.height]}),s.jsxs(Ue,{type:"secondary",children:["路径数量: ",e.length]}),s.jsxs(Ue,{type:"secondary",children:["总点数: ",e.reduce((S,D)=>S+D.points.length,0)]})]})})]})})})},jl=()=>{const[e,t]=h.useState("recorder"),[n,r]=h.useState([]),[a,o]=h.useState({width:800,height:600}),[i,c]=h.useState(),l=(u,d,f)=>{r(u),o(d),c(f),t("preview")};return s.jsx("div",{className:"trace-recorder-page min-h-screen",style:{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)"},children:s.jsx("div",{className:"container mx-auto px-4 py-6",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsx(ce,{className:"shadow-lg",children:s.jsx($r,{activeKey:e,onChange:t,size:"large",className:"trace-tabs",items:[{key:"recorder",label:"📝 轨迹记录",children:s.jsx(pl,{})},{key:"manager",label:"📁 轨迹管理",children:s.jsx(xl,{onGenerateGuide:l})},{key:"preview",label:`🎯 引导线预览 ${n.length>0?`(${n.length})`:""}`,children:s.jsx(bl,{guidePaths:n,canvasSize:a,backgroundImage:i})}]})})})})})},{Title:fn,Text:pn}=Pe,vl=()=>{const e=Me(),t=[{id:"L1-1",name:"第一关第一阶段",type:"L1"},{id:"L1-2",name:"第一关第二阶段",type:"L1"},{id:"L1-3",name:"第一关第三阶段",type:"L1"},{id:"L1-4",name:"第一关第四阶段",type:"L1"},{id:"L1-5",name:"第一关第五阶段",type:"L1"},{id:"L2-1",name:"第二关第一阶段 (统一背景)",type:"other"},{id:"L3-1",name:"第三关第一阶段 (统一背景)",type:"other"},{id:"L4-1",name:"第四关第一阶段 (统一背景)",type:"other"}],n=r=>{const[a,o]=r.split("-");e(`/game/${a}/${o}`)};return s.jsx("div",{className:"min-h-screen pt-8",style:{background:"transparent"},children:s.jsxs("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[s.jsxs("div",{className:"text-center mb-8",children:[s.jsx(fn,{level:1,className:"text-white",style:{textShadow:"2px 2px 4px rgba(0,0,0,0.8)"},children:"背景图片测试"}),s.jsx(pn,{className:"text-white text-lg",style:{textShadow:"1px 1px 2px rgba(0,0,0,0.8)"},children:"点击下面的按钮测试不同关卡的背景图片切换"})]}),s.jsx("div",{className:"text-center",children:s.jsxs(me,{direction:"vertical",size:"large",children:[s.jsx(me,{wrap:!0,size:"middle",children:t.map(r=>s.jsx(H,{type:r.type==="L1"?"primary":"default",size:"large",onClick:()=>n(r.id),style:{minWidth:"200px",height:"50px",fontSize:"16px",backgroundColor:r.type==="L1"?"rgba(139, 92, 246, 0.9)":"rgba(34, 197, 94, 0.9)",borderColor:r.type==="L1"?"rgba(139, 92, 246, 0.9)":"rgba(34, 197, 94, 0.9)",color:"white",backdropFilter:"blur(10px)"},children:r.name},r.id))}),s.jsx(H,{type:"default",size:"large",onClick:()=>e("/"),style:{minWidth:"200px",height:"50px",fontSize:"16px",backgroundColor:"rgba(255, 255, 255, 0.9)",backdropFilter:"blur(10px)"},children:"返回首页"})]})}),s.jsx("div",{className:"mt-12 text-center",children:s.jsxs("div",{className:"inline-block p-6 rounded-lg",style:{backgroundColor:"rgba(255, 255, 255, 0.9)",backdropFilter:"blur(10px)",maxWidth:"600px"},children:[s.jsx(fn,{level:3,children:"测试说明"}),s.jsxs(pn,{children:[s.jsx("strong",{children:"紫色按钮 (L1关卡)"}),"：使用各自目录下的background.jpg文件作为背景图片。",s.jsx("br",{}),"例如：L1-2关卡显示 /L1-2/background.jpg 作为背景。",s.jsx("br",{}),s.jsx("strong",{children:"绿色按钮 (其他关卡)"}),"：统一使用 /background.jpg 作为背景图片。",s.jsx("br",{}),s.jsx("strong",{children:"其他页面"}),"：轨迹记录、首页等非游戏页面也使用统一背景 /background.jpg。"]})]})})]})})};class wl extends h.Component{constructor(n){super(n);bt(this,"handleReload",()=>{window.location.reload()});bt(this,"handleGoHome",()=>{window.location.href="/"});this.state={hasError:!1}}static getDerivedStateFromError(n){return{hasError:!0,error:n}}componentDidCatch(n,r){console.error("ErrorBoundary caught an error:",n,r),this.setState({error:n,errorInfo:r})}render(){return this.state.hasError?s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:s.jsx("div",{className:"max-w-2xl mx-auto p-8",children:s.jsx(Dr,{status:"error",title:"哎呀，出现了一些问题",subTitle:"不用担心，这不是您的错误。我们的技术团队会尽快解决这个问题。",extra:[s.jsx(H,{type:"primary",icon:s.jsx(Ke,{}),onClick:this.handleReload,size:"large",children:"重新加载"},"reload"),s.jsx(H,{icon:s.jsx(Nn,{}),onClick:this.handleGoHome,size:"large",children:"返回首页"},"home")],children:s.jsxs("div",{className:"text-center mt-8",children:[s.jsx("p",{className:"text-gray-600 mb-4",children:"如果问题持续存在，请联系我们的客服团队："}),s.jsxs("p",{className:"text-gray-600",children:["📧 <EMAIL>",s.jsx("br",{}),"📞 400-123-4567"]}),s.jsx("div",{className:"mt-6 p-4 bg-orange-50 rounded-lg",children:s.jsx("p",{className:"text-orange-600 font-medium",children:"💝 记忆画笔团队始终为您提供温暖的技术支持"})})]})})})}):this.props.children}}const Sl=({visible:e,onClose:t,onEnterFullscreen:n})=>{const r=()=>{n(),t()};return s.jsx(Lt,{open:e,onCancel:t,footer:null,centered:!0,width:500,closable:!1,maskClosable:!1,className:"fullscreen-prompt-modal",children:s.jsxs("div",{className:"text-center p-6",children:[s.jsxs("div",{className:"mb-6",children:[s.jsx("div",{className:"w-20 h-20 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(ps,{className:"text-3xl text-white"})}),s.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-3",children:"获得最佳体验"}),s.jsxs("p",{className:"text-lg text-gray-600 leading-relaxed",children:["为了获得最佳的绘画体验，建议您使用全屏模式。",s.jsx("br",{}),"全屏模式可以让您专注于创作，减少干扰。"]})]}),s.jsxs(me,{size:"large",className:"w-full justify-center",children:[s.jsx(H,{type:"primary",size:"large",icon:s.jsx(ps,{}),onClick:r,className:"h-12 px-8 text-lg bg-gradient-to-r from-purple-500 to-purple-600 border-0",children:"进入全屏"}),s.jsx(H,{size:"large",icon:s.jsx(En,{}),onClick:t,className:"h-12 px-8 text-lg",children:"稍后再说"})]}),s.jsx("div",{className:"mt-6 text-sm text-gray-500",children:s.jsx("p",{children:"您也可以随时通过右上角的全屏按钮切换全屏模式"})})]})})},Nl=()=>{const e=Me(),t=Ut(),[n,r]=h.useState(!1),[a,o]=h.useState(!!document.fullscreenElement);Be.useEffect(()=>{const l=()=>{o(!!document.fullscreenElement)};return document.addEventListener("fullscreenchange",l),()=>{document.removeEventListener("fullscreenchange",l)}},[]);const i=async()=>{try{document.fullscreenElement?await document.exitFullscreen():await document.documentElement.requestFullscreen()}catch(l){console.error("全屏切换失败:",l)}},c=[{key:"home",icon:s.jsx(Nn,{}),label:"首页",onClick:()=>{e("/"),r(!1)}},{key:"game",icon:s.jsx(Oe,{}),label:"开始绘画",onClick:()=>{e("/game"),r(!1)}},{key:"gallery",icon:s.jsx(Xe,{}),label:"作品画廊",onClick:()=>{e("/gallery"),r(!1)}},{key:"leaderboard",icon:s.jsx(Rt,{}),label:"排行榜",onClick:()=>{e("/leaderboard"),r(!1)}},{key:"trace-recorder",icon:s.jsx(Mr,{}),label:"轨迹记录",onClick:()=>{e("/trace-recorder"),r(!1)}},{key:"settings",icon:s.jsx(ks,{}),label:"设置",onClick:()=>{e("/settings"),r(!1)}},{type:"divider"},{key:"fullscreen",icon:a?s.jsx(Ur,{}):s.jsx(ps,{}),label:a?"退出全屏":"进入全屏",onClick:()=>{i(),r(!1)}}];return s.jsxs("div",{className:"fixed top-6 right-6 z-50",children:[s.jsx(Br,{children:n&&s.jsx(re.div,{initial:{opacity:0,scale:.8,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.8,y:20},transition:{duration:.2},className:"mb-4",children:s.jsx("div",{className:"bg-white rounded-2xl shadow-2xl border border-gray-100 overflow-hidden",children:s.jsx("div",{className:"p-2",children:c.map((l,u)=>{if(l.type==="divider")return s.jsx("div",{className:"h-px bg-gray-200 my-2"},u);const d=l.key==="home"&&t.pathname==="/"||l.key==="game"&&t.pathname.startsWith("/game")||l.key==="gallery"&&t.pathname==="/gallery"||l.key==="leaderboard"&&t.pathname==="/leaderboard"||l.key==="trace-recorder"&&t.pathname==="/trace-recorder"||l.key==="settings"&&t.pathname==="/settings";return s.jsx(H,{type:d?"primary":"text",icon:l.icon,onClick:l.onClick,className:"w-full justify-start mb-1 h-12 text-left",size:"large",children:l.label},l.key)})})})})}),s.jsx(re.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:s.jsx(H,{type:"primary",shape:"circle",size:"large",icon:n?s.jsx(En,{}):s.jsx(Fr,{}),onClick:()=>r(!n),className:"w-14 h-14 bg-gradient-to-br from-purple-500 to-purple-600 border-0 shadow-2xl hover:shadow-3xl",style:{fontSize:"18px"}})})]})},{Content:El}=kt,{Title:kl,Paragraph:Cl}=Pe,Pl=()=>{const[e,t]=Be.useState(!1),n=Me();ho(),Be.useEffect(()=>{if(!localStorage.getItem("memorybrush-fullscreen-prompt-seen")&&!document.fullscreenElement){const i=setTimeout(()=>{t(!0)},2e3);return()=>clearTimeout(i)}},[]);const r=async()=>{try{await document.documentElement.requestFullscreen(),localStorage.setItem("memorybrush-fullscreen-prompt-seen","true")}catch(o){console.error("进入全屏失败:",o)}},a=()=>{t(!1),localStorage.setItem("memorybrush-fullscreen-prompt-seen","true")};return s.jsxs(wl,{children:[s.jsx(kt,{className:"min-h-screen",style:{background:"transparent"},children:s.jsx(kt,{children:s.jsx(kt,{className:"transition-all duration-300",children:s.jsx(El,{className:"min-h-screen",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:s.jsxs(za,{children:[s.jsx(Le,{path:"/",element:s.jsx(mo,{})}),s.jsx(Le,{path:"/game",element:s.jsx(hs,{})}),s.jsx(Le,{path:"/game/:level",element:s.jsx(hs,{})}),s.jsx(Le,{path:"/game/:level/:stage",element:s.jsx(hs,{})}),s.jsx(Le,{path:"/profile",element:s.jsx(nl,{})}),s.jsx(Le,{path:"/settings",element:s.jsx(ol,{})}),s.jsx(Le,{path:"/gallery",element:s.jsx(cl,{})}),s.jsx(Le,{path:"/leaderboard",element:s.jsx(hl,{})}),s.jsx(Le,{path:"/trace-recorder",element:s.jsx(jl,{})}),s.jsx(Le,{path:"/background-test",element:s.jsx(vl,{})}),s.jsx(Le,{path:"*",element:s.jsx("div",{className:"min-h-screen pt-8",style:{background:"transparent"},children:s.jsx("div",{className:"max-w-4xl mx-auto px-4 py-8",children:s.jsx(re.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:s.jsxs(ce,{className:"text-center p-12 shadow-soft",children:[s.jsxs("div",{className:"mb-8",children:[s.jsx("div",{className:"w-24 h-24 bg-gradient-to-br from-gray-400 to-gray-600 rounded-full flex items-center justify-center mx-auto mb-6",children:s.jsx("span",{className:"text-4xl text-white",children:"🔍"})}),s.jsx(kl,{level:1,className:"text-gray-600 mb-4",children:"页面建设中"}),s.jsxs(Cl,{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:["您访问的页面正在建设中，敬请期待！",s.jsx("br",{}),"我们正在努力为您提供更好的功能体验。"]})]}),s.jsx(me,{size:"large",children:s.jsx(H,{size:"large",icon:s.jsx(Fe,{}),onClick:()=>n("/"),className:"h-12 px-8 text-lg",children:"返回首页"})})]})})})})})]})})})})})}),s.jsx(Nl,{}),s.jsx(Sl,{visible:e,onClose:a,onEnterFullscreen:r})]})},Tl={token:{colorPrimary:"#8b5cf6",colorSuccess:"#10b981",colorWarning:"#f59e0b",colorError:"#ef4444",borderRadius:8,fontSize:16,fontFamily:"Inter, system-ui, sans-serif"},components:{Button:{fontSize:18,paddingContentHorizontal:24,paddingContentVertical:12},Input:{fontSize:16,paddingBlock:12},Card:{borderRadius:12}}};gs.createRoot(document.getElementById("root")).render(s.jsx(Be.StrictMode,{children:s.jsx(Ha,{children:s.jsx(zr,{locale:uo,theme:Tl,componentSize:"large",children:s.jsx(Pl,{})})})}));
//# sourceMappingURL=index-CL2RZXpO.js.map
