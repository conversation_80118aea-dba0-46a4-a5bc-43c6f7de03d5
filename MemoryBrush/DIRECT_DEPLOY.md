# 🚀 MemoryBrush 直接部署指南

## 📋 服务器要求
- **操作系统**: Ubuntu 20.04+ / CentOS 7+ / Debian 10+
- **内存**: 最少 1GB，推荐 2GB+
- **存储**: 最少 5GB 可用空间
- **网络**: 开放端口 80 (HTTP重定向), 443 (HTTPS) 和 8000 (后端API)
- **域名**: 需要域名 `www.mb.com` 解析到服务器IP `*******`

## 🔧 环境准备

### 1. 安装 Node.js (前端)
```bash
# 安装 Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

### 2. 安装 Python (后端)
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y python3 python3-pip python3-venv

# CentOS/RHEL
sudo yum install -y python3 python3-pip

# 验证安装
python3 --version
pip3 --version
```

### 3. 安装 Caddy (Web服务器)
```bash
# Ubuntu/Debian
sudo apt install -y debian-keyring debian-archive-keyring apt-transport-https
curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/gpg.key' | sudo gpg --dearmor -o /usr/share/keyrings/caddy-stable-archive-keyring.gpg
curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/debian.deb.txt' | sudo tee /etc/apt/sources.list.d/caddy-stable.list
sudo apt update
sudo apt install -y caddy

# CentOS/RHEL
sudo yum install -y yum-plugin-copr
sudo yum copr enable @caddy/caddy
sudo yum install -y caddy

# 启动并设置开机自启
sudo systemctl start caddy
sudo systemctl enable caddy
```

### 4. 安装系统依赖
```bash
# Ubuntu/Debian
sudo apt install -y build-essential libopencv-dev python3-opencv

# CentOS/RHEL
sudo yum groupinstall -y "Development Tools"
sudo yum install -y opencv-devel python3-opencv
```

## 📁 项目部署

### 1. 上传项目文件
```bash
# 创建项目目录
sudo mkdir -p /var/www/MemoryDoodle
sudo chown $USER:$USER /var/www/MemoryDoodle

# 上传项目文件到 /var/www/MemoryDoodle/
# 或使用git克隆
cd /var/www/MemoryDoodle
git clone your-repository-url .
```

### 2. 部署后端

#### 创建Python虚拟环境
```bash
cd /var/www/MemoryDoodle/backend
python3 -m venv venv
source venv/bin/activate
```

#### 安装Python依赖
```bash
pip install -r requirements.txt
```

#### 配置环境变量
```bash
# 创建环境配置文件
cat > .env << EOF
DEBUG=False
SECRET_KEY=B88C05D8-8ACC-453D-8A41-238FB135B5A0
DATABASE_URL=sqlite:///./memory_brush.db
ALLOWED_HOSTS=http://***********,http://dm.bjxdql.com
EOF
```

#### 创建systemd服务
```bash
sudo tee /etc/systemd/system/memory-brush-backend.service > /dev/null << EOF
[Unit]
Description=Memory Brush Backend
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=/var/www/MemoryDoodle/backend
Environment=PATH=/var/www/MemoryDoodle/backend/venv/bin
ExecStart=/var/www/MemoryDoodle/backend/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8000
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF
```

#### 启动后端服务
```bash
sudo systemctl daemon-reload
sudo systemctl status memory-brush-backend
sudo systemctl start memory-brush-backend
sudo systemctl enable memory-brush-backend

# 检查状态
sudo systemctl status memory-brush-backend
```

### 3. 部署前端

#### 安装依赖并构建
```bash
cd /var/www/MemoryDoodle/frontend

# 配置生产环境变量
cat > .env.production << EOF
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_APP_TITLE=记忆画笔
VITE_APP_VERSION=1.0.0
EOF

# 安装依赖
npm install

# 构建生产版本
npm run build
```

#### 配置Caddy (自动HTTPS)
```bash
sudo tee /etc/caddy/Caddyfile > /dev/null << EOF
# MemoryBrush 配置 - 临时HTTP配置（调试用）
# 注意：生产环境应使用HTTPS

# 临时HTTP配置 - 用于调试SSL问题
www.mb.com {
    # 静态文件服务
    root * /var/www/MemoryDoodle/frontend/dist

    # API代理
    reverse_proxy /api/* 127.0.0.1:8000

    # 处理SPA路由
    try_files {path} /index.html

    # 静态资源缓存
    @static {
        path *.js *.css *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2 *.ttf *.eot
    }
    header @static Cache-Control "public, max-age=31536000, immutable"

    # 基础安全头
    header {
        X-Frame-Options "SAMEORIGIN"
        X-XSS-Protection "1; mode=block"
        X-Content-Type-Options "nosniff"
        X-Robots-Tag "noindex, nofollow"
    }

    # 启用文件服务
    file_server

    # 访问日志
    log {
        output file /var/log/caddy/memory-brush.log {
            roll_size 100mb
            roll_keep 5
            roll_keep_for 720h
        }
        format json
    }
}

# 备用IP访问 (重定向到域名)
******* {
    redir http://www.mb.com{uri} permanent
}

# ============================================
# 正式HTTPS配置（DNS解析正常后启用）
# ============================================
# www.mb.com {
#     # 静态文件服务
#     root * /var/www/MemoryDoodle/frontend/dist
#
#     # API代理
#     reverse_proxy /api/* 127.0.0.1:8000
#
#     # 处理SPA路由
#     try_files {path} /index.html
#
#     # 静态资源缓存
#     @static {
#         path *.js *.css *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2 *.ttf *.eot
#     }
#     header @static Cache-Control "public, max-age=31536000, immutable"
#
#     # 安全头 (HTTPS增强)
#     header {
#         X-Frame-Options "SAMEORIGIN"
#         X-XSS-Protection "1; mode=block"
#         X-Content-Type-Options "nosniff"
#         Referrer-Policy "strict-origin-when-cross-origin"
#         Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
#         X-Robots-Tag "noindex, nofollow"
#     }
#
#     # 启用文件服务
#     file_server
#
#     # 访问日志
#     log {
#         output file /var/log/caddy/memory-brush.log {
#             roll_size 100mb
#             roll_keep 5
#             roll_keep_for 720h
#         }
#         format json
#     }
#
#     # TLS配置 (自动HTTPS)
#     tls {
#         protocols tls1.2 tls1.3
#     }
# }
#
# # HTTP重定向到HTTPS
# http://www.mb.com {
#     redir https://{host}{uri} permanent
# }
EOF

# 创建日志目录
sudo mkdir -p /var/log/caddy
sudo chown caddy:caddy /var/log/caddy

# 验证配置
sudo caddy validate --config /etc/caddy/Caddyfile

# 重启Caddy
sudo systemctl restart caddy
```

## 🚀 一键部署脚本

创建自动化部署脚本：
```bash
cat > /var/www/MemoryDoodle/deploy-direct.sh << 'EOF'
#!/bin/bash

set -e

echo "🚀 开始直接部署 MemoryBrush..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 获取服务器IP
SERVER_IP=$(hostname -I | awk '{print $1}')
echo -e "${YELLOW}📍 服务器IP: ${SERVER_IP}${NC}"

# 部署后端
echo -e "${YELLOW}🔧 部署后端...${NC}"
cd /var/www/MemoryDoodle/backend

# 激活虚拟环境
source venv/bin/activate

# 更新依赖
pip install -r requirements.txt

# 重启后端服务
sudo systemctl restart memory-brush-backend

# 部署前端
echo -e "${YELLOW}🎨 部署前端...${NC}"
cd /var/www/MemoryDoodle/frontend

# 更新环境变量
sed -i "s/your-server-ip/${SERVER_IP}/g" .env.production

# 安装依赖并构建
npm install
npm run build

# 重启Nginx
sudo systemctl restart nginx

# 检查服务状态
echo -e "${YELLOW}🔍 检查服务状态...${NC}"

if systemctl is-active --quiet memory-brush-backend; then
    echo -e "${GREEN}✅ 后端服务正常${NC}"
else
    echo -e "${RED}❌ 后端服务异常${NC}"
    sudo systemctl status memory-brush-backend
fi

if systemctl is-active --quiet nginx; then
    echo -e "${GREEN}✅ Nginx服务正常${NC}"
else
    echo -e "${RED}❌ Nginx服务异常${NC}"
    sudo systemctl status nginx
fi

# 检查端口
if curl -f http://localhost:80 > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 前端访问正常${NC}"
else
    echo -e "${RED}❌ 前端访问异常${NC}"
fi

if curl -f http://localhost:8000/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 后端API正常${NC}"
else
    echo -e "${RED}❌ 后端API异常${NC}"
fi

echo -e "${GREEN}🎉 部署完成！${NC}"
echo -e "${GREEN}📱 访问地址: http://${SERVER_IP}${NC}"
EOF

chmod +x /var/www/MemoryDoodle/deploy-direct.sh
```

## 🔍 验证部署

### 检查服务状态
```bash
# 检查后端服务
sudo systemctl status memory-brush-backend

# 检查Caddy
sudo systemctl status caddy

# 检查端口监听
sudo netstat -tlnp | grep :80
sudo netstat -tlnp | grep :8000
```

### 测试访问
```bash
# 测试前端
curl http://localhost:80

# 测试后端API
curl http://localhost:8000/health
```

## 🛠️ 常用运维命令

### 服务管理
```bash
# 重启后端
sudo systemctl restart memory-brush-backend

# 重启前端(Caddy)
sudo systemctl restart caddy

# 查看后端日志
sudo journalctl -u memory-brush-backend -f

# 查看Caddy日志
sudo journalctl -u caddy -f
sudo tail -f /var/log/caddy/memory-brush.log
```

### 更新应用
```bash
# 拉取最新代码
cd /var/www/MemoryDoodle
git pull

# 运行部署脚本
./deploy-direct.sh
```

### 备份数据
```bash
# 备份数据库
cp /var/www/MemoryDoodle/backend/memory_brush.db /backup/

# 备份上传文件
tar -czf /backup/uploads_$(date +%Y%m%d).tar.gz /var/www/MemoryDoodle/backend/uploads/
```

## 🔒 安全配置

### 防火墙设置
```bash
# Ubuntu/Debian
sudo ufw allow 80
sudo ufw allow 8000
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=8000/tcp
sudo firewall-cmd --reload
```

### 文件权限
```bash
# 设置正确的文件权限（Caddy以caddy用户运行）
sudo chown -R $USER:caddy /var/www/MemoryDoodle
sudo chmod -R 755 /var/www/MemoryDoodle

# 确保Caddy可以读取前端文件
sudo chown -R $USER:caddy /var/www/MemoryDoodle/frontend/dist
sudo chmod -R 755 /var/www/MemoryDoodle/frontend/dist

# 设置后端权限
sudo chown -R $USER:$USER /var/www/MemoryDoodle/backend
sudo chmod -R 775 /var/www/MemoryDoodle/backend/uploads

# 设置Caddy配置文件权限
sudo chown caddy:caddy /etc/caddy/Caddyfile
sudo chmod 644 /etc/caddy/Caddyfile

# 设置日志目录权限
sudo chown -R caddy:caddy /var/log/caddy
```

## 🐛 故障排除

### 常见问题

1. **后端服务启动失败**
```bash
# 查看详细错误
sudo journalctl -u memory-brush-backend -n 50

# 手动启动测试
cd /var/www/MemoryDoodle/backend
source venv/bin/activate
uvicorn main:app --host 0.0.0.0 --port 8000
```

2. **前端构建失败**
```bash
# 清理缓存重新构建
cd /var/www/MemoryDoodle/frontend
rm -rf node_modules package-lock.json
npm install
npm run build
```

3. **Nginx配置错误**
```bash
# 测试配置
sudo nginx -t

# 查看错误日志
sudo tail -f /var/log/nginx/error.log
```

4. **端口被占用**
```bash
# 查看端口占用
sudo netstat -tlnp | grep :8000
sudo lsof -i :8000

# 停止占用进程
sudo kill -9 PID
```

## 📞 快速命令参考

```bash
# 一键部署
./deploy-direct.sh

# 查看所有服务状态
sudo systemctl status memory-brush-backend nginx

# 重启所有服务
sudo systemctl restart memory-brush-backend nginx

# 查看实时日志
sudo journalctl -u memory-brush-backend -f
```

这种直接部署方式资源占用更少，适合小型服务器使用！
EOF
