# 🧪 MemoryBrush 测试模式使用指南

## 📋 功能说明

测试模式是为了方便开发和测试而添加的功能，可以让您无需逐关通过就能访问所有关卡。

## 🚀 如何启用测试模式

### 方法1: 通过UI界面
1. 启动应用并进入游戏主页
2. 在级别选择页面，向下滚动找到"开发者选项"区域
3. 找到"测试模式"开关，点击开启
4. 看到"🧪 测试模式已开启 - 所有关卡已解锁"提示
5. 现在所有级别和关卡都已解锁，可以自由访问

### 方法2: 通过浏览器控制台
```javascript
// 在浏览器控制台中执行
localStorage.setItem('memorybrush-test-mode', 'true')
// 然后刷新页面
location.reload()
```

## 🎮 测试模式功能

### ✅ 已解锁的内容
- **所有级别**: level-1, level-2, level-3, level-4
- **所有关卡**:
  - 级别1 (线条启蒙): 自由画线、描线练习、直线图形、曲线图形
  - 级别2 (立体空间): 立体图形、色彩填充、质感画笔
  - 级别3 (画面构图): 抽象艺术、风景艺术、肖像艺术
  - 级别4 (智能创作): AI辅助、风格转换、创意模式

### 🔄 状态管理
- 测试模式状态保存在 `localStorage` 中
- 切换测试模式不会影响正常的游戏进度
- 关闭测试模式后会恢复到实际的游戏进度

## 🛠️ 开发者选项

在级别选择页面的底部，您可以找到开发者选项区域，包含：

1. **测试模式开关**
   - 快速开启/关闭测试模式
   - 实时显示当前状态

2. **重置进度按钮**
   - 清除所有游戏进度
   - 恢复到初始状态（只解锁第一关）

## 📝 使用场景

### 🧪 开发测试
- 测试新功能时无需重复通关
- 快速验证各个关卡的功能
- 检查UI在不同关卡下的表现

### 🎨 内容创作
- 快速访问高级关卡进行演示
- 制作教程或截图时的便利工具
- 体验完整的游戏内容

### 🐛 问题调试
- 重现特定关卡的问题
- 测试关卡切换逻辑
- 验证数据保存和加载

## ⚠️ 注意事项

1. **仅用于测试**: 测试模式仅用于开发和测试目的
2. **不影响正常进度**: 开启测试模式不会修改您的实际游戏进度
3. **临时状态**: 测试模式的解锁状态是临时的，关闭后恢复正常
4. **数据安全**: 您的正常游戏进度始终保存在localStorage中

## 🔧 技术实现

### 状态管理
```typescript
// 测试模式状态
const [testMode, setTestMode] = useState<boolean>(() => {
  const saved = localStorage.getItem('memorybrush-test-mode')
  return saved === 'true'
})

// 解锁状态根据测试模式动态调整
useEffect(() => {
  if (testMode) {
    // 解锁所有关卡
    setUnlockedLevels(['level-1', 'level-2', 'level-3', 'level-4'])
    setUnlockedStages([...allStages])
  } else {
    // 恢复正常进度
    const savedProgress = localStorage.getItem('memorybrush-unlocked-levels')
    setUnlockedLevels(savedProgress ? JSON.parse(savedProgress) : ['level-1'])
  }
}, [testMode])
```

### 数据持久化
- 测试模式状态: `localStorage.getItem('memorybrush-test-mode')`
- 正常游戏进度: `localStorage.getItem('memorybrush-unlocked-levels')`
- 关卡解锁状态: `localStorage.getItem('memorybrush-unlocked-stages')`

## 🚀 快速开始

1. **启动应用**:
   ```bash
   cd MemoryBrush/frontend
   npm run dev
   ```

2. **访问游戏**: http://localhost:5173

3. **开启测试模式**: 在级别选择页面找到开发者选项，开启测试模式

4. **开始测试**: 现在可以自由访问所有关卡进行测试

## 🔄 恢复正常模式

当测试完成后，记得关闭测试模式：

1. 在开发者选项中关闭测试模式开关
2. 或在控制台执行: `localStorage.removeItem('memorybrush-test-mode')`
3. 刷新页面恢复正常的游戏进度

---

🎉 **现在您可以自由地测试所有关卡功能了！**
