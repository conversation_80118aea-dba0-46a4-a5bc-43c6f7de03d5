# 🎨 Level 1 背景图交互功能

## 📋 功能说明

为 Level 1 (线条启蒙) 实现了特殊的背景图交互功能，使用 `background-l1.jpg` 作为背景，用户可以直接点击背景图中的数字圆圈来进入对应关卡。

## ✨ 功能特点

### 🖼️ 背景图显示
- Level 1 的关卡选择页面使用 `/background-l1.jpg` 作为全屏背景
- 背景图保持原始比例，不会变形拉伸
- 高度适配视口，宽度自适应，空白区域用背景色填充

### 🎯 可点击热区
- 在背景图上放置了 5 个可点击的圆形热区
- 每个热区对应一个关卡：L1-1, L1-2, L1-3, L1-4, L1-5
- 热区位置可以通过配置调整

### 🔒 解锁状态显示
- **已解锁关卡**: 蓝色渐变圆圈，显示数字 (1, 2, 3, 4, 5)
- **未解锁关卡**: 灰色圆圈，显示锁定图标
- 鼠标悬停时有缩放动画效果

### 💡 用户界面
- **返回按钮**: 左上角，返回级别选择页面
- **关卡标题**: 每个热区下方显示关卡名称
- **简洁设计**: 移除了级别标题和操作提示，保持界面简洁

## 🛠️ 技术实现

### 组件结构
```typescript
// StageSelector.tsx 中的条件渲染
if (level.id === 'level-1') {
    return <Level1StageSelector ... />;
}
// 其他级别使用原有的卡片布局
```

### 热区配置
```typescript
const stageHotspots = [
    { id: 'L1-1', x: '15%', y: '25%', number: 1 },
    { id: 'L1-2', x: '30%', y: '40%', number: 2 },
    { id: 'L1-3', x: '50%', y: '30%', number: 3 },
    { id: 'L1-4', x: '70%', y: '45%', number: 4 },
    { id: 'L1-5', x: '85%', y: '35%', number: 5 },
];
```

### 响应式设计
- 使用百分比定位，适配不同屏幕尺寸
- 背景图保持比例不变形 (`background-size: auto 100vh`)
- 高度适配视口，宽度自适应，空白区域填充背景色
- 热区使用绝对定位，相对于背景图位置

## 🔧 调试功能

### 开发模式
在 URL 中添加 `?debug=true` 参数可以启用调试模式：
```
http://localhost:5173/game/level-1?debug=true
```

调试模式下会显示：
- 热区边界（红色边框）
- 热区坐标信息
- 便于调整热区位置

### 位置调整
如果热区位置不准确，可以修改 `stageHotspots` 配置：
```typescript
// 在 StageSelector.tsx 中调整这些值
{ id: 'L1-1', x: '15%', y: '25%', number: 1 }, // 调整 x, y 值
```

## 📱 用户体验

### 交互流程
1. 用户进入 Level 1 关卡选择页面
2. 看到 `background-l1.jpg` 背景图
3. 点击背景图中的数字圆圈
4. 直接进入对应关卡开始游戏

### 视觉反馈
- **悬停效果**: 圆圈放大 1.1 倍
- **点击效果**: 圆圈缩小到 0.95 倍
- **解锁状态**: 颜色和图标区分
- **动画效果**: 圆圈依次出现，有延迟动画

### 提示信息
- 鼠标悬停显示关卡标题和状态
- 右下角有操作说明卡片
- 未解锁关卡显示解锁条件

## 🎮 与其他级别的区别

### Level 1 (特殊)
- 使用背景图交互
- 圆形热区点击
- 沉浸式体验

### Level 2-4 (标准)
- 使用卡片布局
- 传统的关卡选择界面
- 保持原有设计

## 🔄 兼容性

### 向后兼容
- 不影响其他级别的功能
- 保持原有的解锁逻辑
- 测试模式正常工作

### 扩展性
- 可以为其他级别添加类似功能
- 热区配置易于修改
- 支持不同的背景图

## 📝 使用说明

### 正常使用
1. 启动应用: `npm run dev`
2. 进入游戏页面
3. 选择 Level 1 (线条启蒙)
4. 点击背景图中的数字圆圈

### 调试模式
1. 在 URL 添加 `?debug=true`
2. 查看热区位置信息
3. 根据实际背景图调整坐标
4. 移除调试参数恢复正常模式

## 🎯 后续优化

### 可能的改进
1. **自适应热区**: 根据背景图自动检测圆圈位置
2. **动态加载**: 支持不同分辨率的背景图
3. **音效反馈**: 点击时播放音效
4. **进度指示**: 显示关卡完成进度

### 性能优化
1. **图片预加载**: 提前加载背景图
2. **懒加载**: 按需加载关卡资源
3. **缓存策略**: 缓存背景图和热区配置

---

🎉 **Level 1 背景图交互功能已完成！用户现在可以通过点击背景图中的数字圆圈直接进入对应关卡。**
