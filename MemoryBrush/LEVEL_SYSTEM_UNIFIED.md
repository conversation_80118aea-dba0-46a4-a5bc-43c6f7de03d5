# 🎯 MemoryBrush 关卡系统统一化

## 📋 统一前的问题

之前的关卡系统存在两套不同的命名格式，导致代码混乱：

1. **LevelSelector 中的关卡**: `free-lines`, `trace-lines`, `straight-shapes`, `curved-shapes` 等
2. **GamePage 中的关卡**: `L1-1`, `L1-2`, `L1-3`, `L1-4` 等

这种不一致导致：
- 代码维护困难
- 关卡解锁逻辑复杂
- 新功能开发时容易出错
- 测试模式配置复杂

## ✅ 统一后的解决方案

### 🎯 统一命名格式
所有关卡都使用 **`Lx-y`** 格式：
- `x`: 级别号 (1, 2, 3, 4)
- `y`: 关卡号 (1, 2, 3, 4, 5, 6...)

### 📁 新的文件结构

#### 1. 统一配置文件
**`frontend/src/config/levels.ts`** - 所有关卡的统一配置
```typescript
export const LEVELS: Level[] = [
  {
    id: 'level-1',
    title: '线条启蒙',
    stages: [
      { id: 'L1-1', title: '自由画线', type: 'free-draw' },
      { id: 'L1-2', title: '匀速直线', type: 'image-trace' },
      { id: 'L1-3', title: '匀速线条组合', type: 'image-trace' },
      { id: 'L1-4', title: '曲线消除', type: 'image-trace' },
      { id: 'L1-5', title: '直线图形', type: 'shape-straight' },
      { id: 'L1-6', title: '曲线图形', type: 'shape-curved' }
    ]
  },
  // ... 其他级别
]
```

#### 2. 更新的测试模式配置
**`frontend/src/config/testMode.ts`** - 自动从统一配置获取关卡列表
```typescript
import { getAllStageIds, getAllLevelIds } from './levels'

export const TEST_MODE_CONFIG = {
  enabled: true,
  get allLevels() { return getAllLevelIds() },
  get allStages() { return getAllStageIds() }
}
```

### 🔄 迁移的组件

#### 1. LevelSelector 组件
- 移除内部关卡定义
- 使用统一的 `LEVELS` 配置
- 自动添加图标映射

#### 2. GamePage 组件  
- 移除重复的关卡定义
- 直接使用 `LEVELS` 配置
- 简化关卡解锁逻辑

#### 3. StageSelector 组件
- 使用统一的类型定义
- 支持新的关卡格式

## 🎮 新的关卡结构

### Level 1 - 线条启蒙
- **L1-1**: 自由画线 (free-draw)
- **L1-2**: 匀速直线 (image-trace)
- **L1-3**: 匀速线条组合 (image-trace)
- **L1-4**: 曲线消除 (image-trace)
- **L1-5**: 直线图形 (shape-straight)
- **L1-6**: 曲线图形 (shape-curved)

### Level 2 - 立体空间
- **L2-1**: 立体图形 (coming-soon)
- **L2-2**: 色彩填充 (coming-soon)
- **L2-3**: 质感画笔 (coming-soon)
- **L2-4**: 阴影效果 (coming-soon)

### Level 3 - 画面构图
- **L3-1**: 抽象艺术 (coming-soon)
- **L3-2**: 几何静物 (coming-soon)
- **L3-3**: 风景艺术 (coming-soon)
- **L3-4**: 肖像艺术 (coming-soon)

### Level 4 - 智能创作
- **L4-1**: 照片描边 (coming-soon)
- **L4-2**: 风格渲染 (coming-soon)
- **L4-3**: AI协作 (coming-soon)
- **L4-4**: 创意模式 (coming-soon)

## 🛠️ 开发者指南

### 添加新关卡
1. 在 `frontend/src/config/levels.ts` 中添加关卡定义
2. 关卡ID必须遵循 `Lx-y` 格式
3. 测试模式会自动包含新关卡

### 关卡类型
- `free-draw`: 自由绘画
- `image-trace`: 图像描线
- `shape-straight`: 直线图形
- `shape-curved`: 曲线图形
- `coming-soon`: 即将推出

### 获取关卡信息
```typescript
import { getStageById, getLevelById, getLevelByStageId } from '@/config/levels'

// 根据关卡ID获取关卡信息
const stage = getStageById('L1-2')

// 根据级别ID获取级别信息
const level = getLevelById('level-1')

// 根据关卡ID获取所属级别
const level = getLevelByStageId('L1-2')
```

## 📈 统一化的优势

1. **代码简洁**: 单一数据源，避免重复定义
2. **易于维护**: 修改关卡只需更新一个文件
3. **类型安全**: 统一的TypeScript类型定义
4. **扩展性强**: 添加新关卡非常简单
5. **测试友好**: 测试模式自动支持所有关卡
6. **命名一致**: 所有关卡使用统一的命名格式

## 🔄 迁移完成

✅ 统一关卡命名格式为 `Lx-y`
✅ 创建统一的关卡配置文件
✅ 更新所有相关组件
✅ 简化测试模式配置
✅ 更新类型定义
✅ 保持向后兼容性

现在整个关卡系统使用统一的格式，代码更加清晰和易于维护！
